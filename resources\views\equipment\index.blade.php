<x-app-layout>
    <x-slot name="header">
        EQP Management
    </x-slot>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Equipment Breakdown</h4>
                @if(Auth::user()->canManageOrders())
                    <a href="{{ route('equipment.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Equipment
                    </a>
                @endif
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- EQP Filters Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center" 
                     style="cursor: pointer; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;" 
                     onclick="toggleFilters()">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-filter me-2" style="color: #fff; text-shadow: 0 1px 2px rgba(0,0,0,0.2);"></i>
                        <span style="font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.2);">EQP Filters</span>
                        <small class="ms-2" style="color: rgba(255,255,255,0.8);">(Click to collapse)</small>
                    </h6>
                    <i class="fas fa-chevron-up text-white" id="filter-toggle-icon" style="text-shadow: 0 1px 2px rgba(0,0,0,0.2);"></i>
                </div>
                <div class="card-body" id="filter-section" style="display: block; padding: 0.75rem 1rem;">
                    <form method="GET" action="{{ route('equipment.index') }}" id="eqp-filter-form">
                        <div class="row g-2 align-items-end">
                            <div class="col-auto">
                                <label for="eqp_line" class="form-label mb-1"><small>EQP Line</small></label>
                                <select class="form-select form-select-sm" name="eqp_line" id="eqp_line" style="min-width: 110px;">
                                    <option value="all" {{ $filters['eqp_line'] === 'all' ? 'selected' : '' }}>All Lines</option>
                                    @foreach($filterOptions['eqp_lines'] as $line)
                                        <option value="{{ $line }}" {{ $filters['eqp_line'] === $line ? 'selected' : '' }}>{{ $line }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-auto">
                                <label for="lot_size" class="form-label mb-1"><small>EQP Size</small></label>
                                <select class="form-select form-select-sm" name="lot_size" id="lot_size" style="min-width: 110px;">
                                    <option value="all" {{ $filters['lot_size'] === 'all' ? 'selected' : '' }}>All Sizes</option>
                                    @foreach($filterOptions['lot_sizes'] as $size)
                                        <option value="{{ $size }}" {{ $filters['lot_size'] === $size ? 'selected' : '' }}>{{ $size }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-auto">
                                <label for="eqp_maker" class="form-label mb-1"><small>EQP Maker</small></label>
                                <select class="form-select form-select-sm" name="eqp_maker" id="eqp_maker" style="min-width: 120px;">
                                    <option value="all" {{ $filters['eqp_maker'] === 'all' ? 'selected' : '' }}>All Makers</option>
                                    @foreach($filterOptions['eqp_makers'] as $maker)
                                        <option value="{{ $maker }}" {{ $filters['eqp_maker'] === $maker ? 'selected' : '' }}>{{ $maker }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-auto">
                                <label for="eqp_type" class="form-label mb-1"><small>Inspection Type</small></label>
                                <select class="form-select form-select-sm" name="eqp_type" id="eqp_type" style="min-width: 130px;">
                                    <option value="all" {{ $filters['eqp_type'] === 'all' ? 'selected' : '' }}>All Types</option>
                                    @foreach($filterOptions['eqp_types'] as $type)
                                        <option value="{{ $type }}" {{ $filters['eqp_type'] === $type ? 'selected' : '' }}>{{ $type }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-auto">
                                <label for="work_type" class="form-label mb-1"><small>Work Type</small></label>
                                <select class="form-select form-select-sm" name="work_type" id="work_type" style="min-width: 120px;">
                                    <option value="all" {{ $filters['work_type'] === 'all' ? 'selected' : '' }}>All Types</option>
                                    @foreach($filterOptions['work_types'] as $type)
                                        <option value="{{ $type }}" {{ $filters['work_type'] === $type ? 'selected' : '' }}>{{ $type }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-auto">
                                <label for="wildcard_search" class="form-label mb-1"><small>Wildcard Search</small></label>
                                <input type="text" 
                                       class="form-control form-control-sm" 
                                       name="wildcard_search" 
                                       id="wildcard_search" 
                                       value="{{ $filters['wildcard_search'] ?? '' }}"
                                       placeholder="Search EQP No, Line, Code..."
                                       style="min-width: 200px;"
                                       title="Search across EQP No, Line, Code, Area, and Maker using wildcards (* for multiple chars, ? for single char)">
                            </div>
                            <div class="col-auto">
                                <button type="submit" class="btn btn-primary btn-sm">
                                    <i class="fas fa-search me-1"></i>Apply
                                </button>
                            </div>
                            <div class="col-auto">
                                <a href="{{ route('equipment.index') }}" class="btn btn-outline-secondary btn-sm">
                                    <i class="fas fa-undo me-1"></i>Reset
                                </a>
                            </div>
                            <div class="col-auto ms-auto">
                                <div class="d-flex gap-2">
                                    <div class="text-center px-3 py-2 rounded" style="background: linear-gradient(135deg, #28a745, #20c997); color: white; border: 1px solid rgba(255,255,255,0.2); min-width: 120px;">
                                        <div style="font-size: 0.7rem; font-weight: 600; text-transform: uppercase; opacity: 0.9;">
                                            <i class="fas fa-calculator me-1"></i>Equipment
                                        </div>
                                        <div style="font-size: 1.1rem; font-weight: 700;">
                                            {{ number_format($totalEquipmentCount) }}
                                        </div>
                                    </div>
                                    <div class="text-center px-3 py-2 rounded" style="background: linear-gradient(135deg, #007bff, #6610f2); color: white; border: 1px solid rgba(255,255,255,0.2); min-width: 120px;">
                                        <div style="font-size: 0.7rem; font-weight: 600; text-transform: uppercase; opacity: 0.9;">
                                            <i class="fas fa-chart-line me-1"></i>Daily Capa
                                        </div>
                                        <div style="font-size: 1.1rem; font-weight: 700;">
                                            {{ number_format($totalDailyCapa) }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Equipment Summary Section -->
    @if(isset($equipmentSummary))
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header border-bottom d-flex justify-content-between align-items-center" 
                         style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border: none;">
                        <h6 class="mb-0 text-white">
                            <i class="fas fa-cogs me-2" style="color: #fff; text-shadow: 0 1px 2px rgba(0,0,0,0.3);"></i>
                            <span style="font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">Equipment Summary</span>
                            <span class="badge ms-2" style="background: rgba(255,255,255,0.2); color: white; font-weight: 500; border: 1px solid rgba(255,255,255,0.3);">{{ $equipmentSummary->count() }} Groups</span>
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        @if($equipmentSummary->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="equipmentSummaryTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(0)">EQP Number <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(1)">EQP Line <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(2)">EQP Code <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(3)">Inspection Class <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(4)">Alloc Type <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(5)">Alloc Qty <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(6)">Daily Capa <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(7)">Set OEE <i class="fas fa-sort text-muted"></i></th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($equipmentSummary as $equipment)
                                            <tr>
                                                <td data-sort="{{ $equipment->eqp_no }}">
                                                    <span class="badge bg-dark text-white fs-6 px-3 py-2">{{ $equipment->eqp_no }}</span>
                                                </td>
                                                <td data-sort="{{ $equipment->eqp_line }}">
                                                    <span class="badge bg-info">{{ $equipment->eqp_line }}</span>
                                                </td>
                                                <td data-sort="{{ $equipment->eqp_code }}">
                                                    <span class="badge bg-primary text-white fs-6 px-3 py-2">{{ $equipment->eqp_code }}</span>
                                                </td>
                                                <td data-sort="{{ $equipment->eqp_class }}">
                                                    <span class="badge bg-secondary">{{ $equipment->eqp_class ?? 'N/A' }}</span>
                                                </td>
                                                <td data-sort="{{ $equipment->lot_type }}">
                                                    <span class="badge bg-warning text-dark">{{ $equipment->lot_type }}</span>
                                                </td>
                                                <td data-sort="{{ $equipment->lotqty_alloc }}">
                                                    <span class="badge bg-info">{{ $equipment->lotqty_alloc }}</span>
                                                </td>
                                                <td data-sort="{{ $equipment->daily_capa }}">
                                                    <span class="badge bg-primary">{{ number_format($equipment->daily_capa) }}</span>
                                                </td>
                                                <td data-sort="{{ $equipment->average_oee_percent }}">
                                                    <span class="badge bg-success">{{ number_format($equipment->average_oee_percent, 1) }}%</span>
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-info" 
                                                                title="View Details"
                                                                onclick="showEquipmentDetails('{{ $equipment->eqp_line }}', '{{ $equipment->eqp_code }}', { eqp_line: '{{ $filters['eqp_line'] }}', lot_size: '{{ $filters['lot_size'] }}', eqp_maker: '{{ $filters['eqp_maker'] }}', eqp_type: '{{ $filters['eqp_type'] }}', work_type: '{{ $filters['work_type'] }}', wildcard_search: '{{ $filters['wildcard_search'] ?? '' }}' })">
                                                            <i class="fas fa-eye"></i> View Details
                                                        </button>
                                                        @if(Auth::user()->canManageOrders())
                                                            <a href="{{ route('equipment.edit', $equipment->id) }}?{{ http_build_query(array_filter($filters)) }}" class="btn btn-sm btn-outline-warning" title="Edit Equipment">
                                                                <i class="fas fa-edit"></i> Edit
                                                            </a>
                                                            <form method="POST" action="{{ route('equipment.destroy', $equipment->id) }}" style="display: inline;" onsubmit="return confirm('Are you sure you want to delete this equipment?')">
                                                                @csrf
                                                                @method('DELETE')
                                                                <input type="hidden" name="return_filters" value="{{ http_build_query(array_filter($filters)) }}">
                                                                <button type="submit" class="btn btn-sm btn-outline-danger" title="Delete Equipment">
                                                                    <i class="fas fa-trash"></i> Delete
                                                                </button>
                                                            </form>
                                                        @endif
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                            @if($equipmentSummary->hasPages())
                                <div class="d-flex justify-content-between align-items-center px-3 py-2 bg-light border-top">
                                    <div class="small text-muted">
                                        Showing {{ $equipmentSummary->firstItem() }} to {{ $equipmentSummary->lastItem() }} of {{ $equipmentSummary->total() }} results
                                    </div>
                                    <div>
                                        {{ $equipmentSummary->links('pagination::bootstrap-4') }}
                                    </div>
                                </div>
                            @endif
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-cogs fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No Equipment Data Found</h5>
                                <p class="text-muted">No records match the current filter criteria.</p>
                                <div class="d-flex justify-content-center gap-2">
                                    <a href="{{ route('equipment.index') }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-undo me-1"></i>Reset Filters
                                    </a>
                                    @if(Auth::user()->canManageOrders())
                                        <a href="{{ route('equipment.create') }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-plus me-1"></i>Add Equipment
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Equipment Details Modal -->
    <div class="modal fade" id="equipmentDetailsModal" tabindex="-1" aria-labelledby="equipmentDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" style="max-width: 90%; width: 1200px;">
            <div class="modal-content" style="height: 80vh; display: flex; flex-direction: column;">
                <div class="modal-header">
                    <h5 class="modal-title" id="equipmentDetailsModalLabel">
                        <i class="fas fa-cogs me-2"></i>Equipment Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="flex: 1; overflow-y: auto; padding: 1.5rem;">
                    <div id="equipmentDetailsContent">
                        <!-- Details will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleFilters() {
            const filterSection = document.getElementById('filter-section');
            const toggleIcon = document.getElementById('filter-toggle-icon');
            
            if (filterSection.style.display === 'none') {
                filterSection.style.display = 'block';
                toggleIcon.className = 'fas fa-chevron-up';
            } else {
                filterSection.style.display = 'none';
                toggleIcon.className = 'fas fa-chevron-down';
            }
        }
        
        function showEquipmentDetails(eqpLine, eqpCode, filters = {}) {
            const modal = new bootstrap.Modal(document.getElementById('equipmentDetailsModal'));
            const content = document.getElementById('equipmentDetailsContent');
            
            // Show loading state
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading equipment details...</p>
                </div>
            `;
            
            modal.show();
            
            // Build parameters including filters
            const params = {
                eqp_line: eqpLine,
                eqp_code: eqpCode,
                ...filters
            };
            
            // Fetch actual equipment details via AJAX
            fetch('/equipment-group-details?' + new URLSearchParams(params))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    content.innerHTML = generateEquipmentDetailsHtml(data.data, data.group_info);
                } else {
                    content.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error loading equipment details. Please try again.
                        </div>
                    `;
                }
            })
            .catch(error => {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error: ${error.message}
                    </div>
                `;
            });
        }
        
        function generateEquipmentDetailsHtml(equipment, groupInfo) {
            // Check if current user can manage orders (ADMIN or MANAGER)
            const canManageOrders = {{ Auth::user()->canManageOrders() ? 'true' : 'false' }};
            let html = `
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center p-3 rounded" 
                             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <div>
                                <h6 class="mb-1 text-white">Group Summary</h6>
                                <div class="d-flex gap-3">
                                    <span class="badge bg-white text-primary">${groupInfo.total_equipment} Equipment</span>
                                    <span class="badge bg-white text-success">${parseInt(groupInfo.total_alloc_qty).toLocaleString()} Total Alloc</span>
                                    <span class="badge bg-white text-info">${parseFloat(groupInfo.average_oee).toFixed(4)} Avg OEE</span>
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="text-white small">
                                    <div><span class="badge bg-info">${groupInfo.eqp_line}</span> <code style="background: rgba(255,255,255,0.2); color: white;">${groupInfo.eqp_code}</code></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover table-sm" id="equipmentDetailsTable">
                        <thead class="table-dark">
                            <tr>
                                <th width="60"><i class="fas fa-list-ol me-1"></i>#</th>
                                <th style="cursor: pointer;" onclick="sortTable(1)"><i class="fas fa-hashtag me-1"></i>EQP No <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(2)"><i class="fas fa-map-marker-alt me-1"></i>Area <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(3)"><i class="fas fa-tag me-1"></i>Type <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(4)"><i class="fas fa-industry me-1"></i>Maker <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(5)"><i class="fas fa-box me-1"></i>Alloc Qty <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(6)"><i class="fas fa-tachometer-alt me-1"></i>OEE <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(7)"><i class="fas fa-clock me-1"></i>Op Time <i class="fas fa-sort text-muted"></i></th>
                                <th width="100"><i class="fas fa-cog me-1"></i>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            equipment.forEach((eqp, index) => {
                const rowClass = index % 2 === 0 ? '' : 'table-light';
                const oeeValue = parseFloat(eqp.eqp_oee) * parseFloat(eqp.eqp_speed) * parseFloat(eqp.operation_time);
                const rowNumber = index + 1;
                
                html += `
                    <tr class="${rowClass}">
                        <td class="text-center"><span class="badge bg-light text-dark">${rowNumber}</span></td>
                        <td data-sort="${eqp.eqp_no}"><strong>${eqp.eqp_no}</strong></td>
                        <td data-sort="${eqp.eqp_area}"><span class="badge bg-secondary">${eqp.eqp_area || 'N/A'}</span></td>
                        <td data-sort="${eqp.eqp_type}"><span class="badge bg-warning text-dark">${eqp.eqp_type}</span></td>
                        <td data-sort="${eqp.eqp_maker}"><span class="badge bg-primary">${eqp.eqp_maker}</span></td>
                        <td data-sort="${eqp.lotqty_alloc}"><span class="badge bg-info">${eqp.lotqty_alloc}</span></td>
                        <td data-sort="${oeeValue}"><span class="badge bg-success">${oeeValue.toFixed(4)}</span></td>
                        <td data-sort="${parseFloat(eqp.operation_time)}"><span class="badge bg-dark">${parseFloat(eqp.operation_time).toFixed(2)}h</span></td>
                        <td class="text-center">
                            <div class="btn-group" role="group">
                                <a href="/equipment/${eqp.id}" class="btn btn-sm btn-outline-info" title="View Equipment">
                                    <i class="fas fa-eye"></i>
                                </a>
                                ${canManageOrders ? `<a href="/equipment/${eqp.id}/edit" class="btn btn-sm btn-outline-warning" title="Edit Equipment">
                                    <i class="fas fa-edit"></i>
                                </a>` : ''}
                            </div>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
                
                ${equipment.length === 0 ? `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No equipment found for this group.</p>
                    </div>
                ` : `
                    <div class="mt-3 p-3 bg-light rounded">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Showing ${equipment.length} equipment unit(s) for this group.
                        </small>
                    </div>
                `}
            `;
            
            return html;
        }
        
        // Summary table sorting variables
        let currentSummarySortColumn = -1;
        let summarySortDirection = 'asc';
        
        // Modal table sorting variables
        let currentSortColumn = -1;
        let sortDirection = 'asc';
        
        function sortSummaryTable(columnIndex) {
            const table = document.getElementById('equipmentSummaryTable');
            if (!table) return;
            
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = Array.from(tbody.getElementsByTagName('tr'));
            
            // Determine sort direction
            if (currentSummarySortColumn === columnIndex) {
                summarySortDirection = summarySortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                summarySortDirection = 'asc';
                currentSummarySortColumn = columnIndex;
            }
            
            // Update header icons
            const headers = table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                const sortIcon = header.querySelector('i.fa-sort, i.fa-sort-up, i.fa-sort-down');
                if (sortIcon && index !== 8) { // Skip Actions column (now at index 8)
                    if (index === columnIndex) {
                        sortIcon.className = summarySortDirection === 'asc' ? 'fas fa-sort-up text-success' : 'fas fa-sort-down text-success';
                    } else {
                        sortIcon.className = 'fas fa-sort text-muted';
                    }
                }
            });
            
            // Sort rows
            rows.sort((a, b) => {
                const cellA = a.children[columnIndex];
                const cellB = b.children[columnIndex];
                
                let valueA, valueB;
                
                // Get sort value from data-sort attribute
                if (cellA.hasAttribute('data-sort')) {
                    valueA = cellA.getAttribute('data-sort');
                    valueB = cellB.getAttribute('data-sort');
                    
                    // Handle numeric values for specific columns
                    if (columnIndex >= 2 && columnIndex <= 4) { // Alloc Qty, EQP Count, Average OEE
                        valueA = parseFloat(valueA.replace(/,/g, '')) || 0;
                        valueB = parseFloat(valueB.replace(/,/g, '')) || 0;
                    }
                } else {
                    valueA = cellA.textContent.trim();
                    valueB = cellB.textContent.trim();
                }
                
                let comparison = 0;
                if (typeof valueA === 'number' && typeof valueB === 'number') {
                    comparison = valueA - valueB;
                } else {
                    comparison = valueA.toString().localeCompare(valueB.toString());
                }
                
                return summarySortDirection === 'asc' ? comparison : -comparison;
            });
            
            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
        }
        
        function sortTable(columnIndex) {
            const table = document.getElementById('equipmentDetailsTable');
            if (!table) return;
            
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = Array.from(tbody.getElementsByTagName('tr'));
            
            // Determine sort direction
            if (currentSortColumn === columnIndex) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortDirection = 'asc';
                currentSortColumn = columnIndex;
            }
            
            // Update header icons
            const headers = table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                const sortIcon = header.querySelector('i.fa-sort, i.fa-sort-up, i.fa-sort-down');
                if (sortIcon && index !== 0 && index !== 8) { // Skip # and Actions columns
                    if (index === columnIndex) {
                        sortIcon.className = sortDirection === 'asc' ? 'fas fa-sort-up text-warning' : 'fas fa-sort-down text-warning';
                    } else {
                        sortIcon.className = 'fas fa-sort text-muted';
                    }
                }
            });
            
            // Sort rows
            rows.sort((a, b) => {
                const cellA = a.children[columnIndex];
                const cellB = b.children[columnIndex];
                
                let valueA, valueB;
                
                // Get sort value from data-sort attribute or text content
                if (cellA.hasAttribute('data-sort')) {
                    valueA = cellA.getAttribute('data-sort');
                    valueB = cellB.getAttribute('data-sort');
                    
                    // Handle numeric values
                    if (columnIndex >= 5 && columnIndex <= 7) { // Alloc Qty, OEE, Op Time columns
                        valueA = parseFloat(valueA) || 0;
                        valueB = parseFloat(valueB) || 0;
                    }
                } else {
                    valueA = cellA.textContent.trim();
                    valueB = cellB.textContent.trim();
                }
                
                let comparison = 0;
                if (typeof valueA === 'number' && typeof valueB === 'number') {
                    comparison = valueA - valueB;
                } else {
                    comparison = valueA.toString().localeCompare(valueB.toString());
                }
                
                return sortDirection === 'asc' ? comparison : -comparison;
            });
            
            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
            
            // Update row numbers
            rows.forEach((row, index) => {
                const numberCell = row.children[0];
                numberCell.innerHTML = `<span class="badge bg-light text-dark">${index + 1}</span>`;
                
                // Update alternating row classes
                row.className = index % 2 === 0 ? '' : 'table-light';
            });
        }
    </script>
</x-app-layout>
