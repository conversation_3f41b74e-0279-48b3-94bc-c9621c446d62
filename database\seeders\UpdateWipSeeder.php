<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\UpdateWip;

class UpdateWipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $path = base_path('updatewip.csv');
        if (!file_exists($path)) {
            $this->command->error("updatewip.csv not found at {$path}");
            return;
        }

        // Read file content and normalize line endings
        $content = file_get_contents($path);
        if ($content === false) {
            $this->command->error('Unable to read updatewip.csv');
            return;
        }

        // Replace various line endings with standard \n
        $content = str_replace(["\r\n", "\r"], "\n", $content);
        $lines = explode("\n", $content);
        $lines = array_filter($lines, function($line) {
            return trim($line) !== '';
        });

        if (empty($lines)) {
            $this->command->error('updatewip.csv appears to be empty after processing.');
            return;
        }

        // Parse header from first line
        $headerLine = array_shift($lines);
        $header = str_getcsv($headerLine, ',');
        
        // Normalize header keys (trim BOM/spaces)
        $header = array_map(function ($h) {
            return trim(preg_replace('/^\xEF\xBB\xBF/', '', (string) $h));
        }, $header);

        $count = 0;
        foreach ($lines as $lineNum => $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }
            
            // Parse CSV data from line
            $row = str_getcsv($line, ',');
            
            // Map row to associative array by header
            $data = [];
            foreach ($header as $idx => $key) {
                $val = $row[$idx] ?? null;
                // Clean up values: trim spaces and surrounding quotes
                if (is_string($val)) {
                    $val = trim($val);
                    // Remove surrounding quotes if present
                    if (strlen($val) >= 2 && $val[0] === '"' && $val[-1] === '"') {
                        $val = substr($val, 1, -1);
                    }
                }
                $data[$key] = $val === '' ? null : $val;
            }

            // Build payload matching our columns
            $payload = [
                'lot_id'         => $data['lot_id'] ?? null,
                'model_15'       => $data['model_15'] ?? null,
                'lot_size'       => $data['lot_size'] ?? null,
                'lot_qty'        => isset($data['lot_qty']) ? (int)str_replace(',', '', trim((string)$data['lot_qty'])) : null,
                'stagnant_tat'   => isset($data['stagnant_tat']) ? trim((string)$data['stagnant_tat']) : null,
                'qty_class'      => $data['qty_class'] ?? null,
                'work_type'      => $data['work_type'] ?? null,
                'wip_status'     => $data['wip_status'] ?? null,
                'lot_status'     => $data['lot_status'] ?? null,
                'hold'           => $data['hold'] ?? null,
                'auto_yn'        => $data['auto_yn'] ?? null,
                'lipas_yn'       => $data['lipas_yn'] ?? null,
                'eqp_type'       => $data['eqp_type'] ?? null,
                'eqp_class'      => $data['eqp_class'] ?? null,
                'lot_location'   => $data['lot_location'] ?? null,
                'lot_code'       => $data['lot_code'] ?? null,
                'modified_by'    => $data['modified_by'] ?? null,
            ];

            // Skip if no lot_id
            if (empty($payload['lot_id'])) {
                continue;
            }

            UpdateWip::updateOrCreate(
                ['lot_id' => $payload['lot_id']],
                $payload
            );
            $count++;
        }

        $this->command->info("Successfully seeded {$count} updatewip rows from updatewip.csv");
    }
}
