<!-- Security Tab -->
<div class="tab-pane fade" id="security" role="tabpanel">
    <!-- Change Password Section -->
    <div class="mb-5">
        <h5 class="mb-3">Change Password</h5>
        <form method="post" action="{{ route('password.update') }}">
            @csrf
            @method('put')
            
            <div class="row g-3">
                <div class="col-md-6">
                    <label for="current_password" class="form-label fw-semibold">Current Password</label>
                    <input type="password" class="form-control" id="current_password" name="current_password" required>
                    @error('current_password', 'updatePassword')
                        <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                    @enderror
                </div>
                <div class="col-md-6"></div>
                <div class="col-md-6">
                    <label for="password" class="form-label fw-semibold">New Password</label>
                    <input type="password" class="form-control" id="password" name="password" required>
                    @error('password', 'updatePassword')
                        <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                    @enderror
                </div>
                <div class="col-md-6">
                    <label for="password_confirmation" class="form-label fw-semibold">Confirm Password</label>
                    <input type="password" class="form-control" id="password_confirmation" name="password_confirmation" required>
                    @error('password_confirmation', 'updatePassword')
                        <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                    @enderror
                </div>
            </div>
            
            <button type="submit" class="btn btn-warning mt-3">
                <i class="fas fa-key me-2"></i>Update Password
            </button>
        </form>
    </div>

    <!-- Security Settings -->
    <div class="mb-5">
        <h5 class="mb-3">Security Settings</h5>
        
        <div class="card border">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h6 class="mb-1">Login Verification</h6>
                        <p class="text-muted mb-0 small">This helps protect accounts from unauthorized access, even if a password is compromised.</p>
                    </div>
                    <button class="btn btn-outline-primary btn-sm">Set Up Verification</button>
                </div>
                
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h6 class="mb-1">Password Verification</h6>
                        <p class="text-muted mb-0 small">This additional step helps ensure that the person attempting to modify account details is the legitimate account owner.</p>
                    </div>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" id="passwordVerification" checked>
                        <label class="form-check-label" for="passwordVerification">Enable</label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Danger Zone -->
    <div class="mb-4">
        <h5 class="mb-3 text-danger">Danger Zone</h5>
        
        <div class="card border-danger">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h6 class="mb-1 text-danger">Deactivate Account</h6>
                        <p class="text-muted mb-0 small">Once you deactivate your account, all of your data will be permanently deleted.</p>
                    </div>
                    <button class="btn btn-outline-danger btn-sm" data-bs-toggle="modal" data-bs-target="#deactivateModal">
                        Deactivate Account
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>