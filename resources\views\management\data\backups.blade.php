<x-app-layout>
    <x-slot name="header">
        Database Backups
    </x-slot>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Backup Management</h4>
                <div class="d-flex gap-2">
                    <a href="{{ route('management.data.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Data Management
                    </a>
                    <form action="{{ route('management.data.backup.create') }}" method="POST" class="d-inline">
                        @csrf
                        <button type="submit" class="btn btn-warning">
                            <i class="fas fa-database me-2"></i>Create New Backup
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Available Backups</h5>
                </div>
                <div class="card-body p-0">
                    @if(count($backups) > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th>Filename</th>
                                        <th>Size</th>
                                        <th>Created</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($backups as $backup)
                                    <tr>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-archive text-warning me-2"></i>
                                                <strong>{{ $backup['filename'] }}</strong>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge bg-light text-dark">
                                                {{ number_format($backup['size'] / 1024, 2) }} KB
                                            </span>
                                        </td>
                                        <td>
                                            <div>
                                                <strong>{{ date('M d, Y', $backup['created_at']) }}</strong>
                                                <br><small class="text-muted">{{ date('g:i A', $backup['created_at']) }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{{ route('management.data.backup.download', $backup['filename']) }}" 
                                                   class="btn btn-outline-primary" 
                                                   title="Download">
                                                    <i class="fas fa-download"></i>
                                                </a>
                                                <form action="{{ route('management.data.backup.delete', $backup['filename']) }}" 
                                                      method="POST" 
                                                      class="d-inline"
                                                      onsubmit="return confirm('Are you sure you want to delete this backup?')">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" 
                                                            class="btn btn-outline-danger" 
                                                            title="Delete">
                                                        <i class="fas fa-trash"></i>
                                                    </button>
                                                </form>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-database fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted">No Backups Found</h5>
                            <p class="text-muted">Create your first backup to get started with data protection.</p>
                            <form action="{{ route('management.data.backup.create') }}" method="POST" class="d-inline">
                                @csrf
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-database me-2"></i>Create First Backup
                                </button>
                            </form>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Information -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle text-info me-2"></i>Backup Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>What's Included in Backups:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-check text-success me-2"></i>All user accounts and profiles</li>
                                <li><i class="fas fa-check text-success me-2"></i>Complete product catalog</li>
                                <li><i class="fas fa-check text-success me-2"></i>Order history and details</li>
                                <li><i class="fas fa-check text-success me-2"></i>System configurations</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Backup Best Practices:</h6>
                            <ul class="list-unstyled">
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>Create regular backups (weekly/monthly)</li>
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>Download and store backups securely</li>
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>Test backup integrity periodically</li>
                                <li><i class="fas fa-lightbulb text-warning me-2"></i>Keep multiple backup versions</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>