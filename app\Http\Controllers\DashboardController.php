<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Endtime;
use App\Models\Equipment;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class DashboardController extends Controller
{
    public function index(Request $request)
    {
        // Get dashboard filter parameters
        $filters = [
            'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
            'dashboard_shift' => $request->get('dashboard_shift', 'day'),
            'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
            'dashboard_work_type' => $request->get('dashboard_work_type', 'all')
        ];
        
        $dashboardStats = $this->getEndtimeDashboardStats($filters);
        
        // Get Per Line Summary and Per Size Summary data
        $perLineSummary = $this->getPerLineSummary($filters);
        $perSizeSummary = $this->getPerSizeSummary($filters);
        $linePerformanceAnalysis = $this->getLinePerformanceAnalysis($filters);
        
        // Get dynamic panel data
        $previousShiftAchievement = $this->getPreviousShiftAchievement($filters);
        $currentPerformanceMonitor = $this->getCurrentPerformanceMonitor($filters);
        
        return view('dashboard', compact(
            'dashboardStats', 
            'perLineSummary', 
            'perSizeSummary', 
            'linePerformanceAnalysis',
            'previousShiftAchievement',
            'currentPerformanceMonitor'
        ));
    }


    
    /**
     * Show the Lot Request Dashboard for analytics
     */
    public function lotRequestDashboard()
    {
        // This would contain analytics for lot requests
        // For now, return a placeholder view
        return view('dashboards.lot-requests', [
            'title' => 'Lot Request Analytics Dashboard'
        ]);
    }
    
    /**
     * Show the WIP Dashboard for analytics
     */
    public function wipDashboard()
    {
        // This would contain analytics for WIP management
        // For now, return a placeholder view
        return view('dashboards.wip', [
            'title' => 'WIP Analytics Dashboard'
        ]);
    }
    
    /**
     * Show the Machine Allocation Dashboard for analytics
     */
    public function machineAllocationDashboard()
    {
        // This would contain analytics for machine allocation
        // For now, return a placeholder view
        return view('dashboards.machine-allocation', [
            'title' => 'Machine Allocation Dashboard'
        ]);
    }
    
    /**
     * Show the Endline Report Dashboard for analytics
     */
    public function endlineReportDashboard()
    {
        // This would contain analytics for endline reports
        // For now, return a placeholder view
        return view('dashboards.endline-report', [
            'title' => 'Endline Report Dashboard'
        ]);
    }
    
    /**
     * Show the Endline WIP Entry Form
     */
    public function endlineWipForm()
    {
        // This would show the entry form for endline WIP with multiple entries
        // For now, return a placeholder view
        return view('dashboards.endline-wip-form', [
            'title' => 'Endline WIP Entry Form'
        ]);
    }
    
    /**
     * Calculate endtime dashboard statistics based on filters
     */
    private function getEndtimeDashboardStats($filters)
    {
        // Apply time range filter based on cutoff
        $endtimeQuery = Endtime::query();
        $this->applyDashboardTimeFilter($endtimeQuery, $filters['dashboard_date'], $filters['dashboard_shift'], $filters['dashboard_cutoff']);
        
        // Apply work type filter
        if ($filters['dashboard_work_type'] !== 'all') {
            $endtimeQuery->where('work_type', $filters['dashboard_work_type']);
        }
        
        // Calculate target capacity based on active equipment and filters
        $targetStats = $this->calculateTargetCapacity(
            $filters['dashboard_work_type'],
            $filters['dashboard_shift'],
            $filters['dashboard_cutoff']
        );
        
        // Total lots (ongoing + submitted)
        $totalLots = $endtimeQuery->count();
        $totalQuantity = $endtimeQuery->sum('lot_qty') ?: 0;
        
        // Submitted lots
        $submittedQuery = clone $endtimeQuery;
        $submittedLots = $submittedQuery->where('status', 'Submitted')->count();
        $submittedQuantity = $submittedQuery->sum('lot_qty') ?: 0;
        
        // Ongoing lots
        $ongoingQuery = clone $endtimeQuery;
        $ongoingLots = $ongoingQuery->where('status', 'Ongoing')->count();
        $ongoingQuantity = $ongoingQuery->sum('lot_qty') ?: 0;
        
        // Calculate percentages
        $submittedPercentage = $totalLots > 0 ? ($submittedLots / $totalLots) * 100 : 0;
        $ongoingPercentage = $totalLots > 0 ? ($ongoingLots / $totalLots) * 100 : 0;
        
        // Calculate equipment status
        $equipmentStats = $this->calculateEquipmentStatus($filters);
        
        return [
            'target_capacity' => $targetStats['capacity'],
            'equipment_count' => $targetStats['equipment_count'],
            'total_lots' => $totalLots,
            'total_quantity' => $totalQuantity,
            'submitted_lots' => $submittedLots,
            'submitted_quantity' => $submittedQuantity,
            'submitted_percentage' => $submittedPercentage,
            'ongoing_lots' => $ongoingLots,
            'ongoing_quantity' => $ongoingQuantity,
            'ongoing_percentage' => $ongoingPercentage,
            // Equipment Status data
            'total_equipment' => $equipmentStats['total_equipment'],
            'equipment_with_ongoing' => $equipmentStats['equipment_with_ongoing'],
            'equipment_status_percentage' => $equipmentStats['equipment_percentage'],
        ];
    }
    
    /**
     * Calculate target capacity based on active equipment, work type filter, and time period
     */
    private function calculateTargetCapacity($workType, $shift, $cutoff)
    {
        $equipmentQuery = Equipment::query();
        
        // Filter equipment by work type if specified
        if ($workType !== 'all') {
            $equipmentQuery->where('work_type', $workType);
        }
        
        // Get filtered equipment count
        $equipmentCount = $equipmentQuery->count();
        
        // Calculate total capacity based on equipment specifications and time period
        $equipmentList = $equipmentQuery->get();
        $totalCapacity = 0;
        
        // Calculate time multiplier based on shift and cutoff selection
        $timeMultiplier = $this->getTimeMultiplier($shift, $cutoff);
        
        foreach ($equipmentList as $equipment) {
            $oee = floatval($equipment->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $equipment->eqp_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $equipment->operation_time)) ?: 0;
            
            // Calculate capacity based on time period: OEE × Speed × Operation Time × Time Multiplier
            $periodCapacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $totalCapacity += $periodCapacity;
        }
        
        return [
            'capacity' => $totalCapacity,
            'equipment_count' => $equipmentCount
        ];
    }
    
    /**
     * Get time multiplier based on shift and cutoff selection
     * Based on 6 cutoff periods per day (4 hours each), total 24 hours
     */
    private function getTimeMultiplier($shift, $cutoff)
    {
        if ($shift === 'all') {
            // Full day (24 hours = 6 cutoffs)
            return 1.0;
        }
        
        // Calculate time fraction based on shift and cutoff
        // Each day has 6 cutoff periods of 4 hours each
        if ($shift === 'day') {
            switch ($cutoff) {
                case '1': // 07:00 AM - 11:59 AM (4 hours = 1/6 of day)
                    return 1.0 / 6.0; // ~0.1667
                case '2': // 12:00 PM - 15:59 PM (4 hours = 1/6 of day)
                    return 1.0 / 6.0; // ~0.1667
                case '3': // 16:00 PM - 18:59 PM (3 hours, but treat as 4 for consistency)
                    return 1.0 / 6.0; // ~0.1667
                case 'all': // Entire day shift: 07:00 AM - 18:59 PM (3 cutoffs = 3/6 of day)
                    return 3.0 / 6.0; // 0.5
            }
        } elseif ($shift === 'night') {
            switch ($cutoff) {
                case '1': // 19:00 PM - 23:59 PM (4 hours = 1/6 of day)
                    return 1.0 / 6.0; // ~0.1667
                case '2': // 00:00 AM - 03:59 AM (4 hours = 1/6 of day)
                    return 1.0 / 6.0; // ~0.1667
                case '3': // 04:00 AM - 06:59 AM (3 hours, but treat as 4 for consistency)
                    return 1.0 / 6.0; // ~0.1667
                case 'all': // Entire night shift: 19:00 PM - 06:59 AM (3 cutoffs = 3/6 of day)
                    return 3.0 / 6.0; // 0.5
            }
        }
        
        // Default to full day if no match
        return 1.0;
    }
    
    /**
     * Calculate equipment status based on filters
     * Now uses equipment.ongoing_lot column instead of checking endtime status
     */
    private function calculateEquipmentStatus($filters)
    {
        // Get total equipment count based on work type filter
        $totalEquipmentQuery = Equipment::query();
        if ($filters['dashboard_work_type'] !== 'all') {
            $totalEquipmentQuery->where('work_type', $filters['dashboard_work_type']);
        }
        $totalEquipment = $totalEquipmentQuery->count();
        
        // Get equipment with ongoing lots by checking the ongoing_lot column directly
        $equipmentWithOngoingQuery = Equipment::query()
            ->whereNotNull('ongoing_lot');
            
        // Apply work type filter to equipment with ongoing lots
        if ($filters['dashboard_work_type'] !== 'all') {
            $equipmentWithOngoingQuery->where('work_type', $filters['dashboard_work_type']);
        }
        
        $equipmentWithOngoing = $equipmentWithOngoingQuery->count();
        
        // Calculate percentage
        $equipmentPercentage = $totalEquipment > 0 ? round(($equipmentWithOngoing / $totalEquipment) * 100, 1) : 0;
        
        return [
            'total_equipment' => $totalEquipment,
            'equipment_with_ongoing' => $equipmentWithOngoing,
            'equipment_percentage' => $equipmentPercentage
        ];
    }
    
    /**
     * Apply dashboard time filter based on cutoff periods
     */
    private function applyDashboardTimeFilter($query, $date, $shift, $cutoff)
    {
        if (!$date) {
            return; // No filtering if date is missing
        }

        $baseDate = Carbon::parse($date);
        $startDateTime = null;
        $endDateTime = null;

        // If shift is 'all', filter by entire date (00:00:00 to 23:59:59)
        if ($shift === 'all') {
            $startDateTime = $baseDate->copy()->startOfDay();
            $endDateTime = $baseDate->copy()->endOfDay();
        }
        // Define cutoff periods for each specific shift
        elseif ($shift === 'day') {
            switch ($cutoff) {
                case '1': // 7:00 AM - 12:00 PM
                    $startDateTime = $baseDate->copy()->setTime(7, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(11, 59, 59);
                    break;
                case '2': // 12:00 PM - 4:00 PM
                    $startDateTime = $baseDate->copy()->setTime(12, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(15, 59, 59);
                    break;
                case '3': // 4:00 PM - 7:00 PM
                    $startDateTime = $baseDate->copy()->setTime(16, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(18, 59, 59);
                    break;
                case 'all': // Entire day shift: 7:00 AM - 7:00 PM
                    $startDateTime = $baseDate->copy()->setTime(7, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(18, 59, 59);
                    break;
            }
        } elseif ($shift === 'night') {
            // Night shift uses start date convention:
            // Night shift "Sept 6" = Sept 6 19:00 → Sept 7 06:59
            switch ($cutoff) {
                case '1': // 7:00 PM - 11:59 PM (same day as start date)
                    $startDateTime = $baseDate->copy()->setTime(19, 0, 0);
                    $endDateTime = $baseDate->copy()->setTime(23, 59, 59);
                    break;
                case '2': // 12:00 AM - 3:59 AM (next day after start date)
                    $startDateTime = $baseDate->copy()->addDay()->setTime(0, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(3, 59, 59);
                    break;
                case '3': // 4:00 AM - 6:59 AM (next day after start date)
                    $startDateTime = $baseDate->copy()->addDay()->setTime(4, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(6, 59, 59);
                    break;
                case 'all': // Entire night shift: 7:00 PM (start date) - 6:59 AM (next day)
                    $startDateTime = $baseDate->copy()->setTime(19, 0, 0);
                    $endDateTime = $baseDate->copy()->addDay()->setTime(6, 59, 59);
                    break;
            }
        }

        // Apply the time range filter if we have valid start and end times
        if ($startDateTime && $endDateTime) {
            $query->whereBetween('est_endtime', [$startDateTime, $endDateTime]);
        }
    }
    
    /**
     * API endpoint for updating dashboard stats via AJAX
     */
    public function updateDashboardStats(Request $request)
    {
        try {
            $filters = [
                'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
                'dashboard_shift' => $request->get('dashboard_shift', 'all'),
                'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
                'dashboard_work_type' => $request->get('dashboard_work_type', 'all')
            ];
            
            $stats = $this->getEndtimeDashboardStats($filters);
            $perLineSummary = $this->getPerLineSummary($filters);
            $perSizeSummary = $this->getPerSizeSummary($filters);
            $linePerformanceAnalysis = $this->getLinePerformanceAnalysis($filters);
            
            // Get dynamic panel data
            $previousShiftAchievement = $this->getPreviousShiftAchievement($filters);
            // Live Performance Monitor always uses TODAY's data regardless of filter date
            $currentPerformanceMonitor = $this->getCurrentPerformanceMonitor($filters);
            
            return response()->json([
                'success' => true,
                'stats' => $stats,
                'perLineSummary' => $perLineSummary,
                'perSizeSummary' => $perSizeSummary,
                'linePerformanceAnalysis' => $linePerformanceAnalysis,
                'previousShiftAchievement' => $previousShiftAchievement,
                'currentPerformanceMonitor' => $currentPerformanceMonitor
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update dashboard statistics: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * API endpoint for getting line-specific area performance
     */
    public function getLineAreaPerformanceData(Request $request)
    {
        try {
            $line = $request->get('line', 'A');
            $filters = [
                'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
                'dashboard_shift' => $request->get('dashboard_shift', 'all'),
                'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
                'dashboard_work_type' => $request->get('dashboard_work_type', 'all')
            ];
            
            $areaPerformance = $this->getLineAreaPerformance($line, $filters);
            
            return response()->json([
                'success' => true,
                'line' => $line,
                'area_performance' => $areaPerformance
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get line area performance: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * API endpoint for manufacturing overview chart data
     */
    public function getManufacturingOverviewData(Request $request)
    {
        try {
            // Get dashboard filters from request (same as dashboard cards)
            $filters = [
                'dashboard_date' => $request->get('dashboard_date', today()->format('Y-m-d')),
                'dashboard_shift' => $request->get('dashboard_shift', 'all'),
                'dashboard_cutoff' => $request->get('dashboard_cutoff', 'all'),
                'dashboard_work_type' => $request->get('dashboard_work_type', 'all')
            ];
            
            $chartData = $this->generateManufacturingChartDataFromFilters($filters);
            
            return response()->json([
                'success' => true,
                'data' => $chartData
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get manufacturing overview data: ' . $e->getMessage()
            ], 500);
        }
    }
    
    /**
     * Generate manufacturing chart data distributed by production lines (using filters)
     */
    private function generateManufacturingChartDataFromFilters($filters)
    {
        $targetCapacity = [];
        $totalEndtime = [];
        $submittedLots = [];
        $remainingLots = [];
        $labels = [];
        
        // Production lines A through K
        $productionLines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        
        foreach ($productionLines as $line) {
            $labels[] = "Line {$line}";
            
            // Get data for this specific line using current dashboard filters
            $lineData = $this->getLineDataByFilters($line, $filters);
            
            // Target Capacity for this line (Card 1 data distributed by line)
            $targetCapacity[] = round(($lineData['target_capacity'] ?? 0) / 1000000, 1); // Convert to millions
            
            // Total Endtime for this line (Card 2 data distributed by line)
            $totalEndtime[] = round(($lineData['total_endtime'] ?? 0) / 1000000, 1); // Convert to millions
            
            // Submitted Lots for this line (Card 3 data distributed by line)
            $submittedLots[] = round(($lineData['submitted_lots'] ?? 0) / 1000000, 1); // Convert to millions
            
            // Remaining Lots for this line (Card 4 data distributed by line)
            $remainingLots[] = round(($lineData['remaining_lots'] ?? 0) / 1000000, 1); // Convert to millions
        }
        
        // Calculate totals for verification
        $totalTargetCapacity = array_sum($targetCapacity) * 1000000; // Convert back to PCS for comparison
        $totalTotalEndtime = array_sum($totalEndtime) * 1000000;
        $totalSubmittedLots = array_sum($submittedLots) * 1000000;
        $totalRemainingLots = array_sum($remainingLots) * 1000000;
        
        // Log totals for debugging
        \Log::info('Chart Totals vs Dashboard Cards:', [
            'Target Capacity' => number_format($totalTargetCapacity) . ' PCS',
            'Total Endtime' => number_format($totalTotalEndtime) . ' PCS',
            'Submitted Lots' => number_format($totalSubmittedLots) . ' PCS',
            'Remaining Lots' => number_format($totalRemainingLots) . ' PCS',
        ]);
        
        return [
            'labels' => $labels,
            'series' => [
                [
                    'name' => 'Target',
                    'type' => 'area',
                    'data' => array_map(function($label, $value) {
                        return ['x' => $label, 'y' => $value];
                    }, $labels, $targetCapacity)
                ],
                [
                    'name' => 'Endtime',
                    'type' => 'bar',
                    'data' => array_map(function($label, $value) {
                        return ['x' => $label, 'y' => $value];
                    }, $labels, $totalEndtime)
                ],
                [
                    'name' => 'Submitted',
                    'type' => 'bar',
                    'chart' => [
                        'dropShadow' => [
                            'enabled' => true,
                            'enabledOnSeries' => null,
                            'top' => 5,
                            'left' => 0,
                            'blur' => 3,
                            'color' => '#000',
                            'opacity' => 0.1,
                        ],
                    ],
                    'data' => array_map(function($label, $value) {
                        return ['x' => $label, 'y' => $value];
                    }, $labels, $submittedLots)
                ],
                [
                    'name' => 'Remaining',
                    'type' => 'bar',
                    'data' => array_map(function($label, $value) {
                        return ['x' => $label, 'y' => $value];
                    }, $labels, $remainingLots)
                ]
            ],
            'totals_debug' => [
                'target_capacity' => $totalTargetCapacity,
                'total_endtime' => $totalTotalEndtime,
                'submitted_lots' => $totalSubmittedLots,
                'remaining_lots' => $totalRemainingLots
            ]
        ];
    }
    
    /**
     * Get data for a specific production line based on dashboard filters
     */
    private function getLineDataByFilters($line, $filters)
    {
        // Extract filters
        $currentDate = $filters['dashboard_date'] ?? today()->format('Y-m-d');
        $currentShift = $filters['dashboard_shift'] ?? 'all';
        $currentCutoff = $filters['dashboard_cutoff'] ?? 'all';
        $workType = $filters['dashboard_work_type'] ?? 'all';
        
        // Get equipment for this line that matches the work type filter
        $equipmentQuery = Equipment::where('eqp_line', $line);
        if ($workType !== 'all') {
            $equipmentQuery->where('work_type', $workType);
        }
        $equipment = $equipmentQuery->get();
        
        // Calculate target capacity for this line (same method as dashboard cards)
        $targetCapacity = 0;
        
        // Calculate time multiplier based on shift and cutoff selection (same as dashboard cards)
        $timeMultiplier = $this->getTimeMultiplier($currentShift, $currentCutoff);
        
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->eqp_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate capacity based on time period: OEE × Speed × Operation Time × Time Multiplier
            $periodCapacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $targetCapacity += $periodCapacity;
        }
        
        // Get endtime data for this line using the SAME filters as dashboard cards
        $endtimeQuery = $this->getLineEndtimeQuery($line, $currentDate, $currentShift, $currentCutoff, $workType);
        
        // Total endtime for this line (same as card 2)
        $totalEndtime = $endtimeQuery->sum('lot_qty') ?: 0;
        
        // Submitted lots for this line (same as card 3)
        $submittedQuery = clone $endtimeQuery;
        $submittedLots = $submittedQuery->where('status', 'Submitted')->sum('lot_qty') ?: 0;
        
        // Remaining lots for this line (same as card 4)
        $remainingLots = $totalEndtime - $submittedLots;
        
        return [
            'target_capacity' => $targetCapacity,
            'total_endtime' => $totalEndtime,
            'submitted_lots' => $submittedLots,
            'remaining_lots' => $remainingLots
        ];
    }
    
    /**
     * Get endtime query for specific line using SAME filters as dashboard cards
     */
    private function getLineEndtimeQuery($line, $date, $shift, $cutoff, $workType)
    {
        // Base endtime query for this line (endtime table has eqp_line column directly)
        $query = Endtime::where('eqp_line', $line);
        
        // Apply the EXACT SAME time filter logic as dashboard cards
        $this->applyDashboardTimeFilter($query, $date, $shift, $cutoff);
        
        // Apply work type filter if needed
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query;
    }
    
    /**
     * Get monthly equipment capacity
     */
    private function getMonthlyCapacity($month, $workType)
    {
        $equipmentQuery = Equipment::query();
        if ($workType !== 'all') {
            $equipmentQuery->where('work_type', $workType);
        }
        
        $equipment = $equipmentQuery->get();
        $totalCapacity = 0;
        
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->eqp_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate monthly capacity (30 days average)
            $monthlyCapacity = round($oee * $speed * $operationTime * 30);
            $totalCapacity += $monthlyCapacity;
        }
        
        return $totalCapacity;
    }
    
    /**
     * Get monthly total endtime (all lots)
     */
    private function getMonthlyTotalEndtime($month, $workType)
    {
        $query = Endtime::whereMonth('est_endtime', $month->month)
                        ->whereYear('est_endtime', $month->year);
                        
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query->sum('lot_qty') ?: 0;
    }
    
    /**
     * Get monthly submitted endtime
     */
    private function getMonthlySubmittedEndtime($month, $workType)
    {
        $query = Endtime::whereMonth('est_endtime', $month->month)
                        ->whereYear('est_endtime', $month->year)
                        ->where('status', 'Submitted');
                        
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query->sum('lot_qty') ?: 0;
    }
    
    /**
     * Get monthly completion rate
     */
    private function getMonthlyCompletionRate($month, $workType)
    {
        $totalQuery = Endtime::whereMonth('est_endtime', $month->month)
                             ->whereYear('est_endtime', $month->year);
                             
        $submittedQuery = clone $totalQuery;
        $submittedQuery->where('status', 'Submitted');
        
        if ($workType !== 'all') {
            $totalQuery->where('work_type', $workType);
            $submittedQuery->where('work_type', $workType);
        }
        
        $total = $totalQuery->count();
        $submitted = $submittedQuery->count();
        
        return $total > 0 ? ($submitted / $total) * 100 : 0;
    }
    
    /**
     * Get daily equipment capacity
     */
    private function getDailyCapacity($date, $workType)
    {
        $equipmentQuery = Equipment::query();
        if ($workType !== 'all') {
            $equipmentQuery->where('work_type', $workType);
        }
        
        $equipment = $equipmentQuery->get();
        $totalCapacity = 0;
        
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->eqp_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate daily capacity
            $dailyCapacity = round($oee * $speed * $operationTime);
            $totalCapacity += $dailyCapacity;
        }
        
        return $totalCapacity;
    }
    
    /**
     * Get daily total endtime (all lots)
     */
    private function getDailyTotalEndtime($date, $workType)
    {
        $query = Endtime::whereDate('est_endtime', $date->format('Y-m-d'));
                        
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query->sum('lot_qty') ?: 0;
    }
    
    /**
     * Get daily submitted endtime
     */
    private function getDailySubmittedEndtime($date, $workType)
    {
        $query = Endtime::whereDate('est_endtime', $date->format('Y-m-d'))
                        ->where('status', 'Submitted');
                        
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query->sum('lot_qty') ?: 0;
    }
    
    /**
     * Get daily completion rate
     */
    private function getDailyCompletionRate($date, $workType)
    {
        $totalQuery = Endtime::whereDate('est_endtime', $date->format('Y-m-d'));
        $submittedQuery = clone $totalQuery;
        $submittedQuery->where('status', 'Submitted');
        
        if ($workType !== 'all') {
            $totalQuery->where('work_type', $workType);
            $submittedQuery->where('work_type', $workType);
        }
        
        $total = $totalQuery->count();
        $submitted = $submittedQuery->count();
        
        return $total > 0 ? ($submitted / $total) * 100 : 0;
    }
    
    /**
     * Get Per Line Summary data based on dashboard filters
     */
    private function getPerLineSummary($filters)
    {
        $lines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        $summary = [
            'lines' => $lines,
            'target' => [],
            'endtime' => [],
            'submitted' => [],
            'submitted_percent' => [],
            'endtime_percent' => []
        ];
        
        foreach ($lines as $line) {
            $lineData = $this->getLineDataByFilters($line, $filters);
            
            // Target capacity in millions
            $targetCapacity = $lineData['target_capacity'];
            $summary['target'][$line] = $targetCapacity > 0 ? number_format($targetCapacity / 1000000, 0) . ' M' : '0 M';
            
            // Endtime (total lots) in millions
            $totalEndtime = $lineData['total_endtime'];
            $summary['endtime'][$line] = $totalEndtime > 0 ? number_format($totalEndtime / 1000000, 0) . ' M' : '0 M';
            
            // Submitted lots in millions
            $submittedLots = $lineData['submitted_lots'];
            $summary['submitted'][$line] = $submittedLots > 0 ? number_format($submittedLots / 1000000, 0) . ' M' : '0 M';
            
            // Calculate percentages
            $submittedPercent = $totalEndtime > 0 ? round(($submittedLots / $totalEndtime) * 100, 1) : 0;
            $endtimePercent = $targetCapacity > 0 ? round(($totalEndtime / $targetCapacity) * 100, 1) : 0;
            
            $summary['submitted_percent'][$line] = $submittedPercent;
            $summary['endtime_percent'][$line] = $endtimePercent;
        }
        
        return $summary;
    }
    
    /**
     * Get Line Performance Analysis data with categorization
     * - Top Performers: Lines with 100% and above result
     * - Needs Attention: Worst 3 lines
     * - Average Performance: Below 100% excluding the worst 3 lines
     * - Each line has 4 areas
     */
    private function getLinePerformanceAnalysis($filters)
    {
        $lines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        $performanceData = [];
        
        // Get shift context to sync header with Panel 1
        $shiftContext = $this->getShiftContext($filters);
        $currentContext = $shiftContext['current'];
        
        // Calculate performance for each line
        foreach ($lines as $line) {
            $lineData = $this->getLineDataByFilters($line, $filters);
            
            // Calculate performance percentage (submitted vs target)
            $targetCapacity = $lineData['target_capacity'];
            $submittedLots = $lineData['submitted_lots'];
            $totalEndtime = $lineData['total_endtime'];
            $performancePercent = $targetCapacity > 0 ? round(($submittedLots / $targetCapacity) * 100, 1) : 0;
            
            // Get area performance for this line (each line has 4 areas)
            $lineAreaPerformance = $this->getLineAreaPerformance($line, $filters);
            
            $performanceData[$line] = [
                'line' => $line,
                'target_capacity' => $targetCapacity,
                'target_formatted' => $targetCapacity > 0 ? number_format($targetCapacity / 1000000, 1) . 'M PCS' : '0 PCS',
                'result' => $submittedLots,
                'result_formatted' => $submittedLots > 0 ? number_format($submittedLots / 1000000, 1) . 'M PCS' : '0 PCS',
                'performance_percent' => $performancePercent,
                'area_performance' => $lineAreaPerformance, // 4 areas per line
                'is_active' => false // Will be set to true for selected line
            ];
        }
        
        // Sort by performance percentage (descending)
        uasort($performanceData, function($a, $b) {
            return $b['performance_percent'] <=> $a['performance_percent'];
        });
        
        // Categorize lines
        $topPerformers = [];
        $needsAttention = [];
        $averagePerformance = [];
        
        // Get lines with 100% and above (Top Performers)
        foreach ($performanceData as $line => $data) {
            if ($data['performance_percent'] >= 100) {
                $topPerformers[] = $data;
            }
        }
        
        // Get worst 3 lines (Needs Attention)
        $sortedByPerformance = array_values($performanceData);
        $needsAttention = array_slice(array_reverse($sortedByPerformance), 0, 3);
        
        // Get below 100% excluding worst 3 (Average Performance)
        $belowHundred = array_filter($performanceData, function($data) {
            return $data['performance_percent'] < 100;
        });
        
        // Remove worst 3 from below 100%
        $worstThreeLines = array_column($needsAttention, 'line');
        $averagePerformance = array_filter($belowHundred, function($data) use ($worstThreeLines) {
            return !in_array($data['line'], $worstThreeLines);
        });
        
        // Set default selected line (first in top performers, or first overall)
        $defaultSelectedLine = !empty($topPerformers) ? $topPerformers[0]['line'] : 'A';
        $defaultAreaPerformance = $performanceData[$defaultSelectedLine]['area_performance'];
        
        return [
            'top_performers' => array_values($topPerformers),
            'needs_attention' => array_values($needsAttention),
            'average_performance' => array_values($averagePerformance),
            'all_lines' => $performanceData,
            'selected_line' => $defaultSelectedLine,
            'area_performance' => $defaultAreaPerformance, // Default area performance
            // Dynamic header information synced with Panel 1
            'panel_title' => $this->generatePanel2Title($currentContext['title'] ?? 'Line Performance Analysis'),
            'panel_subtitle' => $this->generatePanel2Subtitle($currentContext['subtitle'] ?? 'Current Performance by Line & Area'),
            'period_desc' => $currentContext['period_desc'] ?? 'Current period'
        ];
    }
    
    /**
     * Calculate how much of a lot's quantity should be attributed to each area
     * based on equipment capacity contribution
     */
    private function calculateLotAreaAttribution($lotRecord, $filters)
    {
        $attributions = [];
        $equipmentUsed = [];
        
        // Collect all equipment used in this lot
        for ($i = 1; $i <= 10; $i++) {
            $equipmentNo = $lotRecord->{"eqp_{$i}"};
            if (!empty($equipmentNo)) {
                $equipmentUsed[] = $equipmentNo;
            }
        }
        
        if (empty($equipmentUsed)) {
            return []; // No equipment used
        }
        
        // Get equipment details with area information
        $equipmentDetails = Equipment::whereIn('eqp_no', $equipmentUsed)
                                     ->select('eqp_no', 'eqp_line', 'eqp_area', 'eqp_oee', 'eqp_speed', 'operation_time')
                                     ->get()
                                     ->keyBy('eqp_no');
        
        // Calculate capacity per area
        $areaCapacities = [];
        $totalCapacity = 0;
        
        foreach ($equipmentUsed as $eqpNo) {
            $equipment = $equipmentDetails[$eqpNo] ?? null;
            if (!$equipment) continue;
            
            $area = $equipment->eqp_area;
            $oee = floatval($equipment->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $equipment->eqp_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $equipment->operation_time)) ?: 0;
            
            $capacity = $oee * $speed * $operationTime;
            
            if (!isset($areaCapacities[$area])) {
                $areaCapacities[$area] = 0;
            }
            $areaCapacities[$area] += $capacity;
            $totalCapacity += $capacity;
        }
        
        // Calculate attribution per area
        $lotQuantity = $lotRecord->lot_qty ?: 0;
        
        foreach ($areaCapacities as $area => $areaCapacity) {
            $attributionPercent = $totalCapacity > 0 ? ($areaCapacity / $totalCapacity) : 0;
            $attributions[$area] = [
                'quantity' => round($lotQuantity * $attributionPercent),
                'percentage' => round($attributionPercent * 100, 2),
                'capacity_contribution' => $areaCapacity
            ];
        }
        
        return $attributions;
    }
    
    /**
     * Calculate area performance for a specific line and area using capacity-based attribution
     */
    private function calculateAreaPerformance($line, $area, $filters)
    {
        // Get all lots that used equipment from this line
        $lotsQuery = Endtime::query();
        $this->applyDashboardTimeFilter($lotsQuery, $filters['dashboard_date'], 
                                       $filters['dashboard_shift'], $filters['dashboard_cutoff']);
        
        // Apply work type filter
        if ($filters['dashboard_work_type'] !== 'all') {
            $lotsQuery->where('work_type', $filters['dashboard_work_type']);
        }
        
        // Get lots that use any equipment from this line
        $lotsInLine = $lotsQuery->where('eqp_line', $line)->get();
        
        $areaTarget = $this->getAreaTargetCapacity($line, $area, $filters);
        $areaSubmittedQuantity = 0;
        
        foreach ($lotsInLine as $lot) {
            if ($lot->status !== 'Submitted') continue;
            
            // Calculate how much of this lot should be attributed to this area
            $lotAttributions = $this->calculateLotAreaAttribution($lot, $filters);
            
            if (isset($lotAttributions[$area])) {
                $areaSubmittedQuantity += $lotAttributions[$area]['quantity'];
            }
        }
        
        return $areaTarget > 0 ? round(($areaSubmittedQuantity / $areaTarget) * 100, 1) : 0;
    }
    
    /**
     * Get area target capacity for a specific line and area
     */
    private function getAreaTargetCapacity($line, $area, $filters)
    {
        $equipmentInArea = Equipment::where('eqp_line', $line)
                                    ->where('eqp_area', $area);
        
        // Apply work type filter
        if ($filters['dashboard_work_type'] !== 'all') {
            $equipmentInArea->where('work_type', $filters['dashboard_work_type']);
        }
        
        $equipment = $equipmentInArea->get();
        $timeMultiplier = $this->getTimeMultiplier($filters['dashboard_shift'], 
                                                  $filters['dashboard_cutoff']);
        
        $totalCapacity = 0;
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->eqp_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            $capacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $totalCapacity += $capacity;
        }
        
        return $totalCapacity;
    }
    
    /**
     * Get area performance for a specific line (1 line = 4 areas)
     * Now uses real capacity-based calculation instead of simulation
     */
    private function getLineAreaPerformance($line, $filters)
    {
        $areaPerformance = [];
        
        // Calculate performance for each area (1-4) in the line
        for ($area = 1; $area <= 4; $area++) {
            $areaPerformance[$area] = $this->calculateAreaPerformance($line, $area, $filters);
        }
        
        return $areaPerformance;
    }
    
    /**
     * Generate Panel 2 title from Panel 1 title
     */
    private function generatePanel2Title($panel1Title)
    {
        // Transform Panel 1 title to Panel 2 title
        $transformations = [
            'Full Day Performance Monitor' => 'Full Day Line Performance Analysis',
            'Day Shift Performance Monitor' => 'Day Shift Line Performance Analysis', 
            'Night Shift Performance Monitor' => 'Night Shift Line Performance Analysis',
            'Day Shift 1st Cutoff Monitor' => 'Day Shift 1st Cutoff Line Analysis',
            'Day Shift 2nd Cutoff Monitor' => 'Day Shift 2nd Cutoff Line Analysis',
            'Day Shift 3rd Cutoff Monitor' => 'Day Shift 3rd Cutoff Line Analysis',
            'Night Shift 1st Cutoff Monitor' => 'Night Shift 1st Cutoff Line Analysis',
            'Night Shift 2nd Cutoff Monitor' => 'Night Shift 2nd Cutoff Line Analysis',
            'Night Shift 3rd Cutoff Monitor' => 'Night Shift 3rd Cutoff Line Analysis'
        ];
        
        // Check for exact matches first
        if (isset($transformations[$panel1Title])) {
            return $transformations[$panel1Title];
        }
        
        // Fallback transformations for any variations
        return str_replace(
            ['Performance Monitor', 'Monitor', 'Achievement'], 
            ['Line Performance Analysis', 'Line Analysis', 'Line Analysis'], 
            $panel1Title
        );
    }
    
    /**
     * Generate Panel 2 subtitle from Panel 1 subtitle
     */
    private function generatePanel2Subtitle($panel1Subtitle)
    {
        // Transform Panel 1 subtitle to Panel 2 subtitle for line performance context
        $transformations = [
            'Complete Day Analysis' => 'Performance by Line & Area',
            'Day Shift Progress' => 'Day Shift Performance by Line & Area',
            'Complete Day Results' => 'Line Performance Results by Area',
            'Day Shift Results' => 'Day Shift Line Performance by Area',
            'Night Shift Results' => 'Night Shift Line Performance by Area'
        ];
        
        // Check for exact matches in the subtitle
        foreach ($transformations as $search => $replace) {
            if (strpos($panel1Subtitle, $search) !== false) {
                return str_replace($search, $replace, $panel1Subtitle);
            }
        }
        
        // Fallback transformations
        $fallbackTransforms = [
            'Achievement' => 'Performance by Line & Area',
            'Progress' => 'Performance by Line & Area',
            'Results' => 'Line Performance by Area',
            'Analysis' => 'Performance by Line & Area'
        ];
        
        foreach ($fallbackTransforms as $search => $replace) {
            if (strpos($panel1Subtitle, $search) !== false) {
                return str_replace($search, $replace, $panel1Subtitle);
            }
        }
        
        // Default fallback
        return $panel1Subtitle . ' - Performance by Line & Area';
    }
    
    /**
     * Get dynamic shift context for the dashboard panels
     */
    private function getShiftContext($filters)
    {
        $currentDate = Carbon::parse($filters['dashboard_date'] ?? today()->format('Y-m-d'));
        $currentShift = $filters['dashboard_shift'] ?? 'all';
        $currentCutoff = $filters['dashboard_cutoff'] ?? 'all';
        
        // Determine current period context
        $currentContext = $this->getCurrentPeriodContext($currentDate, $currentShift, $currentCutoff);
        
        // Determine previous period context
        $previousContext = $this->getPreviousPeriodContext($currentDate, $currentShift, $currentCutoff);
        
        return [
            'current' => $currentContext,
            'previous' => $previousContext
        ];
    }
    
    /**
     * Get current period context based on filters
     */
    private function getCurrentPeriodContext($date, $shift, $cutoff)
    {
        $context = [
            'date' => $date->format('Y-m-d'),
            'shift' => $shift,
            'cutoff' => $cutoff,
            'title' => '',
            'subtitle' => '',
            'period_desc' => ''
        ];
        
        if ($shift === 'all') {
            $context['title'] = 'Full Day Performance Monitor';
            $context['subtitle'] = $date->format('M j, Y') . ' - Complete Day Analysis';
            $context['period_desc'] = 'Full 24-hour period';
        } elseif ($shift === 'day') {
            if ($cutoff === 'all') {
                $context['title'] = 'Day Shift Performance Monitor';
                $context['subtitle'] = $date->format('M j, Y') . ' - Day Shift Progress';
                $context['period_desc'] = '07:00 AM - 07:00 PM (12 hours)';
            } else {
                $cutoffNames = ['1' => '1st', '2' => '2nd', '3' => '3rd'];
                $cutoffTimes = [
                    '1' => '07:00 AM - 12:00 PM',
                    '2' => '12:00 PM - 04:00 PM', 
                    '3' => '04:00 PM - 07:00 PM'
                ];
                $context['title'] = 'Day Shift ' . ($cutoffNames[$cutoff] ?? '') . ' Cutoff Monitor';
                $context['subtitle'] = $date->format('M j, Y') . ' - ' . ($cutoffTimes[$cutoff] ?? '');
                $context['period_desc'] = $cutoffTimes[$cutoff] ?? '';
            }
        } elseif ($shift === 'night') {
            if ($cutoff === 'all') {
                $context['title'] = 'Night Shift Performance Monitor';
                $context['subtitle'] = $date->format('M j, Y') . ' 07:00 PM ~ ' . $date->copy()->addDay()->format('M j, Y') . ' 06:59 AM';
                $context['period_desc'] = '07:00 PM - 07:00 AM (12 hours)';
            } else {
                $cutoffNames = ['1' => '1st', '2' => '2nd', '3' => '3rd'];
                $nextDay = $date->copy()->addDay();
                
                // Generate proper date ranges for night shift cutoffs
                $cutoffDetails = [
                    '1' => [
                        'time' => '07:00 PM - 11:59 PM',
                        'subtitle' => $date->format('M j, Y') . ' - 07:00 PM - 11:59 PM'
                    ],
                    '2' => [
                        'time' => '12:00 AM - 03:59 AM', 
                        'subtitle' => $nextDay->format('M j, Y') . ' - 12:00 AM - 03:59 AM'
                    ],
                    '3' => [
                        'time' => '04:00 AM - 06:59 AM',
                        'subtitle' => $nextDay->format('M j, Y') . ' - 04:00 AM - 06:59 AM'
                    ]
                ];
                
                $context['title'] = 'Night Shift ' . ($cutoffNames[$cutoff] ?? '') . ' Cutoff Monitor';
                $context['subtitle'] = $cutoffDetails[$cutoff]['subtitle'] ?? ($date->format('M j, Y') . ' - Night Cutoff');
                $context['period_desc'] = $cutoffDetails[$cutoff]['time'] ?? '';
            }
        }
        
        return $context;
    }
    
    /**
     * Get previous period context based on current filters
     */
    private function getPreviousPeriodContext($date, $shift, $cutoff)
    {
        $context = [
            'date' => '',
            'shift' => '',
            'cutoff' => '',
            'title' => '',
            'subtitle' => '',
            'period_desc' => ''
        ];
        
        if ($shift === 'all') {
            // Previous day
            $prevDate = $date->copy()->subDay();
            $context['date'] = $prevDate->format('Y-m-d');
            $context['shift'] = 'all';
            $context['cutoff'] = 'all';
            $context['title'] = 'Previous Day Achievement';
            $context['subtitle'] = $prevDate->format('M j, Y') . ' - Complete Day Results';
            $context['period_desc'] = 'Full 24-hour period';
        } elseif ($shift === 'day') {
            if ($cutoff === 'all') {
                // Previous night shift (same date)
                $context['date'] = $date->format('Y-m-d');
                $context['shift'] = 'night';
                $context['cutoff'] = 'all';
                $context['title'] = 'Previous Night Shift Achievement';
                $context['subtitle'] = $date->format('M j, Y') . ' 07:00 PM ~ ' . $date->copy()->addDay()->format('M j, Y') . ' 06:59 AM';
                $context['period_desc'] = 'Night shift (12 hours)';
            } else {
                // Previous cutoff period
                $prevCutoff = $this->getPreviousCutoff('day', $cutoff);
                if ($prevCutoff['cutoff'] === '3' && $cutoff === '1') {
                    // Previous day's 3rd cutoff
                    $context['date'] = $date->copy()->subDay()->format('Y-m-d');
                } else {
                    $context['date'] = $date->format('Y-m-d');
                }
                $context['shift'] = $prevCutoff['shift'];
                $context['cutoff'] = $prevCutoff['cutoff'];
                $context['title'] = 'Previous ' . $prevCutoff['title'] . ' Achievement';
                $context['subtitle'] = $prevCutoff['period_desc'];
                $context['period_desc'] = $prevCutoff['period_desc'];
            }
        } elseif ($shift === 'night') {
            if ($cutoff === 'all') {
                // Previous day shift
                $context['date'] = $date->format('Y-m-d');
                $context['shift'] = 'day';
                $context['cutoff'] = 'all';
                $context['title'] = 'Previous Day Shift Achievement';
                $context['subtitle'] = $date->format('M j, Y') . ' 07:00 AM ~ 07:00 PM';
                $context['period_desc'] = 'Day shift (12 hours)';
            } else {
                // Previous cutoff period
                $prevCutoff = $this->getPreviousCutoff('night', $cutoff);
                $context['date'] = $date->format('Y-m-d');
                $context['shift'] = $prevCutoff['shift'];
                $context['cutoff'] = $prevCutoff['cutoff'];
                $context['title'] = 'Previous ' . $prevCutoff['title'] . ' Achievement';
                $context['subtitle'] = $prevCutoff['period_desc'];
                $context['period_desc'] = $prevCutoff['period_desc'];
            }
        }
        
        return $context;
    }
    
    /**
     * Get previous cutoff period details
     */
    private function getPreviousCutoff($shift, $cutoff)
    {
        $cutoffSequence = [
            'day' => [
                '1' => ['shift' => 'night', 'cutoff' => '3', 'title' => 'Night 3rd Cutoff', 'period_desc' => '04:00 AM - 07:00 AM'],
                '2' => ['shift' => 'day', 'cutoff' => '1', 'title' => 'Day 1st Cutoff', 'period_desc' => '07:00 AM - 12:00 PM'],
                '3' => ['shift' => 'day', 'cutoff' => '2', 'title' => 'Day 2nd Cutoff', 'period_desc' => '12:00 PM - 04:00 PM']
            ],
            'night' => [
                '1' => ['shift' => 'day', 'cutoff' => '3', 'title' => 'Day 3rd Cutoff', 'period_desc' => '04:00 PM - 07:00 PM'],
                '2' => ['shift' => 'night', 'cutoff' => '1', 'title' => 'Night 1st Cutoff', 'period_desc' => '07:00 PM - 12:00 AM'],
                '3' => ['shift' => 'night', 'cutoff' => '2', 'title' => 'Night 2nd Cutoff', 'period_desc' => '12:00 AM - 04:00 AM']
            ]
        ];
        
        return $cutoffSequence[$shift][$cutoff] ?? [
            'shift' => $shift,
            'cutoff' => '1', 
            'title' => ucfirst($shift) . ' Shift',
            'period_desc' => 'Previous period'
        ];
    }
    
    /**
     * Get current filter period achievement data (Left Panel)
     */
    private function getPreviousShiftAchievement($filters)
    {
        $shiftContext = $this->getShiftContext($filters);
        $currentContext = $shiftContext['current'];
        
        // Use CURRENT filter data (not previous)
        $currentStats = $this->getEndtimeDashboardStats($filters);
        
        // Calculate achievement percentage
        $achievementPercent = $currentStats['target_capacity'] > 0 
            ? round(($currentStats['submitted_quantity'] / $currentStats['target_capacity']) * 100, 1)
            : 0;
        
        // Determine status
        $status = 'Below Target';
        $statusClass = 'warning';
        $gapText = '';
        $impact = 'Monitor progress closely';
        
        if ($achievementPercent >= 100) {
            $status = 'Target Met';
            $statusClass = 'success';
            $gapText = round($achievementPercent - 100, 1) . '% above target capacity';
            $impact = 'Excellent performance achieved';
        } elseif ($achievementPercent >= 90) {
            $status = 'Near Target';
            $statusClass = 'warning';
            $gapText = round(100 - $achievementPercent, 1) . '% below target capacity';
            $impact = 'Close to target - minor improvement needed';
        } else {
            $gapText = round(100 - $achievementPercent, 1) . '% below target capacity';
            $impact = 'Significant improvement required';
        }
        
        return [
            'title' => $currentContext['title'] . ' Achievement',
            'subtitle' => $currentContext['subtitle'],
            'period_desc' => $currentContext['period_desc'],
            'achievement_percent' => $achievementPercent,
            'target_pcs' => number_format($currentStats['target_capacity'] / 1000000, 1) . 'M',
            'actual_pcs' => number_format($currentStats['submitted_quantity'] / 1000000, 1) . 'M',
            'total_hours' => $this->calculatePeriodHours($currentContext['shift'], $currentContext['cutoff']),
            'lines_active' => $this->getActiveLinesCount($filters),
            'status' => $status,
            'status_class' => $statusClass,
            'gap_text' => $gapText,
            'impact' => $impact
        ];
    }
    
    /**
     * Get current performance monitor data (Right Panel) - Always shows TODAY's full day progress
     */
    private function getCurrentPerformanceMonitor($filters)
    {
        // Always use TODAY's date for Live Performance Monitor (not the filter date)
        $todayDate = today();
        
        // Create TODAY's full day filters (always 00:00-23:59 of TODAY)
        $todayFilters = [
            'dashboard_date' => $todayDate->format('Y-m-d'),
            'dashboard_shift' => 'all', // Always full day
            'dashboard_cutoff' => 'all',
            'dashboard_work_type' => $filters['dashboard_work_type'] ?? 'all'
        ];
        
        // Get TODAY's full day stats
        $todayStats = $this->getEndtimeDashboardStats($todayFilters);
        
        // Calculate progress percentage for TODAY's full day
        $progressPercent = $this->calculateFullDayProgress($todayDate);
        
        // Get performance metrics for TODAY's full day
        $linesRunning = $this->getActiveLinesCount($todayFilters);
        $avgEfficiency = $this->calculateAverageEfficiency($todayFilters);
        $vsTarget = $todayStats['target_capacity'] > 0 
            ? round((($todayStats['submitted_quantity'] / $todayStats['target_capacity']) * 100) - 100, 1)
            : 0;
        $timeRemaining = $this->calculateFullDayRemainingTime($todayDate);
        
        // Get best and worst performing lines for TODAY's full day
        $linePerformance = $this->getTopAndWorstLines($todayFilters);
        
        // Generate AI analysis for TODAY's full day performance
        $aiAnalysis = $this->generateAIAnalysis($todayFilters);
        
        // Determine badge text based on TODAY's time and progress
        $badgeText = $this->getBadgeText($todayDate, $progressPercent);
        
        return [
            'title' => 'Live Performance Monitor',
            'subtitle' => $todayDate->format('M j, Y') . ' - Real-time AI Analysis',
            'period_desc' => 'Full 24-hour period with AI insights',
            'progress_percent' => $progressPercent,
            'lines_running' => $linesRunning['active'] . '/' . $linesRunning['total'],
            'avg_efficiency' => number_format($avgEfficiency, 1) . '%',
            'vs_target' => ($vsTarget >= 0 ? '+' : '') . $vsTarget . '%',
            'vs_target_class' => $vsTarget >= 0 ? 'success' : 'danger',
            'time_remaining' => $timeRemaining,
            'best_line' => $linePerformance['best'],
            'worst_line' => $linePerformance['worst'],
            'ai_recommendation' => $aiAnalysis['recommendation'],
            'ai_alerts' => $aiAnalysis['alerts'],
            'badge_text' => $badgeText,
            'current_time' => now()->format('H:i')
        ];
    }
    
    /**
     * Calculate full day progress percentage
     */
    private function calculateFullDayProgress($date)
    {
        $now = now();
        $targetDate = Carbon::parse($date);
        
        // If the target date is in the future, return 0
        if ($targetDate->isFuture()) {
            return 0;
        }
        
        // If the target date is in the past, return 100
        if ($targetDate->endOfDay()->isPast()) {
            return 100;
        }
        
        // If it's today, calculate progress
        if ($targetDate->isToday()) {
            $startOfDay = $targetDate->copy()->startOfDay();
            $endOfDay = $targetDate->copy()->endOfDay();
            $totalMinutes = $endOfDay->diffInMinutes($startOfDay);
            $elapsedMinutes = $now->diffInMinutes($startOfDay);
            
            return min(round(($elapsedMinutes / $totalMinutes) * 100, 1), 100);
        }
        
        return 0;
    }
    
    /**
     * Calculate remaining time for full day
     */
    private function calculateFullDayRemainingTime($date)
    {
        $now = now();
        $targetDate = Carbon::parse($date);
        
        // If the target date is in the past, no time remaining
        if ($targetDate->endOfDay()->isPast()) {
            return '0.00h';
        }
        
        // If the target date is in the future, show full day
        if ($targetDate->isFuture()) {
            return '24.00h';
        }
        
        // If it's today, calculate remaining time
        if ($targetDate->isToday()) {
            $endOfDay = $targetDate->copy()->endOfDay();
            $remainingMinutes = max($now->diffInMinutes($endOfDay, false), 0);
            $remainingHours = $remainingMinutes / 60;
            
            return number_format($remainingHours, 2) . 'h';
        }
        
        return '0.00h';
    }
    
    /**
     * Generate dynamic AI analysis based on current performance data
     */
    private function generateAIAnalysis($filters)
    {
        // Get current period stats for analysis
        $currentStats = $this->getEndtimeDashboardStats($filters);
        
        // Get line performance data
        $linePerformance = $this->getTopAndWorstLines($filters);
        $avgEfficiency = $this->calculateAverageEfficiency($filters);
        
        // Check if there's any real data to analyze
        $hasData = isset($linePerformance['has_data']) && $linePerformance['has_data'] && $currentStats['submitted_quantity'] > 0;
        
        if (!$hasData) {
            return [
                'recommendation' => [
                    'type' => 'info',
                    'title' => 'No Data Available',
                    'message' => 'No production data available for analysis. Start production activities to see AI-powered insights and recommendations.'
                ],
                'alerts' => [
                    [
                        'type' => 'info',
                        'title' => 'Waiting for Data',
                        'message' => 'AI analysis will begin once production lots are submitted and processed.'
                    ]
                ]
            ];
        }
        
        // Get full day stats for comparison
        $currentDate = Carbon::parse($filters['dashboard_date'] ?? today()->format('Y-m-d'));
        $fullDayFilters = [
            'dashboard_date' => $currentDate->format('Y-m-d'),
            'dashboard_shift' => 'all',
            'dashboard_cutoff' => 'all',
            'dashboard_work_type' => $filters['dashboard_work_type'] ?? 'all'
        ];
        $fullDayStats = $this->getEndtimeDashboardStats($fullDayFilters);
        
        // Calculate key metrics
        $currentProgress = $currentStats['target_capacity'] > 0 
            ? round(($currentStats['submitted_quantity'] / $currentStats['target_capacity']) * 100, 1)
            : 0;
        
        $fullDayProgress = $fullDayStats['target_capacity'] > 0 
            ? round(($fullDayStats['submitted_quantity'] / $fullDayStats['target_capacity']) * 100, 1)
            : 0;
        
        $vsTarget = $currentProgress - 100;
        
        // Generate recommendation
        $recommendation = $this->generateRecommendation($currentProgress, $vsTarget, $avgEfficiency, $currentDate);
        
        // Generate alerts
        $alerts = $this->generateAlerts($linePerformance, $currentProgress, $fullDayProgress, $avgEfficiency);
        
        return [
            'recommendation' => $recommendation,
            'alerts' => $alerts
        ];
    }
    
    /**
     * Generate performance recommendation
     */
    private function generateRecommendation($currentProgress, $vsTarget, $avgEfficiency, $date)
    {
        $recommendation = [];
        
        if ($currentProgress >= 110) {
            $recommendation = [
                'type' => 'success',
                'title' => 'Excellent Performance',
                'message' => "Outstanding progress at {$currentProgress}%. Maintain current efficiency to exceed daily targets significantly."
            ];
        } elseif ($currentProgress >= 100) {
            $recommendation = [
                'type' => 'success', 
                'title' => 'Target Achieved',
                'message' => "Great work! Currently at {$currentProgress}%. Continue current pace to maintain target achievement."
            ];
        } elseif ($currentProgress >= 90) {
            $hoursRemaining = $this->calculateFullDayRemainingTime($date);
            $gapPercent = abs($vsTarget);
            $recommendation = [
                'type' => 'info',
                'title' => 'Near Target',
                'message' => "At {$currentProgress}%, only {$gapPercent}% below target. Focus on efficiency improvements in remaining {$hoursRemaining} to reach 100%."
            ];
        } elseif ($currentProgress >= 70) {
            $gapPercent = abs($vsTarget);
            $recommendation = [
                'type' => 'warning',
                'title' => 'Recovery Needed',
                'message' => "Current pace is {$gapPercent}% below target. Implement efficiency improvements and consider extending operations to recover."
            ];
        } else {
            $gapPercent = abs($vsTarget);
            $recommendation = [
                'type' => 'danger',
                'title' => 'Critical Gap',
                'message' => "Significant shortfall at {$currentProgress}%. Immediate intervention required - review line assignments, equipment status, and resource allocation."
            ];
        }
        
        return $recommendation;
    }
    
    /**
     * Generate performance alerts
     */
    private function generateAlerts($linePerformance, $currentProgress, $fullDayProgress, $avgEfficiency)
    {
        $alerts = [];
        
        // Line performance alerts
        if (isset($linePerformance['worst']) && $linePerformance['worst']) {
            $worstLine = $linePerformance['worst']['line'];
            $worstEfficiency = (float) str_replace(['% efficiency', '%'], '', $linePerformance['worst']['efficiency']);
            
            if ($worstEfficiency < 70) {
                $alerts[] = [
                    'type' => 'danger',
                    'title' => 'Critical Line Alert',
                    'message' => "Line {$worstLine} performance critical at {$worstEfficiency}%. Immediate intervention required to prevent daily target miss."
                ];
            } elseif ($worstEfficiency < 85) {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Line Performance Warning',
                    'message' => "Line {$worstLine} underperforming at {$worstEfficiency}%. Monitor closely and consider maintenance or process adjustments."
                ];
            }
        }
        
        // Best performer recognition
        if (isset($linePerformance['best']) && $linePerformance['best']) {
            $bestLine = $linePerformance['best']['line'];
            $bestEfficiency = (float) str_replace(['% efficiency', '%'], '', $linePerformance['best']['efficiency']);
            
            if ($bestEfficiency > 105) {
                $alerts[] = [
                    'type' => 'success',
                    'title' => 'Excellence Recognition',
                    'message' => "Line {$bestLine} excelling at {$bestEfficiency}%. Consider replicating this line's practices across other production lines."
                ];
            }
        }
        
        // Efficiency alerts
        if ($avgEfficiency < 80) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Overall Efficiency Alert',
                'message' => "Average efficiency at {$avgEfficiency}% is below optimal. Review equipment maintenance schedules and operator training needs."
            ];
        }
        
        // Progress comparison alerts
        $progressDiff = $currentProgress - $fullDayProgress;
        if (abs($progressDiff) > 15) {
            if ($progressDiff > 0) {
                $alerts[] = [
                    'type' => 'info',
                    'title' => 'Period Outperforming',
                    'message' => "Current period is {$progressDiff}% above full day average. Excellent momentum - maintain this pace."
                ];
            } else {
                $alerts[] = [
                    'type' => 'warning',
                    'title' => 'Period Underperforming', 
                    'message' => "Current period is " . abs($progressDiff) . "% below full day average. Focus needed to improve this period's performance."
                ];
            }
        }
        
        // If no alerts generated, add a positive message
        if (empty($alerts) && $currentProgress > 85) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'Steady Performance',
                'message' => "Operations running smoothly. Continue monitoring and maintain current production standards."
            ];
        }
        
        return $alerts;
    }
    
    /**
     * Get dynamic badge text for Live Performance Monitor
     */
    private function getBadgeText($currentDate, $progressPercent)
    {
        $now = now();
        $targetDate = Carbon::parse($currentDate);
        
        if ($targetDate->isToday()) {
            if ($progressPercent >= 100) {
                return 'Day Complete';
            } elseif ($progressPercent >= 75) {
                return 'Late Hours';
            } elseif ($progressPercent >= 50) {
                return 'Mid Day';
            } elseif ($progressPercent >= 25) {
                return 'Morning';
            } else {
                return 'Early Morning';
            }
        } elseif ($targetDate->isFuture()) {
            return 'Future Date';
        } else {
            return 'Full Day';
        }
    }
    
    /**
     * Calculate average efficiency across all lines for current filters
     */
    private function calculateAverageEfficiency($filters)
    {
        $linePerformance = $this->getTopAndWorstLines($filters);
        
        // Check if there's any real data
        if (empty($linePerformance['all_lines']) || !isset($linePerformance['has_data']) || !$linePerformance['has_data']) {
            return 0;
        }
        
        $totalEfficiency = 0;
        $lineCountWithData = 0;
        
        foreach ($linePerformance['all_lines'] as $line) {
            // Only include lines that have actual data
            if (isset($line['has_data']) && $line['has_data']) {
                $efficiency = (float) str_replace(['% efficiency', '%'], '', $line['efficiency']);
                $totalEfficiency += $efficiency;
                $lineCountWithData++;
            }
        }
        
        return $lineCountWithData > 0 ? round($totalEfficiency / $lineCountWithData, 1) : 0;
    }
    
    /**
     * Calculate period hours based on shift and cutoff
     */
    private function calculatePeriodHours($shift, $cutoff)
    {
        if ($shift === 'all') {
            return '24.00h';
        } elseif ($cutoff === 'all') {
            return '12.00h'; // Full shift
        } else {
            // Specific cutoff periods
            $cutoffHours = ['1' => 5, '2' => 4, '3' => 3]; // Approximate hours per cutoff
            return number_format($cutoffHours[$cutoff] ?? 4, 2) . 'h';
        }
    }
    
    /**
     * Get count of active lines
     */
    private function getActiveLinesCount($filters)
    {
        $totalLines = 11; // Lines A through K
        
        // Count lines that have submitted lots (actual production activity)
        $activeLinesQuery = Endtime::query();
        $this->applyDashboardTimeFilter($activeLinesQuery, 
            $filters['dashboard_date'], $filters['dashboard_shift'], $filters['dashboard_cutoff']);
        
        // Only count lines with submitted lots (actual production)
        $activeLinesQuery->where('status', 'Submitted');
        
        if ($filters['dashboard_work_type'] !== 'all') {
            $activeLinesQuery->where('work_type', $filters['dashboard_work_type']);
        }
        
        $activeLines = $activeLinesQuery->distinct('eqp_line')->count('eqp_line');
        
        return [
            'active' => $activeLines,
            'total' => $totalLines
        ];
    }
    
    /**
     * Calculate current progress for ongoing periods
     */
    private function calculateCurrentProgress($context)
    {
        $now = now();
        $currentDate = Carbon::parse($context['date']);
        
        // For demonstration, return a calculated progress based on time
        // In real implementation, this would be more sophisticated
        if ($context['shift'] === 'all') {
            $startOfDay = $currentDate->copy()->startOfDay();
            $endOfDay = $currentDate->copy()->endOfDay();
            $totalMinutes = $endOfDay->diffInMinutes($startOfDay);
            $elapsedMinutes = min($now->diffInMinutes($startOfDay), $totalMinutes);
            return round(($elapsedMinutes / $totalMinutes) * 100, 1);
        }
        
        // For specific shifts/cutoffs, return a reasonable progress estimate
        return min(round(($now->hour / 24) * 100, 1), 100);
    }
    
    /**
     * Calculate remaining time for current period
     */
    private function calculateRemainingTime($context)
    {
        // Simplified calculation - in real implementation this would be more precise
        $hours = $this->calculatePeriodHours($context['shift'], $context['cutoff']);
        $hoursFloat = (float) str_replace('h', '', $hours);
        
        // Estimate remaining time (this is a placeholder)
        $remaining = max($hoursFloat - 2, 0); // Assume 2 hours have passed
        
        return number_format($remaining, 2) . 'h';
    }
    
    /**
     * Get top and worst performing lines
     */
    private function getTopAndWorstLines($filters)
    {
        $lines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
        $linePerformances = [];
        $allLinesData = [];
        $hasAnyData = false;
        
        foreach ($lines as $line) {
            $lineData = $this->getLineDataByFilters($line, $filters);
            
            // Only calculate performance if there's actual submitted data
            if ($lineData['submitted_lots'] > 0 && $lineData['target_capacity'] > 0) {
                $performancePercent = round(($lineData['submitted_lots'] / $lineData['target_capacity']) * 100, 1);
                $hasAnyData = true;
            } else {
                $performancePercent = 0;
            }
            
            $linePerformances[$line] = $performancePercent;
            $allLinesData[$line] = [
                'line' => $line,
                'efficiency' => number_format($performancePercent, 1) . '% efficiency',
                'performance_percent' => $performancePercent,
                'target_capacity' => $lineData['target_capacity'],
                'submitted_lots' => $lineData['submitted_lots'],
                'total_endtime' => $lineData['total_endtime'],
                'has_data' => $lineData['submitted_lots'] > 0
            ];
        }
        
        // If no data at all, return no-data response
        if (!$hasAnyData) {
            return [
                'best' => [
                    'line' => '-',
                    'efficiency' => 'No data available',
                    'status' => 'No Data'
                ],
                'worst' => [
                    'line' => '-',
                    'efficiency' => 'No data available',
                    'status' => 'No Data'
                ],
                'all_lines' => $allLinesData,
                'has_data' => false
            ];
        }
        
        // Filter out lines with no data for best/worst calculation
        $linesWithData = array_filter($linePerformances, function($perf) {
            return $perf > 0;
        });
        
        if (empty($linesWithData)) {
            return [
                'best' => [
                    'line' => '-',
                    'efficiency' => 'No data available',
                    'status' => 'No Data'
                ],
                'worst' => [
                    'line' => '-',
                    'efficiency' => 'No data available',
                    'status' => 'No Data'
                ],
                'all_lines' => $allLinesData,
                'has_data' => false
            ];
        }
        
        // Sort to get best and worst from lines with actual data
        arsort($linesWithData);
        $bestLine = array_key_first($linesWithData);
        $bestEfficiency = reset($linesWithData);
        
        asort($linesWithData);
        $worstLine = array_key_first($linesWithData);
        $worstEfficiency = reset($linesWithData);
        
        return [
            'best' => [
                'line' => $bestLine,
                'efficiency' => number_format($bestEfficiency, 1) . '% efficiency',
                'status' => $bestEfficiency >= 100 ? 'Excellent' : 'Good'
            ],
            'worst' => [
                'line' => $worstLine,
                'efficiency' => number_format($worstEfficiency, 1) . '% efficiency',
                'status' => $worstEfficiency < 70 ? 'Critical' : ($worstEfficiency < 90 ? 'Warning' : 'Monitor')
            ],
            'all_lines' => $allLinesData,
            'has_data' => true
        ];
    }
    
    
    /**
     * Get Per Size Summary data based on dashboard filters
     */
    private function getPerSizeSummary($filters)
    {
        // Frontend display sizes
        $displaySizes = ['0603', '1005', '1608', '2012', '3216', '3225'];
        $summary = [
            'sizes' => $displaySizes,
            'target' => [],
            'endtime' => [],
            'submitted' => [],
            'submitted_percent' => [],
            'endtime_percent' => []
        ];
        
        foreach ($displaySizes as $displaySize) {
            // Convert frontend display size to database value
            $dbSize = $this->convertDisplaySizeToDbSize($displaySize);
            $sizeData = $this->getSizeDataByFilters($dbSize, $filters);
            
            // Target capacity in millions
            $targetCapacity = $sizeData['target_capacity'];
            $summary['target'][$displaySize] = $targetCapacity > 0 ? number_format($targetCapacity / 1000000, 0) . ' M' : '0 M';
            
            // Endtime (total lots) in millions
            $totalEndtime = $sizeData['total_endtime'];
            $summary['endtime'][$displaySize] = $totalEndtime > 0 ? number_format($totalEndtime / 1000000, 0) . ' M' : '0 M';
            
            // Submitted lots in millions
            $submittedLots = $sizeData['submitted_lots'];
            $summary['submitted'][$displaySize] = $submittedLots > 0 ? number_format($submittedLots / 1000000, 0) . ' M' : '0 M';
            
            // Calculate percentages
            $submittedPercent = $totalEndtime > 0 ? round(($submittedLots / $totalEndtime) * 100, 1) : 0;
            $endtimePercent = $targetCapacity > 0 ? round(($totalEndtime / $targetCapacity) * 100, 1) : 0;
            
            $summary['submitted_percent'][$displaySize] = $submittedPercent;
            $summary['endtime_percent'][$displaySize] = $endtimePercent;
        }
        
        return $summary;
    }
    
    /**
     * Convert frontend display size to database size values
     * Frontend: 0603, 1005, 1608, 2012, 3216, 3225
     * Database inconsistency: Equipment uses '3', '5', '10', '21', '31', '32'
     *                         Endtime uses '03', '05', '10'
     */
    private function convertDisplaySizeToDbSize($displaySize)
    {
        $sizeMapping = [
            '0603' => ['03', '3'],
            '1005' => ['05', '5'], 
            '1608' => ['10'],
            '2012' => ['21'],
            '3216' => ['31'],
            '3225' => ['32']
        ];
        
        return $sizeMapping[$displaySize] ?? [$displaySize];
    }
    
    /**
     * Get data for a specific size based on dashboard filters
     */
    private function getSizeDataByFilters($dbSizes, $filters)
    {
        // Extract filters
        $currentDate = $filters['dashboard_date'] ?? today()->format('Y-m-d');
        $currentShift = $filters['dashboard_shift'] ?? 'all';
        $currentCutoff = $filters['dashboard_cutoff'] ?? 'all';
        $workType = $filters['dashboard_work_type'] ?? 'all';
        
        // Get equipment for this size that matches the work type filter
        // Handle multiple possible size values due to database inconsistency
        $equipmentQuery = Equipment::whereIn('lot_size', $dbSizes);
        if ($workType !== 'all') {
            $equipmentQuery->where('work_type', $workType);
        }
        $equipment = $equipmentQuery->get();
        
        // Calculate target capacity for this size (same method as dashboard cards)
        $targetCapacity = 0;
        
        // Calculate time multiplier based on shift and cutoff selection (same as dashboard cards)
        $timeMultiplier = $this->getTimeMultiplier($currentShift, $currentCutoff);
        
        foreach ($equipment as $eq) {
            $oee = floatval($eq->eqp_oee) ?: 0;
            $speed = floatval(str_replace(',', '', $eq->eqp_speed)) ?: 0;
            $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
            
            // Calculate capacity based on time period: OEE × Speed × Operation Time × Time Multiplier
            $periodCapacity = round($oee * $speed * $operationTime * $timeMultiplier);
            $targetCapacity += $periodCapacity;
        }
        
        // Get endtime data for this size using the SAME filters as dashboard cards
        $endtimeQuery = $this->getSizeEndtimeQuery($dbSizes, $currentDate, $currentShift, $currentCutoff, $workType);
        
        // Total endtime for this size (same as card 2)
        $totalEndtime = $endtimeQuery->sum('lot_qty') ?: 0;
        
        // Submitted lots for this size (same as card 3)
        $submittedQuery = clone $endtimeQuery;
        $submittedLots = $submittedQuery->where('status', 'Submitted')->sum('lot_qty') ?: 0;
        
        // Remaining lots for this size (same as card 4)
        $remainingLots = $totalEndtime - $submittedLots;
        
        
        return [
            'target_capacity' => $targetCapacity,
            'total_endtime' => $totalEndtime,
            'submitted_lots' => $submittedLots,
            'remaining_lots' => $remainingLots
        ];
    }
    
    /**
     * Get endtime query for specific size using SAME filters as dashboard cards
     */
    private function getSizeEndtimeQuery($dbSizes, $date, $shift, $cutoff, $workType)
    {
        // Base endtime query for this size (endtime table has lot_size column directly)
        // Handle multiple possible size values due to database inconsistency
        $query = Endtime::whereIn('lot_size', $dbSizes);
        
        // Apply the EXACT SAME time filter logic as dashboard cards
        $this->applyDashboardTimeFilter($query, $date, $shift, $cutoff);
        
        // Apply work type filter if needed
        if ($workType !== 'all') {
            $query->where('work_type', $workType);
        }
        
        return $query;
    }
}
