<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create single admin user - <PERSON> Hapita
        User::create([
            'emp_no' => '21278703',
            'emp_name' => 'HAPITA, GILBERT HIBE',
            'role' => 'ADMIN',
            'position' => 'Specialist',
            'title_class' => 'Professional',
            'rank' => 'CL2(Ⅳ)-3',
            'hr_job_name' => 'Production Management',
            'job_assigned' => 'Leadtime In-charge',
            'password' => Hash::make('password'),
            'emp_verified_at' => now(),
        ]);
    }
}
