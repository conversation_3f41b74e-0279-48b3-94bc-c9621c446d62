<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Endtime>
 */
class EndtimeFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $estEndtime = $this->faker->dateTimeBetween('now', '+7 days');
        $actualSubmittedAt = null;
        $remarks = null;
        $submissionNotes = null;
        $status = 'Ongoing';
        
        // Randomly make some lots submitted
        if ($this->faker->boolean(30)) { // 30% chance of being submitted
            $status = 'Submitted';
            
            // Generate actual submission time around the estimated end time
            $varianceMinutes = $this->faker->numberBetween(-120, 120); // ±2 hours variance
            $actualSubmittedAt = Carbon::instance($estEndtime)->addMinutes($varianceMinutes);
            
            // Calculate remarks based on timing
            $timeDifferenceMinutes = $actualSubmittedAt->diffInMinutes(Carbon::instance($estEndtime), false);
            
            if ($timeDifferenceMinutes > 30) {
                $remarks = 'Early';
            } elseif ($timeDifferenceMinutes < -30) {
                $remarks = 'Delayed';
                $submissionNotes = $this->faker->randomElement([
                    'Equipment maintenance delay',
                    'Material shortage',
                    'Quality issue requiring rework',
                    'Process optimization took longer than expected',
                    'Operator training required',
                ]);
            } else {
                $remarks = 'OK';
            }
        }
        
        return [
            'lot_id' => 'LOT' . $this->faker->unique()->numberBetween(100000, 999999),
            'model_15' => $this->faker->optional(0.8)->regexify('[A-Z]{2}[0-9]{3}[A-Z]{2}'),
            'lot_size' => $this->faker->randomElement(['03', '05', '10', '21', '31']),
            'lot_qty' => $this->faker->numberBetween(1000, 50000),
            'work_type' => $this->faker->randomElement(['NOR', 'OI', 'ADV', 'COMB', 'LY', 'RL', 'PR', 'FSTOP', 'WH']),
            'lot_type' => $this->faker->randomElement(['MAIN', 'RL/LY']),
            'lipas_yn' => $this->faker->randomElement(['Y', 'N']),
            'eqp_1' => 'EQP' . $this->faker->numberBetween(1000, 9999),
            'eqp_2' => $this->faker->optional(0.4)->regexify('EQP[0-9]{4}'),
            'eqp_3' => $this->faker->optional(0.2)->regexify('EQP[0-9]{4}'),
            'eqp_line' => $this->faker->randomElement(['LINE1', 'LINE2', 'LINE3', 'LINE4', 'LINE5']),
            'eqp_area' => $this->faker->randomElement(['AREA_A', 'AREA_B', 'AREA_C', 'AREA_D']),
            'status' => $status,
            'est_endtime' => $estEndtime,
            'actual_submitted_at' => $actualSubmittedAt,
            'modified_by' => $this->faker->name(),
            'remarks' => $remarks,
            'submission_notes' => $submissionNotes,
        ];
    }
    
    /**
     * Create an ongoing lot (not yet submitted)
     */
    public function ongoing(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Ongoing',
            'actual_submitted_at' => null,
            'remarks' => null,
            'submission_notes' => null,
        ]);
    }
    
    /**
     * Create an early submitted lot
     */
    public function early(): static
    {
        return $this->state(function (array $attributes) {
            $estEndtime = Carbon::instance($attributes['est_endtime']);
            $actualSubmittedAt = $estEndtime->copy()->subMinutes($this->faker->numberBetween(35, 120));
            
            return [
                'status' => 'Submitted',
                'actual_submitted_at' => $actualSubmittedAt,
                'remarks' => 'Early',
                'submission_notes' => null,
            ];
        });
    }
    
    /**
     * Create a delayed submitted lot
     */
    public function delayed(): static
    {
        return $this->state(function (array $attributes) {
            $estEndtime = Carbon::instance($attributes['est_endtime']);
            $actualSubmittedAt = $estEndtime->copy()->addMinutes($this->faker->numberBetween(35, 180));
            
            return [
                'status' => 'Submitted',
                'actual_submitted_at' => $actualSubmittedAt,
                'remarks' => 'Delayed',
                'submission_notes' => $this->faker->randomElement([
                    'Equipment breakdown caused delay',
                    'Quality control issues required additional time',
                    'Raw material delivery was late',
                    'Process adjustment needed for quality improvement',
                    'Unexpected maintenance requirement',
                ]),
            ];
        });
    }
    
    /**
     * Create an OK (on-time) submitted lot
     */
    public function onTime(): static
    {
        return $this->state(function (array $attributes) {
            $estEndtime = Carbon::instance($attributes['est_endtime']);
            $actualSubmittedAt = $estEndtime->copy()->addMinutes($this->faker->numberBetween(-25, 25));
            
            return [
                'status' => 'Submitted',
                'actual_submitted_at' => $actualSubmittedAt,
                'remarks' => 'OK',
                'submission_notes' => null,
            ];
        });
    }
}
