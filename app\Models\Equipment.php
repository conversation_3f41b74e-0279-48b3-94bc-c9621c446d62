<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Equipment extends Model
{
    use HasFactory;

    protected $table = 'equipment';

    protected $fillable = [
        'eqp_no',
        'eqp_line',
        'eqp_area',
        'eqp_type',
        'eqp_class',
        'eqp_maker',
        'feeder_type',
        'lot_size',
        'work_type',
        'lot_type',
        'lotqty_alloc',
        'daily_capa',
        'eqp_oee',
        'eqp_speed',
        'operation_time',
        'eqp_code',
        'modified_by',
        'ongoing_lot',
    ];

    protected $casts = [
        // Keeping as strings to preserve formatting (e.g., thousands separators, leading zeros)
    ];
    
    /**
     * Calculate daily capacity dynamically.
     * Formula: eqp_oee * eqp_speed * operation_time
     * Note: eqp_oee is stored as decimal (0.75 = 75%), not percentage
     * 
     * @return float|null
     */
    public function calculateDailyCapacity()
    {
        if (!$this->eqp_oee || !$this->eqp_speed || !$this->operation_time) {
            return null;
        }
        
        $oee = floatval($this->eqp_oee); // Use as decimal directly (0.75 = 75%)
        $speed = floatval(str_replace(',', '', $this->eqp_speed)); // Remove commas
        $operationTime = floatval(str_replace(',', '', $this->operation_time)); // Remove commas
        
        return round($oee * $speed * $operationTime);
    }
    
    /**
     * Calculate rate per minute for endtime calculations.
     * Formula: daily_capacity / 1440 (minutes per day)
     * 
     * @return float
     */
    public function getRatePerMinute()
    {
        $dailyCapacity = $this->daily_capa ?? $this->calculateDailyCapacity();
        
        if (!$dailyCapacity) {
            return 0;
        }
        
        return $dailyCapacity / 1440; // Convert daily capacity to per minute
    }
    
    /**
     * Get formatted daily capacity with thousands separator.
     * 
     * @return string
     */
    public function getFormattedDailyCapacity()
    {
        return number_format($this->daily_capa ?? 0);
    }
}
