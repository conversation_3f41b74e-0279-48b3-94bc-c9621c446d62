# Process Dashboard System

<p align="center">
    <img src="https://img.shields.io/badge/Laravel-12.x-red.svg" alt="Laravel Version">
    <img src="https://img.shields.io/badge/PHP-8.2+-blue.svg" alt="PHP Version">
    <img src="https://img.shields.io/badge/Bootstrap-5.3.7-purple.svg" alt="Bootstrap Version">
    <img src="https://img.shields.io/badge/Tailwind-3.1+-teal.svg" alt="Tailwind CSS">
    <img src="https://img.shields.io/badge/Status-Active-green.svg" alt="Project Status">
</p>

## About Process Dashboard

A comprehensive Work-in-Process (WIP) Management Dashboard built with Laravel 11. This system provides real-time visibility into manufacturing processes, equipment utilization, and workflow optimization. Designed for production environments to track, monitor, and analyze manufacturing operations efficiently.

## 🚀 Key Features

### Endtime Forecasting System (New)
- **Advanced NG% Calculation**: Equipment-specific NG (No Good) percentage handling with 3-step calculation method
- **Parallel Processing Support**: Multiple equipment with different start times and capacities
- **Precise Time Calculation**: Decimal hour handling for accurate endtime predictions down to the minute
- **Real-time Recalculation**: Dynamic endtime updates as equipment assignments and NG% values change
- **Interactive Equipment Assignment**: Up to 10 equipment units with drag-and-drop interface
- **Visual Calculation Breakdown**: Step-by-step display of calculation logic and results
- **Equipment Capacity Display**: Real-time capacity calculations based on OEE, speed, and operation time

### WIP Management
- **Real-time WIP Summary**: Grouped analysis by lot size, code, equipment type, and work type
- **Advanced Filtering**: Multi-dimensional filtering (Hold status, Lot size, Quantity class, Work type, WIP status, Lipas, Auto)
- **Interactive Sorting**: Client-side table sorting for both summary and detail views
- **Detailed Lot View**: Modal with individual lot details including stagnation analysis
- **Print Functionality**: Individual lot report generation with professional formatting
- **Average TAT Tracking**: Turnaround time analysis and monitoring

### Equipment Management
- **Equipment Master Data**: Comprehensive equipment specifications and configurations
- **Ongoing Lot Tracking**: Real-time tracking of which lot is currently on each equipment
- **CSV Import**: Bulk equipment data import from CSV files
- **Equipment Classification**: By type, class, maker, feeder type, and operational parameters

### User Management
- **Role-based Access**: Admin and User roles with appropriate permissions
- **Excel Import**: Bulk user import from Excel files
- **Authentication**: Secure login system with employee number integration
- **Admin Controls**: Equipment and WIP data management restricted to admins

### Data Import & Export
- **CSV Support**: Equipment and WIP data import from CSV files
- **Excel Integration**: User data import from Excel files
- **Data Validation**: Robust validation and error handling for imports
- **BOM Handling**: Proper handling of CSV files with BOM encoding

## 🛠️ Technology Stack

- **Backend**: Laravel 12.x (PHP 8.2+)
- **Frontend**: Bootstrap 5.3.7 with FontAwesome icons
- **CSS Framework**: Tailwind CSS 3.1+ with Alpine.js 3.4+
- **Database**: MySQL/MariaDB
- **Authentication**: Laravel Breeze with custom employee-based authentication
- **Build Tool**: Vite 7.0+ for asset compilation
- **Charts**: ApexCharts 5.3+ and Chart.js 4.5+
- **UI Libraries**: SweetAlert2 11.22+ for notifications
- **JavaScript**: Vanilla JS with Alpine.js for reactive components

## 📋 Database Schema

### Core Tables
- **users**: User management with role-based access
- **equipment**: Equipment master data with OEE, speed, and operation time for capacity calculations
- **endtime**: Lot endtime forecasting with equipment assignments and NG% tracking
- **updatewip**: Work-in-process records with stagnation analysis
- **products**: Product catalog (legacy/future use)
- **orders**: Order management system

### Key Endtime Table Schema
```sql
-- Equipment assignments (up to 10 units)
eqp_1 to eqp_10 VARCHAR(255)           -- Equipment numbers
start_time_1 to start_time_10 DATETIME  -- Start times for each equipment
ng_percent_1 to ng_percent_10 DECIMAL   -- NG percentages for rework calculation

-- Calculation results
est_endtime DATETIME                    -- Calculated end time
lot_qty INTEGER                         -- Lot quantity
status VARCHAR(50)                      -- Processing status
```

## 🎨 UI/UX Improvements

### Endtime Forecasting Interface
- **Advanced Modal Design**: 
  - Equipment assignment interface with real-time capacity display
  - NG% input fields with instant calculation updates
  - Step-by-step calculation breakdown with visual indicators
  - Equipment search and selection with autocomplete

- **Calculation Display**:
  - 3-step calculation breakdown (Initial → NG → Final)
  - Equipment-specific NG piece contributions
  - Real-time endtime updates with relative time display
  - Visual progress indicators and capacity utilization charts

### WIP Management Interface
- **Enhanced Modal Design**: 
  - Fixed width (1200px, 90% max-width) and height (80vh)
  - Scrollable content area with proper overflow handling
  - Professional header and footer sections

- **Table Enhancements**:
  - Row numbering in modal details
  - Individual lot print functionality
  - Interactive column sorting (both summary and details)
  - Improved Code column styling with bold text and background

- **Filter Integration**:
  - Fixed data mismatch between summary and modal details
  - Consistent filter application across all views
  - Always-visible filter section for quick access

- **Visual Indicators**:
  - Color-coded TAT badges (green < 15 days, yellow 15-30 days, red > 30 days)
  - Sorting arrows with different colors (green for summary, yellow for modal)
  - Professional gradients and modern card layouts

## 🔧 Installation & Setup

### Prerequisites
- PHP 8.2 or higher
- Composer
- MySQL/MariaDB
- Node.js & NPM (for Vite asset compilation)
- Windows Server/IIS or Apache/Nginx

### Installation Steps

1. **Clone the repository**
```bash
git clone <repository-url>
cd process-dashboard
```

2. **Install dependencies**
```bash
composer install
npm install
npm run build
```

**Note for Windows/IIS**: Ensure proper permissions are set for the storage and bootstrap/cache directories.

3. **Environment setup**
```bash
cp .env.example .env
php artisan key:generate
```

4. **Configure database**
- Update `.env` with your database credentials
- Ensure MySQL/MariaDB is running

5. **Run migrations and seeders**
```bash
php artisan migrate:fresh --seed
```

6. **Prepare CSV files** (optional)
- Place `equipment.csv` in project root for equipment data
- Place `updatewip.csv` in project root for WIP data
- Place Excel file for user data import

7. **Start the application**

**Development:**
```bash
php artisan serve
# or use the custom composer script
composer run serve
```

**Production (Windows/IIS):**
- Configure IIS to point to the `/public` directory
- Ensure URL Rewrite module is installed
- Set appropriate file permissions

## 📊 Data Management

### CSV File Formats

#### equipment.csv
Required columns: `eqp_no`, `eqp_line`, `eqp_area`, `eqp_type`, `eqp_class`, `eqp_maker`, `feeder_type`, `lot_size`, `work_type`, `lot_type`, `lotqty_alloc`, `eqp_oee`, `eqp_speed`, `operation_time`, `eqp_code`, `modified_by`, `ongoing_lot`

#### updatewip.csv
WIP data with lot tracking, quantities, TAT, and location information

### Default Admin User
- **Employee No**: 21278703 (Gilbert Hapita)
- **Employee Name**: HAPITA, GILBERT HIBE
- **Password**: password (default - should be changed after first login)
- **Role**: ADMIN
- **Position**: Specialist - Production Management

## 🎯 Usage

### WIP Management Workflow
1. **Apply Filters**: Use the filter section to narrow down WIP data
2. **Sort Data**: Click column headers to sort by different criteria
3. **View Details**: Click the eye icon to see individual lot details
4. **Print Reports**: Use the print button for individual lot reports
5. **Analyze TAT**: Monitor average turnaround times and identify bottlenecks

### Equipment Management
- Equipment data automatically imported from CSV
- Track ongoing lots on each piece of equipment
- Monitor equipment utilization and allocation

## 🔒 Security

- Role-based access control (Admin/User)
- Data modification restricted to Admin users
- Secure authentication with Laravel Breeze
- Input validation and sanitization

## 🧪 Testing

Run the test suite:
```bash
php artisan test
```

## 📈 Performance

- Optimized database queries with proper indexing
- Client-side sorting for improved responsiveness
- Efficient AJAX loading for modal details
- Responsive design for various screen sizes

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 Changelog

### 2025-09-09 - System Update & Maintenance
- 📊 **Dashboard Enhancement**: Real-time auto-refresh functionality with configurable intervals
- 🎯 **Production Overview**: Advanced capacity tracking with equipment count monitoring
- 🔧 **Chart Integration**: ApexCharts and Chart.js integration for manufacturing analytics
- 🎨 **UI Improvements**: Enhanced header controls with time, shift, and cutoff filters
- 🔐 **Permission System**: Comprehensive role-based access control implementation
- 📱 **Responsive Design**: Improved mobile and tablet compatibility
- 🚀 **Performance**: Optimized AJAX data loading and real-time updates
- 🗃️ **Database**: Consolidated endtime table with enhanced performance indexes

### 2025-09-04 - Endtime Forecasting System v2.1
- ✨ **Critical Bug Fix**: Fixed JavaScript decimal hour truncation in time calculations
- ✨ **Enhanced NG% Logic**: Implemented equipment-based NG piece calculation method
- ✨ **Real-time Recalculation**: View details now shows live recalculated values
- ✨ **Precision Improvements**: Endtime accuracy down to the minute
- 🐛 **Time Sync Fix**: Resolved 41-minute discrepancy in modal vs table display
- 📊 **Calculation Breakdown**: Added step-by-step NG% calculation display
- 🔍 **Equipment Search**: Enhanced equipment selection with autocomplete

### 2025-09-02 - Endtime Forecasting System v2.0
- ✨ **New Feature**: Complete endtime forecasting system with NG% support
- ✨ **Parallel Processing**: Multiple equipment with different start times
- ✨ **Equipment Assignment**: Up to 10 equipment units per lot
- ✨ **NG% Calculation**: Quality rework impact on processing time
- 🗺️ **Database Schema**: Added endtime table with equipment assignments
- 🎨 **Modern UI**: Professional modal design with real-time updates

### 2025-08-29 - Major UI/UX Enhancements
- ✨ Enhanced modal design with fixed sizing and scrolling
- ✨ Added row numbering and individual lot print functionality
- ✨ Implemented interactive table sorting for all views
- ✨ Fixed filter consistency between summary and detail views
- ✨ Improved Code column styling with better visibility
- 🗺️ Added `ongoing_lot` column to equipment table
- 🎨 Enhanced visual indicators and professional styling
- 🐛 Fixed data mismatch issues between views

### Previous Updates
- 🏗️ Initial WIP management system implementation
- 🔐 User authentication and role management
- 📊 Equipment and WIP data import capabilities
- 🎨 Bootstrap-based responsive UI

## 🐛 Known Issues

None at this time. Please report any issues through the issue tracker.

## 📞 Support

For technical support or questions about the Process Dashboard System, please contact the development team.

---

**Built with Laravel 11** | **Powered by Bootstrap 5** | **Enhanced with FontAwesome**
