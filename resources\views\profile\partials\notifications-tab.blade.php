<!-- Notifications Tab -->
<div class="tab-pane fade" id="notifications" role="tabpanel">
    <h5 class="mb-4">Configure Notifications</h5>
    <p class="text-muted mb-4">By configuring notifications, users can tailor their experience to receive alerts for the types of events that matter to them.</p>
    
    <form method="post" action="{{ route('profile.update') }}">
        @csrf
        @method('patch')
        
        <!-- In-App Notifications -->
        <div class="card border mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-bell me-2 text-primary"></i>In-App Notifications
                </h6>
                <small class="text-muted">Alerts that appear within the application interface.</small>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="orderUpdates" name="notifications[order_updates]" checked>
                            <label class="form-check-label" for="orderUpdates">Order Updates</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="productAlerts" name="notifications[product_alerts]" checked>
                            <label class="form-check-label" for="productAlerts">Product Alerts</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="systemAlerts" name="notifications[system_alerts]" checked>
                            <label class="form-check-label" for="systemAlerts">System Alerts</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="securityAlerts" name="notifications[security_alerts]" checked>
                            <label class="form-check-label" for="securityAlerts">Security Alerts</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Email Notifications -->
        <div class="card border mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-envelope me-2 text-success"></i>Email Notifications
                </h6>
                <small class="text-muted">Messages sent to your email address.</small>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="emailOrders" name="email_notifications[orders]">
                            <label class="form-check-label" for="emailOrders">Order Confirmations</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="emailMarketing" name="email_notifications[marketing]">
                            <label class="form-check-label" for="emailMarketing">Marketing Updates</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="emailSecurity" name="email_notifications[security]" checked>
                            <label class="form-check-label" for="emailSecurity">Security Alerts</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="emailWeekly" name="email_notifications[weekly]">
                            <label class="form-check-label" for="emailWeekly">Weekly Reports</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Push Notifications -->
        <div class="card border mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-mobile-alt me-2 text-info"></i>Push Notifications
                </h6>
                <small class="text-muted">Alerts sent to your mobile device or desktop.</small>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="pushOrders" name="push_notifications[orders]">
                            <label class="form-check-label" for="pushOrders">Order Updates</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="pushMessages" name="push_notifications[messages]">
                            <label class="form-check-label" for="pushMessages">New Messages</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- SMS Notifications -->
        <div class="card border mb-4">
            <div class="card-header bg-light">
                <h6 class="mb-0">
                    <i class="fas fa-sms me-2 text-warning"></i>SMS Notifications
                </h6>
                <small class="text-muted">Text messages sent to your mobile phone.</small>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="smsOrders" name="sms_notifications[orders]">
                            <label class="form-check-label" for="smsOrders">Order Updates</label>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="smsSecurity" name="sms_notifications[security]">
                            <label class="form-check-label" for="smsSecurity">Security Alerts</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-2"></i>Save Notification Preferences
        </button>
    </form>
</div>