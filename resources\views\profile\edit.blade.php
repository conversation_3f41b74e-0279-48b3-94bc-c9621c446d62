<x-app-layout>
    <x-slot name="header">Profile Settings</x-slot>

    <!-- Profile Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body p-4">
                    <div class="d-flex align-items-center">
                        <div class="profile-avatar me-4">
                            <div class="avatar-wrapper position-relative">
                                @if(Auth::user()->avatar)
                                    <img src="{{ asset('storage/' . Auth::user()->avatar) }}" alt="Profile Picture" class="rounded-circle" width="100" height="100" style="object-fit: cover;">
                                @else
                                    <div class="rounded-circle d-flex align-items-center justify-content-center" style="width: 100px; height: 100px; background-color: #6366f1; color: white; font-weight: 600; font-size: 36px;">
                                        {{ strtoupper(substr(Auth::user()->emp_name, 0, 1)) }}
                                    </div>
                                @endif
                                <button class="btn position-absolute bottom-0 end-0 rounded-circle d-flex align-items-center justify-content-center" 
                                        style="width: 32px; height: 32px; background-color: white; border: 2px solid #e2e8f0; box-shadow: 0 2px 8px rgba(0,0,0,0.15); color: #6366f1;" 
                                        data-bs-toggle="modal" data-bs-target="#avatarModal">
                                    <i class="fas fa-camera" style="font-size: 12px;"></i>
                                </button>
                            </div>
                        </div>
                        <div class="profile-info">
                            <h3 class="mb-1">{{ Auth::user()->emp_name }}</h3>
                            <p class="text-muted mb-2">Employee No: {{ Auth::user()->emp_no }}</p>
                            <div class="mb-2">
                                <span class="badge bg-{{ Auth::user()->role === 'ADMIN' ? 'primary' : (Auth::user()->role === 'MANAGER' ? 'warning' : 'success') }} px-3 py-2">
                                    {{ Auth::user()->role }}
                                </span>
                                @if(Auth::user()->position)
                                    <span class="badge bg-secondary px-3 py-2 ms-2">
                                        {{ Auth::user()->position }}
                                    </span>
                                @endif
                            </div>
                            @if(Auth::user()->job_assigned)
                                <p class="text-muted mb-0 small">{{ Auth::user()->job_assigned }}</p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Settings Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <ul class="nav nav-pills nav-justified" id="profileTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="account-tab" data-bs-toggle="pill" data-bs-target="#account" type="button" role="tab">
                                <i class="fas fa-user me-2"></i>Account
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="pill" data-bs-target="#security" type="button" role="tab">
                                <i class="fas fa-shield-alt me-2"></i>Security
                            </button>
                        </li>
                        <!-- <li class="nav-item" role="presentation">
                            <button class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" type="button" role="tab">
                                <i class="fas fa-bell me-2"></i>Notifications
                            </button>
                        </li> -->
                    </ul>
                </div>
                <div class="card-body p-4">
                    <div class="tab-content" id="profileTabsContent">
                        @include('profile.partials.account-tab')
                        @include('profile.partials.security-tab')
                        @include('profile.partials.notifications-tab')
                    </div>
                </div>
            </div>
        </div>
    </div>

    @include('profile.partials.modals')
    @include('profile.partials.scripts')
</x-app-layout>
