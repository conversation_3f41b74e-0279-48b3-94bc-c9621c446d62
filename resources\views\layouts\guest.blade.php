<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Local Fonts -->
    <link href="{{ asset('fonts/inter.css') }}" rel="stylesheet">
    
    <!-- Local Icons -->
    <link rel="stylesheet" href="{{ asset('libs/fontawesome/css/all.min.css') }}">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body>
    <div class="auth-cover-page d-flex align-items-center">
        <div class="container-fluid">
            <div class="row g-0 min-vh-100">
                <!-- Form Section -->
                <div class="col-xl-7 col-lg-6 d-flex align-items-center justify-content-center">
                    <div class="auth-card rounded-3 p-4 p-lg-5 w-100" style="max-width: 500px;">
                        <div class="d-lg-none text-center mb-4">
                            <a href="/" class="text-decoration-none">
                                <h4 class="fw-bold text-primary mb-0">
                                    <i class="fas fa-chart-line me-2"></i>
                                    {{ config('app.name') }}
                                </h4>
                            </a>
                        </div>
                        {{ $slot }}
                    </div>
                </div>
                
                <!-- Welcome Section -->
                <div class="col-xl-5 col-lg-6 d-none d-lg-block">
                    <div class="auth-welcome-section h-100 d-flex flex-column justify-content-center align-items-center text-center p-5 position-relative">
                        <div class="position-relative z-index-2">
                            <div class="mb-4">
                                <i class="fas fa-chart-line fa-4x mb-3" style="color: rgba(255,255,255,0.9);"></i>
                            </div>
                            <h2 class="fw-bold mb-3">Welcome to Visual Inspection {{ config('app.name') }}</h2>
                            <p class="fs-5 mb-4" style="color: rgba(255,255,255,0.9);">
                                
                            </p>
                            <div class="d-flex justify-content-center">
                                <div class="bg-white bg-opacity-20 rounded-circle p-4">
                                    <i class="fas fa-dashboard fa-2x" style="color: rgba(255,255,255,0.9);"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .auth-cover-page {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        .auth-cover-page::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff08" points="0,1000 1000,0 1000,1000"/></svg>') no-repeat;
            background-size: cover;
        }
        .auth-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .auth-welcome-section {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            position: relative;
            overflow: hidden;
        }
        .auth-welcome-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="2" fill="%23ffffff20"/><circle cx="75" cy="75" r="1" fill="%23ffffff15"/><circle cx="50" cy="10" r="1.5" fill="%23ffffff25"/></svg>') repeat;
            animation: float 6s ease-in-out infinite;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        .social-btn {
            transition: all 0.3s ease;
        }
        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .form-control {
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .auth-brand-side {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            align-items: center;
            justify-content: center;
        }
        .auth-form-side {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 2rem 1rem;
        }
        @media (max-width: 991.98px) {
            .auth-form-side {
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }
        }
    </style>
</body>
</html>
