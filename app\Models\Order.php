<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Order extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number',
        'user_id',
        'total_amount',
        'total_items',
        'status',
        'notes',
        'order_date'
    ];

    protected $casts = [
        'total_amount' => 'decimal:2',
        'total_items' => 'integer',
        'order_date' => 'datetime',
    ];

    /**
     * Get the user who placed this order
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the order items for this order
     */
    public function orderItems()
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the products for this order through order items
     */
    public function products()
    {
        return $this->belongsToMany(Product::class, 'order_items')
                    ->withPivot('quantity', 'price', 'total')
                    ->withTimestamps();
    }

    /**
     * Generate order number
     */
    public static function generateOrderNumber()
    {
        return 'SPK' . str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            'pending' => 'bg-warning',
            'paid' => 'bg-success',
            'shipped' => 'bg-info',
            'delivered' => 'bg-primary',
            'cancelled' => 'bg-danger',
            default => 'bg-secondary'
        };
    }
}
