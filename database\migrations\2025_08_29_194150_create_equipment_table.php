<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('equipment', function (Blueprint $table) {
            $table->id();
            $table->string('eqp_no')->unique();
            $table->string('eqp_line');
            $table->string('eqp_area');
            $table->string('eqp_type');
            $table->string('eqp_class');
            $table->string('eqp_maker');
            $table->string('feeder_type');
            $table->string('lot_size');
            $table->string('work_type');
            $table->string('lot_type');
            $table->string('lotqty_alloc');
            $table->decimal('eqp_oee', 5, 2)->nullable();
            $table->integer('eqp_speed')->nullable();
            $table->integer('operation_time')->nullable();
            $table->string('eqp_code');
            $table->string('modified_by')->nullable();
            $table->string('ongoing_lot')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('equipment');
    }
};
