<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\OrderItem;

class ReportTest extends TestCase
{
    use RefreshDatabase;

    public function test_admin_can_access_reports_index(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)->get(route('reports.index'));
        
        $response->assertStatus(200);
        $response->assertViewIs('reports.index');
    }

    public function test_regular_user_cannot_access_reports(): void
    {
        $user = User::factory()->create(['role' => 'user']);
        
        $response = $this->actingAs($user)->get(route('reports.index'));
        
        $response->assertStatus(403);
    }

    public function test_admin_can_access_sales_report(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)->get(route('reports.sales'));
        
        $response->assertStatus(200);
        $response->assertViewIs('reports.sales');
    }

    public function test_admin_can_access_inventory_report(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)->get(route('reports.inventory'));
        
        $response->assertStatus(200);
        $response->assertViewIs('reports.inventory');
    }

    public function test_admin_can_access_user_analytics(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)->get(route('reports.users'));
        
        $response->assertStatus(200);
        $response->assertViewIs('reports.users');
    }

    public function test_admin_can_export_sales_report(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Create some test data
        $customer = User::factory()->create(['role' => 'user']);
        $product = Product::factory()->create();
        $order = Order::factory()->create([
            'user_id' => $customer->id,
            'status' => 'paid'
        ]);
        OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id
        ]);
        
        $response = $this->actingAs($admin)->get(route('reports.export.sales'));
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
    }

    public function test_admin_can_export_inventory_report(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Create some test products
        Product::factory()->count(3)->create();
        
        $response = $this->actingAs($admin)->get(route('reports.export.inventory'));
        
        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'text/csv; charset=UTF-8');
    }

    public function test_sales_report_with_filters(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)->get(route('reports.sales', [
            'start_date' => now()->startOfMonth()->format('Y-m-d'),
            'end_date' => now()->endOfMonth()->format('Y-m-d'),
            'period' => 'daily'
        ]));
        
        $response->assertStatus(200);
        $response->assertViewHas(['salesData', 'topProducts', 'salesSummary']);
    }

    public function test_inventory_report_with_filters(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        // Create products with different categories and stock levels
        Product::factory()->create(['category' => 'Electronics', 'stock' => 5]);
        Product::factory()->create(['category' => 'Books', 'stock' => 0]);
        
        $response = $this->actingAs($admin)->get(route('reports.inventory', [
            'category' => 'Electronics',
            'low_stock' => 1
        ]));
        
        $response->assertStatus(200);
        $response->assertViewHas(['products', 'inventorySummary']);
    }

    public function test_user_analytics_with_date_range(): void
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $response = $this->actingAs($admin)->get(route('reports.users', [
            'start_date' => now()->startOfMonth()->format('Y-m-d'),
            'end_date' => now()->endOfMonth()->format('Y-m-d')
        ]));
        
        $response->assertStatus(200);
        $response->assertViewHas(['userStats', 'topCustomers', 'registrationTrend']);
    }
}
