<x-app-layout>
    <x-slot name="header">
        Financial Analytics
    </x-slot>

    <!-- Financial Overview Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success bg-opacity-10 text-success me-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($financialData['revenue_analysis']['current_month'], 2) }}</h3>
                            <p class="text-muted mb-0">Current Month</p>
                            @php
                                $monthGrowth = $financialData['revenue_analysis']['last_month'] > 0 ? 
                                    (($financialData['revenue_analysis']['current_month'] - $financialData['revenue_analysis']['last_month']) / $financialData['revenue_analysis']['last_month']) * 100 : 0;
                            @endphp
                            <small class="text-{{ $monthGrowth >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $monthGrowth >= 0 ? 'up' : 'down' }}"></i>
                                {{ abs(number_format($monthGrowth, 1)) }}% vs last month
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary me-3">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($financialData['revenue_analysis']['year_to_date'], 2) }}</h3>
                            <p class="text-muted mb-0">Year to Date</p>
                            @php
                                $yearGrowth = $financialData['revenue_analysis']['last_year'] > 0 ? 
                                    (($financialData['revenue_analysis']['year_to_date'] - $financialData['revenue_analysis']['last_year']) / $financialData['revenue_analysis']['last_year']) * 100 : 0;
                            @endphp
                            <small class="text-{{ $yearGrowth >= 0 ? 'success' : 'danger' }}">
                                <i class="fas fa-arrow-{{ $yearGrowth >= 0 ? 'up' : 'down' }}"></i>
                                {{ abs(number_format($yearGrowth, 1)) }}% vs last year
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning bg-opacity-10 text-warning me-3">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($financialData['profit_margins']['profit_margin'], 1) }}%</h3>
                            <p class="text-muted mb-0">Profit Margin</p>
                            <small class="text-warning">Estimated margin</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info bg-opacity-10 text-info me-3">
                            <i class="fas fa-crystal-ball"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($financialData['forecasting']['next_month_forecast'], 2) }}</h3>
                            <p class="text-muted mb-0">Next Month Forecast</p>
                            <small class="text-info">Projected revenue</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <!-- Financial Trends -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Revenue Trends (Last 12 Months)</h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px;">
                        <canvas id="financialTrendsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Breakdown -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Revenue vs Cost Breakdown</h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px;">
                        <canvas id="revenueBreakdownChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Financial Details -->
    <div class="row g-4 mb-4">
        <!-- Profit Analysis -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Profit Analysis</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="mb-3">
                                <h4 class="text-primary mb-1">${{ number_format($financialData['profit_margins']['total_revenue'], 2) }}</h4>
                                <small class="text-muted">Total Revenue</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="mb-3">
                                <h4 class="text-warning mb-1">${{ number_format($financialData['profit_margins']['estimated_cost'], 2) }}</h4>
                                <small class="text-muted">Estimated Cost</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="mb-3">
                                <h4 class="text-success mb-1">${{ number_format($financialData['profit_margins']['estimated_profit'], 2) }}</h4>
                                <small class="text-muted">Estimated Profit</small>
                            </div>
                        </div>
                    </div>
                    
                    @php
                        $profitPercent = $financialData['profit_margins']['total_revenue'] > 0 ? 
                            ($financialData['profit_margins']['estimated_profit'] / $financialData['profit_margins']['total_revenue']) * 100 : 0;
                    @endphp
                    
                    <div class="progress mt-3" style="height: 20px; --profit-width: {{ number_format($profitPercent, 2) }}%;">
                        <div class="progress-bar bg-success" style="width: var(--profit-width)">
                            {{ number_format($profitPercent, 1) }}% Profit
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h6>Key Metrics:</h6>
                        <div class="row">
                            <div class="col-6">
                                <small class="text-muted">Gross Margin:</small>
                                <div class="fw-bold">{{ number_format($financialData['profit_margins']['profit_margin'], 2) }}%</div>
                            </div>
                            <div class="col-6">
                                <small class="text-muted">Cost Ratio:</small>
                                <div class="fw-bold">{{ number_format(100 - $financialData['profit_margins']['profit_margin'], 2) }}%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Revenue Forecast -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Revenue Forecast</h5>
                </div>
                <div class="card-body">
                    <div class="forecast-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-0">Next Month</h6>
                            <small class="text-muted">Based on 3-month average</small>
                        </div>
                        <div class="text-end">
                            <h5 class="mb-0 text-primary">${{ number_format($financialData['forecasting']['next_month_forecast'], 2) }}</h5>
                        </div>
                    </div>
                    
                    <div class="forecast-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-0">Next Quarter</h6>
                            <small class="text-muted">3-month projection</small>
                        </div>
                        <div class="text-end">
                            <h5 class="mb-0 text-success">${{ number_format($financialData['forecasting']['quarterly_forecast'], 2) }}</h5>
                        </div>
                    </div>
                    
                    <div class="forecast-item d-flex justify-content-between align-items-center mb-3 p-3 bg-light rounded">
                        <div>
                            <h6 class="mb-0">Annual Projection</h6>
                            <small class="text-muted">12-month forecast</small>
                        </div>
                        <div class="text-end">
                            <h5 class="mb-0 text-info">${{ number_format($financialData['forecasting']['yearly_forecast'], 2) }}</h5>
                        </div>
                    </div>
                    
                    <div class="alert alert-info mt-3">
                        <small>
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Note:</strong> Forecasts are based on historical trends and may vary based on market conditions.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Analytics -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Payment & Performance Metrics</h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success mb-1">{{ number_format($financialData['payment_analytics']['payment_success_rate']) }}%</h4>
                                <small class="text-muted">Payment Success Rate</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-primary mb-1">{{ $financialData['payment_analytics']['average_processing_time'] }}</h4>
                                <small class="text-muted">Avg Processing Time</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-info mb-1">${{ number_format($financialData['financial_trends']->avg('avg_order_value'), 2) }}</h4>
                                <small class="text-muted">Avg Order Value</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-warning mb-1">{{ number_format($financialData['financial_trends']->avg('orders')) }}</h4>
                            <small class="text-muted">Avg Monthly Orders</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Back Navigation -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <a href="{{ route('management.analytics.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Analytics Overview
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
        
        .forecast-item {
            transition: all 0.3s ease;
        }
        
        .forecast-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }
    </style>

    @php
        $trendsLabels = $financialData['financial_trends']->map(function($item) {
            return $item->year . '-' . str_pad($item->month, 2, '0', STR_PAD_LEFT);
        });
        $trendsData = $financialData['financial_trends']->pluck('revenue');
        $avgOrderValues = $financialData['financial_trends']->pluck('avg_order_value');
        $profitData = $financialData['profit_margins']['estimated_profit'];
        $costData = $financialData['profit_margins']['estimated_cost'];
    @endphp

    <div id="chart-data" 
         data-trends-data="{{ json_encode($trendsData) }}"
         data-trends-labels="{{ json_encode($trendsLabels) }}"
         data-avg-order-values="{{ json_encode($avgOrderValues) }}"
         data-profit-data="{{ json_encode($profitData) }}"
         data-cost-data="{{ json_encode($costData) }}"
         style="display: none;"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded');
                return;
            }

            // Get data from data attributes
            const chartDataElement = document.getElementById('chart-data');
            const trendsData = JSON.parse(chartDataElement.getAttribute('data-trends-data'));
            const trendsLabels = JSON.parse(chartDataElement.getAttribute('data-trends-labels'));
            const avgOrderValues = JSON.parse(chartDataElement.getAttribute('data-avg-order-values'));
            const profitData = parseFloat(chartDataElement.getAttribute('data-profit-data'));
            const costData = parseFloat(chartDataElement.getAttribute('data-cost-data'));

            // Financial Trends Chart
            const trendsCtx = document.getElementById('financialTrendsChart');
            if (trendsCtx) {
                
                new Chart(trendsCtx, {
                    type: 'line',
                    data: {
                        labels: trendsLabels,
                        datasets: [{
                            label: 'Revenue ($)',
                            data: trendsData,
                            borderColor: 'rgb(99, 102, 241)',
                            backgroundColor: 'rgba(99, 102, 241, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            yAxisID: 'y'
                        }, {
                            label: 'Avg Order Value ($)',
                            data: avgOrderValues,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false,
                                },
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toFixed(0);
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Revenue Breakdown Chart
            const breakdownCtx = document.getElementById('revenueBreakdownChart');
            if (breakdownCtx) {
                const revenueData = [
                    profitData,
                    costData
                ];
                
                new Chart(breakdownCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Estimated Profit', 'Estimated Cost'],
                        datasets: [{
                            data: revenueData,
                            backgroundColor: [
                                '#22c55e', // Green for profit
                                '#f59e0b'  // Orange for cost
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            },
                            tooltip: {
                                callbacks: {
                                    label: function(context) {
                                        const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                        const percentage = ((context.parsed * 100) / total).toFixed(1);
                                        return context.label + ': $' + context.parsed.toLocaleString() + ' (' + percentage + '%)';
                                    }
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</x-app-layout>