<x-app-layout>
    <x-slot name="header">
        Edit Order
    </x-slot>

    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Edit Order {{ $order->order_number }}</h4>
                    <p class="text-muted mb-0">Update order status and information</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('orders.show', $order) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Order
                    </a>
                    <a href="{{ route('orders.index') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>All Orders
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Order Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('orders.update', $order) }}" method="POST">
                        @csrf
                        @method('PUT')
                        
                        <!-- Order Details (Read-only) -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label text-muted">Order Number</label>
                                <p class="fw-bold">{{ $order->order_number }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">Customer</label>
                                <p class="fw-bold">{{ $order->user->name }} ({{ $order->user->email }})</p>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label text-muted">Total Items</label>
                                <p class="fw-bold">{{ $order->total_items }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">Total Amount</label>
                                <p class="fw-bold text-success">${{ number_format($order->total_amount, 2) }}</p>
                            </div>
                        </div>

                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label text-muted">Order Date</label>
                                <p class="fw-bold">{{ $order->created_at->format('M d, Y \a\t g:i A') }}</p>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label text-muted">Current Status</label>
                                <p class="mb-0">
                                    <span class="badge {{ $order->getStatusBadgeClass() }} fs-6">
                                        {{ ucfirst($order->status) }}
                                    </span>
                                </p>
                            </div>
                        </div>

                        <hr class="my-4">

                        <!-- Editable Fields -->
                        <div class="mb-4">
                            <label for="status" class="form-label">Order Status <span class="text-danger">*</span></label>
                            <select class="form-select @error('status') is-invalid @enderror" 
                                    id="status" name="status" required>
                                @if(Auth::user()->canManageOrders())
                                    <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="paid" {{ $order->status === 'paid' ? 'selected' : '' }}>Paid</option>
                                    <option value="shipped" {{ $order->status === 'shipped' ? 'selected' : '' }}>Shipped</option>
                                    <option value="delivered" {{ $order->status === 'delivered' ? 'selected' : '' }}>Delivered</option>
                                    <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                @else
                                    <option value="pending" {{ $order->status === 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="cancelled" {{ $order->status === 'cancelled' ? 'selected' : '' }}>Cancelled</option>
                                    @if(in_array($order->status, ['paid', 'shipped', 'delivered']))
                                        <option value="{{ $order->status }}" selected>{{ ucfirst($order->status) }}</option>
                                    @endif
                                @endif
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <div class="row mt-2">
                                    <div class="col-12">
                                        <small class="text-muted">
                                            <strong>Status Guide:</strong><br>
                                            <span class="badge bg-warning text-dark me-1">Pending</span> Order placed, awaiting payment<br>
                                            @if(Auth::user()->canManageOrders())
                                                <span class="badge bg-success me-1">Paid</span> Payment received, preparing for shipment<br>
                                                <span class="badge bg-info me-1">Shipped</span> Order shipped, in transit<br>
                                                <span class="badge bg-primary me-1">Delivered</span> Order delivered to customer<br>
                                            @endif
                                            <span class="badge bg-danger me-1">Cancelled</span> Order cancelled
                                            @if(!Auth::user()->canManageOrders())
                                                <br><em class="text-info">Note: Only pending orders can be cancelled by customers.</em>
                                            @endif
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="notes" class="form-label">Order Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" 
                                      name="notes" 
                                      rows="4" 
                                      placeholder="Add any notes or special instructions for this order...">{{ old('notes', $order->notes) }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Add internal notes, tracking information, or customer communication details.</div>
                        </div>

                        <!-- Order Items (Read-only) -->
                        <div class="mb-4">
                            <label class="form-label">Order Items</label>
                            <div class="border rounded p-3 bg-light">
                                @if($order->orderItems && $order->orderItems->count() > 0)
                                    <div class="table-responsive">
                                        <table class="table table-sm table-borderless mb-0">
                                            <thead>
                                                <tr>
                                                    <th>Product</th>
                                                    <th>Price</th>
                                                    <th>Qty</th>
                                                    <th>Total</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($order->orderItems as $item)
                                                    <tr>
                                                        <td>
                                                            {{ $item->product ? $item->product->name : 'Product unavailable' }}
                                                        </td>
                                                        <td>${{ number_format($item->price, 2) }}</td>
                                                        <td>{{ $item->quantity }}</td>
                                                        <td class="fw-bold">${{ number_format($item->total, 2) }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot>
                                                <tr class="border-top">
                                                    <th colspan="3">Total:</th>
                                                    <th class="text-success">${{ number_format($order->total_amount, 2) }}</th>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                @else
                                    <p class="text-muted mb-0">No items found for this order.</p>
                                @endif
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-between">
                            <div>
                                <a href="{{ route('orders.show', $order) }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-times me-2"></i>Cancel
                                </a>
                            </div>
                            <div class="d-flex gap-2">
                                @if(Auth::user()->canManageOrders())
                                    <!-- Quick Status Update Buttons (Admin and Order Manager) -->
                                    @if($order->status === 'pending')
                                        <button type="button" class="btn btn-success status-update-btn" onclick="updateStatus('paid', event)">
                                            <i class="fas fa-credit-card me-2"></i>Mark as Paid
                                        </button>
                                    @elseif($order->status === 'paid')
                                        <button type="button" class="btn btn-info status-update-btn" onclick="updateStatus('shipped', event)">
                                            <i class="fas fa-shipping-fast me-2"></i>Mark as Shipped
                                        </button>
                                    @elseif($order->status === 'shipped')
                                        <button type="button" class="btn btn-primary status-update-btn" onclick="updateStatus('delivered', event)">
                                            <i class="fas fa-box me-2"></i>Mark as Delivered
                                        </button>
                                    @endif
                                @endif
                                
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save me-2"></i>Update Order
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Change History -->
    <div class="row mt-4">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Order History</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Order Created</h6>
                                <p class="mb-1">Order {{ $order->order_number }} was placed</p>
                                <small class="text-muted">{{ $order->created_at->format('M d, Y \a\t g:i A') }}</small>
                            </div>
                        </div>
                        
                        @if($order->updated_at != $order->created_at)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-{{ $order->status === 'cancelled' ? 'danger' : 'success' }}"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Status Updated</h6>
                                    <p class="mb-1">Order status changed to <span class="badge {{ $order->getStatusBadgeClass() }}">{{ ucfirst($order->status) }}</span></p>
                                    <small class="text-muted">{{ $order->updated_at->format('M d, Y \a\t g:i A') }}</small>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function updateStatus(status, event) {
            // Prevent any default behavior
            if (event) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            const statusLabels = {
                'paid': 'Paid',
                'shipped': 'Shipped',
                'delivered': 'Delivered',
                'cancelled': 'Cancelled'
            };
            
            // Confirm the status change
            if (!confirm(`Are you sure you want to mark this order as ${statusLabels[status]}?`)) {
                return false;
            }
            
            // Get the main form specifically
            const form = document.querySelector('form[action*="orders"][method="POST"]');
            if (!form) {
                alert('Form not found. Please refresh the page and try again.');
                return false;
            }
            
            // Check if CSRF token exists
            const csrfToken = form.querySelector('input[name="_token"]');
            if (!csrfToken || !csrfToken.value) {
                alert('Security token expired. Please refresh the page and try again.');
                return false;
            }
            
            // Get the button that was clicked
            let activeButton = null;
            if (event && event.target) {
                activeButton = event.target.closest('.status-update-btn');
            }
            
            if (activeButton) {
                const originalText = activeButton.innerHTML;
                
                // Disable button and show loading
                activeButton.disabled = true;
                activeButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                
                // Re-enable button after 5 seconds if no response
                setTimeout(() => {
                    if (activeButton && activeButton.disabled) {
                        activeButton.disabled = false;
                        activeButton.innerHTML = originalText;
                    }
                }, 5000);
            }
            
            // Update the status and submit
            const statusField = document.getElementById('status');
            if (statusField) {
                statusField.value = status;
            }
            
            // Submit the form
            form.submit();
        }
        
        // Add session warning functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Refresh CSRF token every 30 minutes
            setInterval(function() {
                fetch('/csrf-refresh', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.token) {
                        document.querySelectorAll('input[name="_token"]').forEach(input => {
                            input.value = data.token;
                        });
                    }
                })
                .catch(error => {
                    console.log('CSRF token refresh failed:', error);
                    showSessionWarning();
                });
            }, 30 * 60 * 1000); // 30 minutes
            
            // Show session warning 5 minutes before session expires (115 minutes)
            setTimeout(function() {
                showSessionWarning();
            }, 115 * 60 * 1000);
        });
        
        function showSessionWarning() {
            if (document.querySelector('.session-warning')) return; // Don't show multiple warnings
            
            const warning = document.createElement('div');
            warning.className = 'alert alert-warning alert-dismissible fade show session-warning';
            warning.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            warning.innerHTML = `
                <strong>Session Warning:</strong> Your session will expire soon. Save any changes and refresh the page to avoid being logged out.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            document.body.appendChild(warning);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (warning.parentNode) {
                    warning.remove();
                }
            }, 10000);
        }
    </script>

    <style>
        .timeline {
            position: relative;
            padding: 0;
        }
        
        .timeline-item {
            position: relative;
            padding-left: 40px;
            margin-bottom: 30px;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 14px;
            top: 30px;
            height: calc(100% + 10px);
            width: 2px;
            background-color: #dee2e6;
        }
        
        .timeline-marker {
            position: absolute;
            left: 0;
            top: 5px;
            width: 28px;
            height: 28px;
            border-radius: 50%;
            border: 3px solid white;
            box-shadow: 0 0 0 2px #dee2e6;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .timeline-content h6 {
            margin-bottom: 8px;
            font-size: 1rem;
        }
        
        .timeline-content p {
            margin-bottom: 4px;
            font-size: 0.9rem;
        }
        
        .timeline-content small {
            font-size: 0.8rem;
        }
    </style>
</x-app-layout>