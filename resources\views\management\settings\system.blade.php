<x-app-layout>
    <x-slot name="header">
        System Settings
    </x-slot>

    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('management.settings.index') }}">Settings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">System</li>
                </ol>
            </nav>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-server me-2"></i>System Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.settings.update.system') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row g-4">
                            <!-- Application Mode -->
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">Application Mode</h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('maintenance_mode') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="maintenance_mode" 
                                           name="maintenance_mode" 
                                           value="1"
                                           {{ old('maintenance_mode', $settings['maintenance_mode']) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="maintenance_mode">
                                        <strong>Maintenance Mode</strong>
                                    </label>
                                    <div class="form-text">When enabled, only admins can access the application</div>
                                    @error('maintenance_mode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('debug_mode') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="debug_mode" 
                                           name="debug_mode" 
                                           value="1"
                                           {{ old('debug_mode', $settings['debug_mode']) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="debug_mode">
                                        <strong>Debug Mode</strong>
                                    </label>
                                    <div class="form-text">Show detailed error messages (disable in production)</div>
                                    @error('debug_mode')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <!-- Performance Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Performance Settings</h6>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('cache_enabled') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="cache_enabled" 
                                           name="cache_enabled" 
                                           value="1"
                                           {{ old('cache_enabled', $settings['cache_enabled']) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="cache_enabled">
                                        <strong>Enable Caching</strong>
                                    </label>
                                    <div class="form-text">Improves application performance</div>
                                    @error('cache_enabled')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-md-6">
                                <label for="session_lifetime" class="form-label">Session Lifetime <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('session_lifetime') is-invalid @enderror" 
                                           id="session_lifetime" 
                                           name="session_lifetime" 
                                           value="{{ old('session_lifetime', $settings['session_lifetime']) }}" 
                                           min="5" 
                                           max="1440" 
                                           required>
                                    <span class="input-group-text">minutes</span>
                                </div>
                                <div class="form-text">How long users stay logged in (5-1440 minutes)</div>
                                @error('session_lifetime')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Logging Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Logging Settings</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="log_level" class="form-label">Log Level <span class="text-danger">*</span></label>
                                <select class="form-select @error('log_level') is-invalid @enderror" 
                                        id="log_level" 
                                        name="log_level" 
                                        required>
                                    <option value="">Select Log Level</option>
                                    @php
                                        $logLevels = [
                                            'emergency' => 'Emergency - System is unusable',
                                            'alert' => 'Alert - Action must be taken immediately',
                                            'critical' => 'Critical - Critical conditions',
                                            'error' => 'Error - Runtime errors',
                                            'warning' => 'Warning - Exceptional occurrences',
                                            'notice' => 'Notice - Normal but significant events',
                                            'info' => 'Info - Interesting events',
                                            'debug' => 'Debug - Detailed debug information',
                                        ];
                                    @endphp
                                    @foreach($logLevels as $value => $label)
                                        <option value="{{ $value }}" {{ old('log_level', $settings['log_level']) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('log_level')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="max_upload_size" class="form-label">Max Upload Size <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('max_upload_size') is-invalid @enderror" 
                                           id="max_upload_size" 
                                           name="max_upload_size" 
                                           value="{{ old('max_upload_size', $settings['max_upload_size']) }}" 
                                           min="1" 
                                           max="100" 
                                           required>
                                    <span class="input-group-text">MB</span>
                                </div>
                                <div class="form-text">Maximum file upload size (1-100 MB)</div>
                                @error('max_upload_size')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- System Actions -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">System Actions</h6>
                            </div>

                            <div class="col-12">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <form action="{{ route('management.settings.clear-cache') }}" method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-warning w-100">
                                                <i class="fas fa-broom me-2"></i>Clear Cache
                                            </button>
                                        </form>
                                    </div>
                                    <div class="col-md-3">
                                        <form action="{{ route('management.settings.optimize') }}" method="POST" class="d-inline">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-success w-100">
                                                <i class="fas fa-tachometer-alt me-2"></i>Optimize
                                            </button>
                                        </form>
                                    </div>
                                    <div class="col-md-3">
                                        <a href="{{ route('management.settings.download-logs') }}" class="btn btn-outline-info w-100">
                                            <i class="fas fa-download me-2"></i>Download Logs
                                        </a>
                                    </div>
                                    <div class="col-md-3">
                                        <form action="{{ route('management.settings.clear-logs') }}" 
                                              method="POST" 
                                              class="d-inline w-100"
                                              onsubmit="return confirm('Are you sure you want to clear all logs?')">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-danger w-100">
                                                <i class="fas fa-trash me-2"></i>Clear Logs
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12 mt-4">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('management.settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Current Status</h6>
                </div>
                <div class="card-body">
                    <div class="system-status">
                        <div class="mb-3">
                            <label class="form-label text-muted small">Maintenance Mode</label>
                            <p class="mb-0">
                                <span class="badge {{ $settings['maintenance_mode'] ? 'bg-warning' : 'bg-success' }}">
                                    {{ $settings['maintenance_mode'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Debug Mode</label>
                            <p class="mb-0">
                                <span class="badge {{ $settings['debug_mode'] ? 'bg-warning' : 'bg-success' }}">
                                    {{ $settings['debug_mode'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Cache</label>
                            <p class="mb-0">
                                <span class="badge {{ $settings['cache_enabled'] ? 'bg-success' : 'bg-warning' }}">
                                    {{ $settings['cache_enabled'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Session Lifetime</label>
                            <p class="mb-0">{{ $settings['session_lifetime'] }} minutes</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Log Level</label>
                            <p class="mb-0">{{ ucfirst($settings['log_level']) }}</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted small">Max Upload Size</label>
                            <p class="mb-0">{{ $settings['max_upload_size'] }} MB</p>
                        </div>
                    </div>
                </div>
            </div>

            @if(isset($systemInfo))
            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">System Information</h6>
                </div>
                <div class="card-body">
                    <div class="system-info">
                        @foreach($systemInfo as $key => $value)
                        <div class="mb-2">
                            <label class="form-label text-muted small">{{ ucfirst(str_replace('_', ' ', $key)) }}</label>
                            <p class="mb-0 small">{{ $value }}</p>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Help & Tips</h6>
                </div>
                <div class="card-body">
                    <div class="help-tips">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Warning:</strong> Debug mode should be disabled in production environments.
                        </div>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-2"></i>Enable caching for better performance</li>
                            <li><i class="fas fa-check text-success me-2"></i>Regular cache clearing prevents issues</li>
                            <li><i class="fas fa-check text-success me-2"></i>Monitor log levels to avoid disk space issues</li>
                            <li><i class="fas fa-check text-success me-2"></i>Optimize application after configuration changes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>