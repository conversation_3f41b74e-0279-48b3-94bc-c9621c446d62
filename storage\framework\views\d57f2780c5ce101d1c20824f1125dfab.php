<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        ENDTIME | SUBMIT ONGOING LOT
     <?php $__env->endSlot(); ?>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><strong>Validation Errors:</strong>
            <ul class="mb-0 mt-2">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Submit Ongoing Lot Form -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-success text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-check-circle me-2"></i>Submit Ongoing Lot to Inspection
                </h5>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-warning btn-sm">
                        <i class="fas fa-home me-1"></i>Dashboard
                    </a>
                    <a href="<?php echo e(route('endtime.index')); ?>" class="btn btn-light btn-sm">
                        <i class="fas fa-list me-1"></i>Back to List
                    </a>
                    <a href="<?php echo e(route('endtime.create')); ?>" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus-circle me-1"></i>ADD ENDTIME Lot
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body p-4">
            <div class="alert alert-info mb-4">
                <i class="fas fa-info-circle me-2"></i>
                <strong>Purpose:</strong> Update ongoing forecasted lots to "Submitted" status when they have been completed and submitted for inspection.
            </div>

            <form id="submittedLotForm" action="<?php echo e(route('endtime.submit')); ?>" method="POST">
                <?php echo csrf_field(); ?>

                <!-- Lot Search Section -->
                <div class="form-section mb-4">
                    <h6 class="section-title mb-3">
                        <i class="fas fa-search me-2"></i>Select Ongoing Lot
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="lot_search_input" class="form-label fw-bold">Search Ongoing Lot <span class="text-danger">*</span></label>
                            <div class="position-relative">
                                <input type="text" class="form-control form-control-lg" id="lot_search_input" name="lot_search"
                                       placeholder="Type Lot ID to search..."
                                       autocomplete="off" required
                                       oninput="searchOngoingLots()"
                                       onblur="setTimeout(validateLotSelection, 200)"
                                       onfocus="showLotDropdown()">
                                <div class="search-icon">
                                    <i class="fas fa-search"></i>
                                </div>
                                <div class="lot-search-dropdown d-none position-absolute w-100 bg-white border rounded shadow-sm"
                                     id="lotSearchDropdown" style="z-index: 1000; max-height: 300px; overflow-y: auto;">
                                    <!-- Dynamic lot options will be inserted here -->
                                </div>
                                <input type="hidden" id="selected_lot_id" name="lot_id" required>
                            </div>
                            <small class="text-muted">Only lots with "Ongoing" status will appear in search results</small>
                        </div>

                        <div class="col-md-6">
                            <div class="ongoing-lots-summary">
                                <h6 class="fw-bold text-success">Available Ongoing Lots</h6>
                                <div id="ongoing-lots-count" class="stat-display">
                                    <div class="stat-number">--</div>
                                    <div class="stat-label">Ongoing Lots</div>
                                </div>
                                <button type="button" class="btn btn-outline-success btn-sm mt-2" onclick="refreshOngoingLots()">
                                    <i class="fas fa-sync-alt me-1"></i>Refresh List
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submission Details Section -->
                <div class="form-section mb-4">
                    <h6 class="section-title mb-3">
                        <i class="fas fa-clock me-2"></i>Submission Details
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="actual_submission_time" class="form-label fw-bold">Actual Submission Time <span class="text-danger">*</span></label>
                            <div class="input-group">
                                <input type="datetime-local" class="form-control form-control-lg"
                                       id="actual_submission_time" name="actual_submitted_at"
                                       step="60" required
                                       onchange="calculateLotResult()">
                                <button type="button" class="btn btn-outline-primary"
                                        onclick="setCurrentTime()" title="Set to current time">
                                    <i class="fas fa-clock"></i>
                                </button>
                            </div>
                            <small class="text-muted">When was the lot actually submitted to inspection?</small>
                        </div>

                        <div class="col-md-6">
                            <label for="submitted_status" class="form-label fw-bold">New Status <span class="text-danger">*</span></label>
                            <select class="form-select form-select-lg" id="submitted_status" name="status" required>
                                <option value="Submitted">Submitted</option>
                            </select>
                            <small class="text-muted">Lot will be marked as submitted for inspection</small>
                        </div>
                    </div>
                </div>

                <!-- Selected Lot Details Display -->
                <div id="selectedLotDetails" class="form-section mb-4" style="display: none;">
                    <h6 class="section-title mb-3">
                        <i class="fas fa-info-circle me-2"></i>Selected Lot Information
                    </h6>

                    <div class="lot-details-card">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="detail-item">
                                    <label>Lot ID:</label>
                                    <span id="detail_lot_id" class="detail-value">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>Model:</label>
                                    <span id="detail_model" class="detail-value">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>Quantity:</label>
                                    <span id="detail_quantity" class="detail-value">-</span> <small>pcs</small>
                                </div>
                                <div class="detail-item">
                                    <label>Work Type:</label>
                                    <span id="detail_work_type" class="detail-value badge bg-secondary">-</span>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="detail-item">
                                    <label>Line/Area:</label>
                                    <span id="detail_line_area" class="detail-value">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>Est. End Time:</label>
                                    <span id="detail_end_time" class="detail-value text-primary fw-bold">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>Submission Result:</label>
                                    <span id="detail_result" class="detail-value badge bg-secondary">-</span>
                                </div>
                                <div class="detail-item">
                                    <label>Time Difference:</label>
                                    <span id="detail_time_diff" class="detail-value">-</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submission Result and Notes Section -->
                <div class="form-section mb-4">
                    <h6 class="section-title mb-3">
                        <i class="fas fa-clipboard me-2"></i>Additional Information
                    </h6>

                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="lot_result" class="form-label fw-bold">Submission Result</label>
                            <input type="text" class="form-control form-control-lg" id="lot_result" name="remarks" readonly
                                   placeholder="Will be calculated automatically">
                            <small class="text-muted">Auto-calculated based on timing: Early, OK, or Delayed</small>
                        </div>
                        <div class="col-md-8">
                            <label for="submission_notes" class="form-label fw-bold">Notes <small class="text-muted">(Optional)</small></label>
                            <textarea class="form-control" id="submission_notes" name="submission_notes" rows="3"
                                      placeholder="Enter any additional notes about the lot submission..."></textarea>
                            <small class="text-muted">Any issues, observations, or additional details about the submission</small>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="form-actions">
                    <div class="d-flex justify-content-end gap-3">
                        <a href="<?php echo e(route('endtime.index')); ?>" class="btn btn-outline-secondary btn-lg">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-success btn-lg" id="submitLotButton" disabled>
                            <i class="fas fa-check-circle me-2"></i>Submit Lot
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- JavaScript Integration -->
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set current time for submission input
            const now = new Date();
            const currentDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            const submissionTimeInput = document.getElementById('actual_submission_time');
            if (submissionTimeInput) {
                submissionTimeInput.value = currentDateTime;
            }

            // Load ongoing lots count
            loadOngoingLotsCount();
        });

        // Set current time button functionality
        function setCurrentTime() {
            const now = new Date();
            const currentDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            document.getElementById('actual_submission_time').value = currentDateTime;
            calculateLotResult();
        }

        // Load ongoing lots count
        function loadOngoingLotsCount() {
            fetch('/endtime/ongoing-lots')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const countElement = document.querySelector('#ongoing-lots-count .stat-number');
                        if (countElement) {
                            countElement.textContent = data.lots.length;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading ongoing lots count:', error);
                });
        }

        // Refresh ongoing lots count
        function refreshOngoingLots() {
            const button = event.target;
            const icon = button.querySelector('i');

            // Show loading state
            icon.classList.add('fa-spin');
            button.disabled = true;

            loadOngoingLotsCount();

            // Reset button state
            setTimeout(() => {
                icon.classList.remove('fa-spin');
                button.disabled = false;
            }, 1000);
        }

        // Search ongoing lots functionality
        function searchOngoingLots() {
            const searchInput = document.getElementById('lot_search_input');
            const searchTerm = searchInput.value.trim();
            const dropdown = document.getElementById('lotSearchDropdown');

            if (searchTerm.length < 1) {
                dropdown.classList.add('d-none');
                return;
            }

            fetch('/endtime/ongoing-lots')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const filteredLots = data.lots.filter(lot =>
                            lot.lot_id.toLowerCase().includes(searchTerm.toLowerCase())
                        );

                        displayLotOptions(filteredLots);
                    }
                })
                .catch(error => {
                    console.error('Error searching ongoing lots:', error);
                });
        }

        // Display lot options in dropdown
        function displayLotOptions(lots) {
            const dropdown = document.getElementById('lotSearchDropdown');

            if (lots.length === 0) {
                dropdown.innerHTML = '<div class="dropdown-item text-muted">No ongoing lots found</div>';
            } else {
                dropdown.innerHTML = lots.map(lot => `
                    <div class="dropdown-item lot-option" onclick="selectLot(${lot.id}, '${lot.lot_id}')">
                        <div class="lot-option-main">
                            <strong>${lot.lot_id}</strong>
                            <span class="badge bg-primary ms-2">${lot.model_15}</span>
                        </div>
                        <div class="lot-option-details">
                            <small class="text-muted">
                                ${lot.eqp_line}/${lot.eqp_area} • ${lot.lot_qty} pcs • Est: ${new Date(lot.est_endtime).toLocaleString()}
                            </small>
                        </div>
                    </div>
                `).join('');
            }

            dropdown.classList.remove('d-none');
        }

        // Select a lot from dropdown
        function selectLot(lotId, lotIdString) {
            document.getElementById('lot_search_input').value = lotIdString;
            document.getElementById('selected_lot_id').value = lotId;
            document.getElementById('lotSearchDropdown').classList.add('d-none');

            // Load lot details
            loadLotDetails(lotId);

            // Enable submit button
            document.getElementById('submitLotButton').disabled = false;
        }

        // Load lot details
        function loadLotDetails(lotId) {
            fetch(`/api/endtime/${lotId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const lot = data.lot;

                        // Populate lot details
                        document.getElementById('detail_lot_id').textContent = lot.lot_id;
                        document.getElementById('detail_model').textContent = lot.model_15;
                        document.getElementById('detail_quantity').textContent = lot.lot_qty.toLocaleString();
                        document.getElementById('detail_work_type').textContent = lot.work_type;
                        document.getElementById('detail_line_area').textContent = `${lot.eqp_line}/${lot.eqp_area}`;
                        document.getElementById('detail_end_time').textContent = new Date(lot.est_endtime).toLocaleString();

                        // Show details section
                        document.getElementById('selectedLotDetails').style.display = 'block';

                        // Calculate result based on current submission time
                        calculateLotResult();
                    }
                })
                .catch(error => {
                    console.error('Error loading lot details:', error);
                });
        }

        // Show lot dropdown
        function showLotDropdown() {
            if (document.getElementById('lot_search_input').value.length > 0) {
                searchOngoingLots();
            }
        }

        // Validate lot selection
        function validateLotSelection() {
            const searchInput = document.getElementById('lot_search_input');
            const selectedLotId = document.getElementById('selected_lot_id');

            if (searchInput.value && !selectedLotId.value) {
                // Clear invalid selection
                searchInput.value = '';
                document.getElementById('selectedLotDetails').style.display = 'none';
                document.getElementById('submitLotButton').disabled = true;
            }
        }

        // Calculate lot result
        function calculateLotResult() {
            const selectedLotId = document.getElementById('selected_lot_id').value;
            const submissionTime = document.getElementById('actual_submission_time').value;

            if (!selectedLotId || !submissionTime) {
                return;
            }

            // Get the estimated end time from the display
            const estEndTimeText = document.getElementById('detail_end_time').textContent;
            if (estEndTimeText === '-') {
                return;
            }

            try {
                const actualTime = new Date(submissionTime);
                const estimatedTime = new Date(estEndTimeText);

                // Calculate difference in minutes
                const diffMinutes = Math.round((actualTime - estimatedTime) / (1000 * 60));

                let result = '';
                let timeDiffText = '';
                let resultClass = '';

                if (diffMinutes > 30) {
                    result = 'Delayed';
                    resultClass = 'bg-danger';
                    timeDiffText = `${diffMinutes} minutes late`;
                } else if (diffMinutes < -30) {
                    result = 'Early';
                    resultClass = 'bg-success';
                    timeDiffText = `${Math.abs(diffMinutes)} minutes early`;
                } else {
                    result = 'OK';
                    resultClass = 'bg-primary';
                    if (diffMinutes === 0) {
                        timeDiffText = 'On time';
                    } else if (diffMinutes > 0) {
                        timeDiffText = `${diffMinutes} minutes late (within tolerance)`;
                    } else {
                        timeDiffText = `${Math.abs(diffMinutes)} minutes early (within tolerance)`;
                    }
                }

                // Update result display
                document.getElementById('lot_result').value = result;
                document.getElementById('detail_result').textContent = result;
                document.getElementById('detail_result').className = `detail-value badge ${resultClass}`;
                document.getElementById('detail_time_diff').textContent = timeDiffText;

            } catch (error) {
                console.error('Error calculating lot result:', error);
            }
        }

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('lotSearchDropdown');
            const searchInput = document.getElementById('lot_search_input');

            if (!searchInput.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.add('d-none');
            }
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Project\process-dashboard\resources\views/endtime/submit.blade.php ENDPATH**/ ?>