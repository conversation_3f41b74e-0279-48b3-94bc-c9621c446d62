<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LotAssignment extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'lot_request_id',
        'lot_id',
        'lot_code',
        'equipment_number',
        'equipment_code',
        'lot_quantity',
        'assigned_date',
        'assigned_by'
    ];
    
    protected $casts = [
        'assigned_date' => 'datetime',
    ];
    
    /**
     * Get the lot request this assignment belongs to
     */
    public function lotRequest()
    {
        return $this->belongsTo(LotRequest::class);
    }
    
    /**
     * Get the user who made the assignment
     */
    public function assignedBy()
    {
        return $this->belongsTo(User::class, 'assigned_by');
    }
    
    /**
     * Get the equipment this lot is assigned to
     */
    public function equipment()
    {
        return $this->belongsTo(Equipment::class, 'equipment_number', 'eqp_no');
    }
}
