<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\View\View;

class RegisteredUserController extends Controller
{
    /**
     * Display the registration view.
     */
    public function create(): View
    {
        return view('auth.register');
    }

    /**
     * Handle an incoming registration request.
     *
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'emp_no' => ['required', 'string', 'max:255', 'unique:'.User::class],
            'emp_name' => ['required', 'string', 'max:255'],
            'position' => ['nullable', 'string', 'max:255'],
            'title_class' => ['nullable', 'string', 'max:255'],
            'rank' => ['nullable', 'string', 'max:255'],
            'hr_job_name' => ['nullable', 'string', 'max:255'],
            'job_assigned' => ['nullable', 'string', 'max:255'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);

        $user = User::create([
            'emp_no' => $request->emp_no,
            'emp_name' => $request->emp_name,
            'position' => $request->position,
            'title_class' => $request->title_class,
            'rank' => $request->rank,
            'hr_job_name' => $request->hr_job_name,
            'job_assigned' => $request->job_assigned,
            'password' => Hash::make($request->password),
            'role' => 'USER', // Default role
            'emp_verified_at' => null, // New users are deactivated by default
        ]);

        event(new Registered($user));

        // Don't auto-login deactivated users
        // Auth::login($user); // Removed - users must be activated first

        return redirect(route('login'))
            ->with('success', 'Registration successful! Your account needs to be activated by an administrator before you can log in.');
    }
}
