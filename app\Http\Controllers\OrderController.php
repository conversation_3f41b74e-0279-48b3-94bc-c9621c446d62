<?php

namespace App\Http\Controllers;

use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class OrderController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Allow admins and order managers to access all order management functions
        // Regular users can only view/create/edit their own orders
        $this->middleware('order.manage')->only(['destroy']);
    }

    /**
     * Get the authenticated user as a User model instance
     * 
     * @return User
     */
    private function getAuthenticatedUser(): User
    {
        return Auth::user();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $query = Order::with(['user', 'orderItems.product']);
        
        // Admins and order managers can see all orders
        // Regular users can only see their own orders
        if (!$this->getAuthenticatedUser()->canManageOrders()) {
            $query->where('user_id', Auth::user()->id);
        }
        
        $orders = $query->latest()->paginate(10);
        return view('orders.index', compact('orders'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $products = Product::where('status', 'active')->where('stock', '>', 0)->get();
        return view('orders.create', compact('products'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validation rules depend on user role
        $rules = [
            'products' => 'required|array|min:1',
            'products.*.id' => 'required|exists:products,id',
            'products.*.quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:1000',
        ];
        
        // If user can manage orders, they can select a customer
        if ($this->getAuthenticatedUser()->canManageOrders()) {
            $rules['user_id'] = 'required|exists:users,id';
        }
        
        // Remove debug code
        
        $request->validate($rules);

        $totalAmount = 0;
        $totalItems = 0;
        $orderItems = [];

        // Calculate total and validate stock
        foreach ($request->products as $item) {
            $product = Product::findOrFail($item['id']);
            
            if ($product->stock < $item['quantity']) {
                return back()->withErrors([
                    'products' => "Insufficient stock for {$product->name}. Available: {$product->stock}"
                ])->withInput();
            }
            
            $totalAmount += $product->price * $item['quantity'];
            $totalItems += $item['quantity'];
            $orderItems[] = [
                'product' => $product,
                'quantity' => $item['quantity'],
                'price' => $product->price
            ];
        }

        // Determine the user_id for the order
        if ($this->getAuthenticatedUser()->canManageOrders() && $request->filled('user_id')) {
            $userId = $request->user_id;
        } else {
            $userId = Auth::user()->id;
        }

        // Create order
        $order = Order::create([
            'order_number' => 'ORD-' . strtoupper(Str::random(8)),
            'user_id' => $userId,
            'total_amount' => $totalAmount,
            'total_items' => $totalItems,
            'notes' => $request->notes,
            'order_date' => now(),
        ]);

        // Create order items and update product stock
        foreach ($orderItems as $item) {
            $order->orderItems()->create([
                'product_id' => $item['product']->id,
                'quantity' => $item['quantity'],
                'price' => $item['price'],
                'total' => $item['price'] * $item['quantity'],
            ]);
            
            // Update product stock
            $item['product']->decrement('stock', $item['quantity']);
        }

        return redirect()->route('orders.show', $order)
            ->with('success', 'Order created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Order $order)
    {
        // Admins and order managers can view any order
        // Regular users can only view their own orders
        if (!$this->getAuthenticatedUser()->canManageOrders() && $order->user_id !== Auth::user()->id) {
            abort(403, 'Unauthorized access.');
        }
        
        $order->load(['orderItems.product', 'user']);
        
        return view('orders.show', compact('order'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Order $order)
    {
        // Admins and order managers can edit any order
        // Regular users can only edit their own orders
        if (!$this->getAuthenticatedUser()->canManageOrders() && $order->user_id !== Auth::user()->id) {
            abort(403, 'Unauthorized access.');
        }
        
        return view('orders.edit', compact('order'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Order $order)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Authentication required'], 401);
            }
            return redirect()->route('login')
                ->with('error', 'You must be logged in to update orders.');
        }

        // Admins and order managers can update any order
        // Regular users can only update their own orders
        if (!$this->getAuthenticatedUser()->canManageOrders() && $order->user_id !== Auth::user()->id) {
            abort(403, 'Unauthorized access.');
        }
        
        // Role-based validation for status updates
        if (!$this->getAuthenticatedUser()->canManageOrders()) {
            // Regular users have limited status update options
            $request->validate([
                'status' => 'required|in:pending,cancelled',
                'notes' => 'nullable|string|max:1000',
            ]);
        } else {
            // Admins and order managers can set any status
            $request->validate([
                'status' => 'required|in:pending,paid,shipped,delivered,cancelled',
                'notes' => 'nullable|string|max:1000',
            ]);
        }

        try {
            $order->update([
                'status' => $request->status,
                'notes' => $request->notes,
            ]);

            return redirect()->route('orders.show', $order)
                ->with('success', 'Order updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update order. Please try again.')
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Order $order)
    {
        $order->delete();
        
        return redirect()->route('orders.index')
            ->with('success', 'Order deleted successfully!');
    }
}
