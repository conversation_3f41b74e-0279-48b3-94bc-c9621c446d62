<script>
// Avatar preview functionality
document.getElementById('avatar').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Validate file size (5MB)
        if (file.size > 5 * 1024 * 1024) {
            showToast('Error', 'File size must be less than 5MB', 'error');
            e.target.value = '';
            return;
        }
        
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            showToast('Error', 'Please select a valid image file (JPEG, PNG, JPG, GIF)', 'error');
            e.target.value = '';
            return;
        }
        
        const reader = new FileReader();
        reader.onload = function(e) {
            const previewElement = document.getElementById('avatarPreview');
            
            // If it's currently a div (letter avatar), replace with img
            if (previewElement.tagName === 'DIV') {
                const img = document.createElement('img');
                img.id = 'avatarPreview';
                img.className = 'rounded-circle';
                img.style.objectFit = 'cover';
                img.width = 150;
                img.height = 150;
                img.src = e.target.result;
                img.alt = 'Avatar Preview';
                previewElement.parentNode.replaceChild(img, previewElement);
            } else {
                // If it's already an img, just update the src
                previewElement.src = e.target.result;
            }
        };
        reader.readAsDataURL(file);
    }
});

// Upload avatar functionality
document.getElementById('uploadAvatar').addEventListener('click', function() {
    const fileInput = document.getElementById('avatar');
    const file = fileInput.files[0];
    
    if (!file) {
        showToast('Error', 'Please select an image first', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('avatar', file);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
    
    const uploadBtn = this;
    const originalText = uploadBtn.innerHTML;
    uploadBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Uploading...';
    uploadBtn.disabled = true;
    
    fetch('{{ route("profile.avatar.update") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        
        if (data.success) {
            // Update all avatar images on the page
            updateAvatarImages(data.avatar_url);
            
            // Force page refresh after successful upload to ensure new avatar is displayed
            setTimeout(() => {
                window.location.reload();
            }, 1000);
            
            // Close modal properly with cleanup
            const modalElement = document.getElementById('avatarModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
            
            // Comprehensive cleanup after modal close
            setTimeout(() => {
                cleanupModalArtifacts();
            }, 300);
            
            // Show success message
            showToast('Success', data.message, 'success');
            
            // Clear file input
            fileInput.value = '';
        } else {
            console.error('Server returned error:', data);
            showToast('Error', data.message || 'Failed to upload image', 'error');
        }
    })
    .catch(error => {
        console.error('Upload error details:', error);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
        showToast('Error', `An error occurred while uploading the image: ${error.message}`, 'error');
    })
    .finally(() => {
        uploadBtn.innerHTML = originalText;
        uploadBtn.disabled = false;
    });
});

// Remove avatar functionality
document.getElementById('removeAvatar').addEventListener('click', function() {
    if (!confirm('Are you sure you want to remove your profile picture?')) {
        return;
    }
    
    const removeBtn = this;
    const originalText = removeBtn.innerHTML;
    removeBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Removing...';
    removeBtn.disabled = true;
    
    fetch('{{ route("profile.avatar.remove") }}', {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'X-Requested-With': 'XMLHttpRequest'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update all avatar images on the page
            updateAvatarImages(data.avatar_url);
            
            // Close modal properly with cleanup
            const modalElement = document.getElementById('avatarModal');
            const modal = bootstrap.Modal.getInstance(modalElement);
            if (modal) {
                modal.hide();
            }
            
            // Comprehensive cleanup after modal close
            setTimeout(() => {
                cleanupModalArtifacts();
            }, 300);
            
            // Show success message
            showToast('Success', data.message, 'success');
            
            // Clear file input
            document.getElementById('avatar').value = '';
        } else {
            showToast('Error', data.message || 'Failed to remove image', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('Error', 'An error occurred while removing the image', 'error');
    })
    .finally(() => {
        removeBtn.innerHTML = originalText;
        removeBtn.disabled = false;
    });
});

// Helper function to clean up modal artifacts
function cleanupModalArtifacts() {
    // Remove any remaining modal backdrops
    const backdrops = document.querySelectorAll('.modal-backdrop');
    backdrops.forEach(backdrop => backdrop.remove());
    
    // Reset body classes and styles
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
    
    // Only remove pointer-events from elements that are explicitly modal-related
    // Avoid affecting other Bootstrap components like dropdowns
    const modalElements = document.querySelectorAll('.modal, .modal-backdrop');
    modalElements.forEach(element => {
        if (element.style.pointerEvents) {
            element.style.pointerEvents = '';
        }
    });
}

// Helper function to update all avatar images on the page
function updateAvatarImages(newAvatarUrl) {
    // Add timestamp to force browser refresh and avoid caching
    const timestamp = new Date().getTime();
    const urlWithTimestamp = newAvatarUrl + '?t=' + timestamp;
    
    // Update main profile avatar
    const mainAvatar = document.querySelector('.profile-avatar img');
    if (mainAvatar) {
        mainAvatar.src = urlWithTimestamp;
    }
    
    // Update modal preview
    const modalPreview = document.getElementById('avatarPreview');
    if (modalPreview) {
        if (modalPreview.tagName === 'DIV') {
            // Replace div with img
            const img = document.createElement('img');
            img.id = 'avatarPreview';
            img.className = 'rounded-circle';
            img.style.objectFit = 'cover';
            img.width = 150;
            img.height = 150;
            img.src = urlWithTimestamp;
            img.alt = 'Avatar Preview';
            modalPreview.parentNode.replaceChild(img, modalPreview);
        } else {
            modalPreview.src = urlWithTimestamp;
        }
    }
    
    // Update navbar avatar
    const navbarAvatar = document.querySelector('.user-avatar img');
    if (navbarAvatar) {
        navbarAvatar.src = urlWithTimestamp;
    }
    
    // Update any other avatar images that might exist
    const allAvatarImages = document.querySelectorAll('img[src*="storage/avatars"], img[src*="storage/"]');
    allAvatarImages.forEach(img => {
        img.src = urlWithTimestamp;
    });
    
    // Replace any letter-based avatar divs with actual images
    const avatarDivs = document.querySelectorAll('.profile-avatar div[style*="background-color: #6366f1"], .user-avatar div[style*="background-color: #6366f1"], div[id="avatarPreview"][style*="background-color: #6366f1"]');
    avatarDivs.forEach(div => {
        const img = document.createElement('img');
        img.src = urlWithTimestamp;
        img.alt = div.textContent + "'s Avatar";
        img.className = 'rounded-circle';
        img.style.objectFit = 'cover';
        
        // Copy dimensions
        const style = window.getComputedStyle(div);
        img.width = parseInt(style.width);
        img.height = parseInt(style.height);
        img.style.width = style.width;
        img.style.height = style.height;
        
        // Replace the div with the image
        div.parentNode.replaceChild(img, div);
    });
}

// Helper function to show toast notifications
function showToast(title, message, type = 'info') {
    // Remove existing toasts
    const existingToasts = document.querySelectorAll('.toast-container');
    existingToasts.forEach(container => {
        const toast = container.querySelector('.toast');
        if (toast) {
            const bsToast = bootstrap.Toast.getInstance(toast);
            if (bsToast) {
                bsToast.hide();
            }
        }
        container.remove();
    });
    
    // Create new toast
    const toastContainer = document.createElement('div');
    toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
    toastContainer.style.zIndex = '1070'; // Higher than modal backdrop
    toastContainer.style.pointerEvents = 'none'; // Don't block interaction
    
    const iconClass = type === 'success' ? 'fa-check-circle text-success' : 
                     type === 'error' ? 'fa-exclamation-circle text-danger' : 
                     'fa-info-circle text-info';
    
    toastContainer.innerHTML = `
        <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true" style="pointer-events: auto;">
            <div class="toast-header">
                <i class="fas ${iconClass} me-2"></i>
                <strong class="me-auto">${title}</strong>
                <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
            <div class="toast-body">${message}</div>
        </div>
    `;
    
    document.body.appendChild(toastContainer);
    
    // Initialize Bootstrap toast
    const toast = toastContainer.querySelector('.toast');
    const bsToast = new bootstrap.Toast(toast, {
        autohide: true,
        delay: 4000
    });
    
    // Clean up when toast is hidden
    toast.addEventListener('hidden.bs.toast', () => {
        toastContainer.remove();
    });
    
    bsToast.show();
}

// Auto-hide toasts
document.addEventListener('DOMContentLoaded', function() {
    const toasts = document.querySelectorAll('.toast');
    toasts.forEach(toast => {
        setTimeout(() => {
            toast.classList.remove('show');
        }, 3000);
    });
});

// Tab switching functionality
document.addEventListener('DOMContentLoaded', function() {
    const tabButtons = document.querySelectorAll('#profileTabs button[data-bs-toggle="pill"]');
    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            // Store active tab in localStorage
            localStorage.setItem('activeProfileTab', this.getAttribute('data-bs-target'));
        });
    });

    // Restore active tab from localStorage
    const activeTab = localStorage.getItem('activeProfileTab');
    if (activeTab) {
        const activeButton = document.querySelector(`#profileTabs button[data-bs-target="${activeTab}"]`);
        const activePane = document.querySelector(activeTab);
        
        if (activeButton && activePane) {
            // Remove active classes from all tabs
            document.querySelectorAll('#profileTabs .nav-link').forEach(link => link.classList.remove('active'));
            document.querySelectorAll('.tab-pane').forEach(pane => pane.classList.remove('show', 'active'));
            
            // Add active classes to selected tab
            activeButton.classList.add('active');
            activePane.classList.add('show', 'active');
        }
    }
});
</script>

<style>
.nav-pills .nav-link {
    border-radius: 0;
    border-bottom: 3px solid transparent;
    background: none;
    color: #6c757d;
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-pills .nav-link.active {
    background: none;
    color: #6366f1;
    border-bottom-color: #6366f1;
}

.nav-pills .nav-link:hover {
    background: rgba(99, 102, 241, 0.1);
    color: #6366f1;
}

.avatar-wrapper {
    display: inline-block;
    position: relative;
}

/* Camera button styling */
.avatar-wrapper button {
    transition: all 0.3s ease;
    border: none !important;
    outline: none !important;
}

.avatar-wrapper button:hover {
    transform: scale(1.1);
    background-color: #6366f1 !important;
    color: white !important;
    box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4) !important;
}

.avatar-wrapper button:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2) !important;
}

.avatar-wrapper button:active {
    transform: scale(0.95);
}

.form-check-input:checked {
    background-color: #6366f1;
    border-color: #6366f1;
}

.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.profile-avatar img {
    border: 4px solid #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    transition: all 0.3s ease;
}

.profile-avatar img:hover {
    transform: scale(1.05);
}

.toast {
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.form-control:focus,
.form-select:focus {
    border-color: #6366f1;
    box-shadow: 0 0 0 0.2rem rgba(99, 102, 241, 0.25);
}

.btn-primary {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    border: none;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.card-header {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    border-bottom: 1px solid #e2e8f0;
}

@media (max-width: 768px) {
    .profile-avatar {
        text-align: center;
        margin-bottom: 1rem;
    }
    
    .nav-pills .nav-link {
        font-size: 0.875rem;
        padding: 0.75rem 0.5rem;
    }
    
    .nav-pills .nav-link i {
        display: block;
        margin-bottom: 0.25rem;
    }
}
</style>