<x-app-layout>
    <x-slot name="header">
        Create New Order
    </x-slot>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Create Order</h4>
                    <p class="text-muted mb-0">Add a new order to the system</p>
                </div>
                <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Orders
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Order Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('orders.store') }}" method="POST" id="orderForm">
                        @csrf
                        
                        <!-- Customer Selection (Admin and Order Manager) -->
                        @if(Auth::user()->canManageOrders())
                            <div class="mb-4">
                                <label for="user_id" class="form-label">Customer</label>
                                <select class="form-select @error('user_id') is-invalid @enderror" 
                                        id="user_id" name="user_id">
                                    <option value="">Select Customer</option>
                                    @foreach(\App\Models\User::where('role', 'USER')->get() as $user)
                                        <option value="{{ $user->id }}" {{ old('user_id') == $user->id ? 'selected' : '' }}>
                                            {{ $user->emp_name }} ({{ $user->emp_no }})
                                        </option>
                                    @endforeach
                                </select>
                                @error('user_id')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">Select the customer for this order</div>
                            </div>
                        @endif

                        <!-- Product Selection -->
                        <div class="mb-4">
                            <label class="form-label">Products <span class="text-danger">*</span></label>
                            <div id="productContainer">
                                <div class="product-item border rounded p-3 mb-3">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Product</label>
                                            <select class="form-select product-select @error('products.0.id') is-invalid @enderror" 
                                                    name="products[0][id]" required>
                                                <option value="">Select Product</option>
                                                @foreach($products as $product)
                                                    <option value="{{ $product->id }}" 
                                                            data-price="{{ $product->price }}" 
                                                            data-stock="{{ $product->stock }}">
                                                        {{ $product->name }} - ${{ number_format($product->price, 2) }} 
                                                        (Stock: {{ $product->stock }})
                                                    </option>
                                                @endforeach
                                            </select>
                                            @error('products.0.id')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-4">
                                            <label class="form-label">Quantity</label>
                                            <input type="number" 
                                                   class="form-control quantity-input @error('products.0.quantity') is-invalid @enderror" 
                                                   name="products[0][quantity]" 
                                                   min="1" 
                                                   value="{{ old('products.0.quantity', 1) }}" 
                                                   required>
                                            @error('products.0.quantity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-2 d-flex align-items-end">
                                            <button type="button" class="btn btn-outline-danger remove-product" style="display: none;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <small class="text-muted item-total">Select a product to see the total</small>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="button" class="btn btn-outline-primary" id="addProduct">
                                <i class="fas fa-plus me-2"></i>Add Another Product
                            </button>
                            
                            @error('products')
                                <div class="text-danger mt-2">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Order Notes -->
                        <div class="mb-4">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" 
                                      name="notes" 
                                      rows="3" 
                                      placeholder="Any special instructions or notes for this order...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Order Summary -->
                        <div class="border rounded p-3 bg-light mb-4">
                            <h6 class="mb-3">Order Summary</h6>
                            <div class="d-flex justify-content-between">
                                <span>Total Items:</span>
                                <span id="totalItems">0</span>
                            </div>
                            <div class="d-flex justify-content-between">
                                <span>Total Amount:</span>
                                <span class="fw-bold text-success" id="totalAmount">$0.00</span>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Order
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let productIndex = 1;
        let productOptionsHtml = '';

        document.addEventListener('DOMContentLoaded', function() {
            // Store product options HTML for reuse
            const firstSelect = document.querySelector('.product-select');
            if (firstSelect) {
                productOptionsHtml = firstSelect.innerHTML;
            }
            
            updateOrderSummary();
            
            // Add product button
            document.getElementById('addProduct').addEventListener('click', function() {
                addProductRow();
            });

            // Initial event listeners
            addProductEventListeners();
        });

        function addProductRow() {
            const container = document.getElementById('productContainer');
            const productItem = document.createElement('div');
            productItem.className = 'product-item border rounded p-3 mb-3';
            
            productItem.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Product</label>
                        <select class="form-select product-select" name="products[${productIndex}][id]" required>
                            ${productOptionsHtml}
                        </select>
                    </div>
                    <div class="col-md-4">
                        <label class="form-label">Quantity</label>
                        <input type="number" class="form-control quantity-input" 
                               name="products[${productIndex}][quantity]" min="1" value="1" required />
                    </div>
                    <div class="col-md-2 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-danger remove-product">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-2">
                    <small class="text-muted item-total">Select a product to see the total</small>
                </div>
            `;
            
            container.appendChild(productItem);
            productIndex++;
            
            // Add event listeners to new elements
            addProductEventListeners();
            updateRemoveButtons();
        }

        function addProductEventListeners() {
            // Product select change
            document.querySelectorAll('.product-select').forEach(select => {
                select.removeEventListener('change', handleProductChange);
                select.addEventListener('change', handleProductChange);
            });

            // Quantity input change
            document.querySelectorAll('.quantity-input').forEach(input => {
                input.removeEventListener('input', updateOrderSummary);
                input.addEventListener('input', updateOrderSummary);
            });

            // Remove product buttons
            document.querySelectorAll('.remove-product').forEach(button => {
                button.removeEventListener('click', handleRemoveProduct);
                button.addEventListener('click', handleRemoveProduct);
            });
        }

        function handleProductChange(e) {
            const select = e.target;
            const productItem = select.closest('.product-item');
            const quantityInput = productItem.querySelector('.quantity-input');
            const itemTotal = productItem.querySelector('.item-total');
            
            if (select.value) {
                const option = select.selectedOptions[0];
                const price = parseFloat(option.dataset.price);
                const stock = parseInt(option.dataset.stock);
                
                quantityInput.max = stock;
                if (parseInt(quantityInput.value) > stock) {
                    quantityInput.value = stock;
                }
                
                updateItemTotal(productItem);
            } else {
                itemTotal.textContent = 'Select a product to see the total';
            }
            
            updateOrderSummary();
        }

        function handleRemoveProduct(e) {
            const productItem = e.target.closest('.product-item');
            productItem.remove();
            updateOrderSummary();
            updateRemoveButtons();
        }

        function updateItemTotal(productItem) {
            const select = productItem.querySelector('.product-select');
            const quantityInput = productItem.querySelector('.quantity-input');
            const itemTotal = productItem.querySelector('.item-total');
            
            if (select.value && quantityInput.value) {
                const option = select.selectedOptions[0];
                const price = parseFloat(option.dataset.price);
                const quantity = parseInt(quantityInput.value);
                const total = price * quantity;
                
                itemTotal.innerHTML = '<strong>Subtotal: $' + total.toFixed(2) + '</strong>';
            }
        }

        function updateOrderSummary() {
            let totalItems = 0;
            let totalAmount = 0;
            
            document.querySelectorAll('.product-item').forEach(item => {
                const select = item.querySelector('.product-select');
                const quantityInput = item.querySelector('.quantity-input');
                
                if (select.value && quantityInput.value) {
                    const option = select.selectedOptions[0];
                    const price = parseFloat(option.dataset.price);
                    const quantity = parseInt(quantityInput.value);
                    
                    totalItems += quantity;
                    totalAmount += price * quantity;
                    
                    updateItemTotal(item);
                }
            });
            
            document.getElementById('totalItems').textContent = totalItems;
            document.getElementById('totalAmount').textContent = '$' + totalAmount.toFixed(2);
        }

        function updateRemoveButtons() {
            const productItems = document.querySelectorAll('.product-item');
            const removeButtons = document.querySelectorAll('.remove-product');
            
            removeButtons.forEach((button, index) => {
                button.style.display = productItems.length > 1 ? 'block' : 'none';
            });
        }
    </script>
</x-app-layout>