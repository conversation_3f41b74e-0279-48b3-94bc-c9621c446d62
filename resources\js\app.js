// Import Bootstrap
import * as bootstrap from "bootstrap";
import "bootstrap/dist/js/bootstrap.bundle.min.js";

// Import Chart.js
import Chart from "chart.js/auto";

// Import ApexCharts
import ApexCharts from "apexcharts";

// Import SweetAlert2
import <PERSON>wal from "sweetalert2";

// Make Bootstrap, Chart.js, ApexCharts and SweetAlert2 globally available
window.bootstrap = bootstrap;
window.Chart = Chart;
window.ApexCharts = ApexCharts;
window.Swal = Swal;

// Dashboard JavaScript Functions
class Dashboard {
    constructor() {
        this.init();
    }

    init() {
        this.initSidebar();
        this.initCharts();
        this.initDataTables();
        this.initNotifications();
    }

    // Enhanced Sidebar functionality (merged from emergency-restore.js)
    initSidebar() {
        console.log("Enhanced sidebar initialization starting");

        // Add CSS fixes for collapsed sidebar dropdowns
        this.addSidebarCSS();

        // Initialize user dropdown functionality
        this.initUserDropdown();

        // Initialize sidebar toggle functionality
        this.initSidebarToggle();

        // Initialize sidebar dropdowns
        this.initSidebarDropdowns();

        // Initialize theme toggle
        this.initThemeToggle();

        // Initialize keyboard shortcuts
        this.initKeyboardShortcuts();

        // Active menu highlighting
        this.initActiveMenuHighlighting();

        console.log("Enhanced sidebar initialization completed");
    }

    // Add CSS fixes for collapsed sidebar dropdowns
    addSidebarCSS() {
        const style = document.createElement("style");
        style.textContent = `
            /* Fix collapsed sidebar dropdown positioning */
            .sidebar.collapsed .nav-item.has-dropdown .dropdown-menu {
                position: absolute !important;
                left: 70px !important;
                top: 0 !important;
                background: #2c3e50 !important;
                border-radius: 8px !important;
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
                min-width: 220px !important;
                z-index: 1050 !important;
                border: 1px solid rgba(255, 255, 255, 0.1) !important;
                display: none !important;
                margin: 0 !important;
                padding: 6px 0 !important;
            }
            
            .sidebar.collapsed .nav-item.has-dropdown:hover .dropdown-menu {
                display: block !important;
            }
            
            .sidebar.collapsed .dropdown-item {
                padding: 12px 20px !important;
                color: rgba(255, 255, 255, 0.9) !important;
                display: flex !important;
                align-items: center !important;
                text-align: left !important;
                justify-content: flex-start !important;
                white-space: nowrap !important;
            }
            
            .sidebar.collapsed .dropdown-item:hover {
                background: rgba(255, 255, 255, 0.15) !important;
                color: #ffffff !important;
                padding-left: 25px !important;
            }
            
            .sidebar.collapsed .dropdown-item i {
                margin-right: 12px !important;
                width: 18px !important;
                text-align: center !important;
                color: rgba(255, 255, 255, 0.9) !important;
            }
            
            .sidebar.collapsed .dropdown-item:hover i {
                color: #ffffff !important;
            }
            
            /* Ensure tooltips work for collapsed sidebar */
            .sidebar.collapsed .nav-link {
                position: relative;
            }
            
            .sidebar.collapsed .nav-link:hover::after {
                content: attr(data-title);
                position: absolute;
                left: calc(100% + 10px);
                top: 50%;
                transform: translateY(-50%);
                background: rgba(0, 0, 0, 0.8);
                color: white;
                padding: 8px 12px;
                border-radius: 6px;
                font-size: 0.875rem;
                white-space: nowrap;
                z-index: 1001;
                pointer-events: none;
                opacity: 0;
                animation: fadeInTooltip 0.3s ease 0.5s forwards;
            }
            
            @keyframes fadeInTooltip {
                from {
                    opacity: 0;
                    transform: translateY(-50%) translateX(-5px);
                }
                to {
                    opacity: 1;
                    transform: translateY(-50%) translateX(0);
                }
            }
            
            /* Hide tooltip when dropdown is showing */
            .sidebar.collapsed .nav-item.has-dropdown:hover .nav-link::after {
                display: none;
            }
        `;
        document.head.appendChild(style);
    }

    // Initialize user dropdown functionality
    initUserDropdown() {
        const userDropdown = document.getElementById("userDropdown");
        const headerDropdownMenu = document.querySelector(
            ".header-actions .dropdown-menu"
        );

        if (userDropdown && headerDropdownMenu) {
            console.log("Found user dropdown elements");

            userDropdown.onclick = function (e) {
                e.preventDefault();
                const isShown = headerDropdownMenu.classList.contains("show");

                if (isShown) {
                    headerDropdownMenu.classList.remove("show");
                    userDropdown.setAttribute("aria-expanded", "false");
                } else {
                    headerDropdownMenu.classList.add("show");
                    userDropdown.setAttribute("aria-expanded", "true");
                }
            };

            // Close on outside click
            document.onclick = function (e) {
                if (
                    !userDropdown.contains(e.target) &&
                    !headerDropdownMenu.contains(e.target)
                ) {
                    headerDropdownMenu.classList.remove("show");
                    userDropdown.setAttribute("aria-expanded", "false");
                }
            };
        }
    }

    // Initialize sidebar toggle functionality
    initSidebarToggle() {
        const sidebarToggleDesktop = document.getElementById(
            "sidebarToggleDesktop"
        );
        const sidebarToggleMobile = document.getElementById(
            "sidebarToggleMobile"
        );
        const sidebar = document.getElementById("sidebar");
        const mainContent = document.querySelector(".main-content");

        console.log("Sidebar toggle elements:", {
            desktop: !!sidebarToggleDesktop,
            mobile: !!sidebarToggleMobile,
            sidebar: !!sidebar,
            mainContent: !!mainContent,
        });

        // Load saved sidebar state and transition from immediate state
        const savedSidebarState = localStorage.getItem("sidebarCollapsed");
        
        // Only apply collapsed state on desktop (window width > 768px)
        if (window.innerWidth > 768 && savedSidebarState === "true" && sidebar && mainContent) {
            // Remove immediate class and apply normal collapsed classes
            document.documentElement.classList.remove('sidebar-collapsed-immediate');
            sidebar.classList.add("collapsed");
            mainContent.classList.add("sidebar-collapsed");
        } else {
            // Ensure immediate class is removed if not collapsed or on mobile
            document.documentElement.classList.remove('sidebar-collapsed-immediate');
        }

        // Desktop sidebar collapse/expand
        if (sidebarToggleDesktop && sidebar && mainContent) {
            sidebarToggleDesktop.onclick = function () {
                console.log("Desktop sidebar toggle clicked");
                const isCollapsed = sidebar.classList.contains("collapsed");

                if (isCollapsed) {
                    sidebar.classList.remove("collapsed");
                    mainContent.classList.remove("sidebar-collapsed");
                    localStorage.setItem("sidebarCollapsed", "false");
                    console.log("Sidebar expanded");
                } else {
                    sidebar.classList.add("collapsed");
                    mainContent.classList.add("sidebar-collapsed");
                    localStorage.setItem("sidebarCollapsed", "true");
                    console.log("Sidebar collapsed");
                }

                // Update dropdown behavior after state change
                setTimeout(() => this.updateDropdownBehavior(), 100);
            }.bind(this);
        }

        // Mobile sidebar toggle
        if (sidebarToggleMobile && sidebar) {
            sidebarToggleMobile.onclick = function () {
                console.log("Mobile sidebar toggle clicked");
                sidebar.classList.toggle("show");

                if (sidebar.classList.contains("show")) {
                    // Add overlay for mobile
                    const overlay = document.createElement("div");
                    overlay.className = "sidebar-overlay";
                    overlay.style.cssText = `
                        position: fixed;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        background: rgba(0, 0, 0, 0.5);
                        z-index: 999;
                        opacity: 0;
                        transition: opacity 0.3s ease;
                    `;
                    document.body.appendChild(overlay);

                    setTimeout(() => (overlay.style.opacity = "1"), 10);

                    overlay.onclick = function () {
                        sidebar.classList.remove("show");
                        overlay.style.opacity = "0";
                        setTimeout(() => {
                            if (overlay.parentNode) {
                                overlay.parentNode.removeChild(overlay);
                            }
                        }, 300);
                    };
                } else {
                    const overlay = document.querySelector(".sidebar-overlay");
                    if (overlay) {
                        overlay.style.opacity = "0";
                        setTimeout(() => {
                            if (overlay.parentNode) {
                                overlay.parentNode.removeChild(overlay);
                            }
                        }, 300);
                    }
                }
            };
        }

        // Sidebar header click toggle (breadcrumb area)
        const sidebarHeader = document.getElementById("sidebarHeader");
        if (
            sidebarHeader &&
            sidebar &&
            mainContent &&
            window.innerWidth > 768
        ) {
            sidebarHeader.onclick = function () {
                console.log("Sidebar header clicked");
                const isCollapsed = sidebar.classList.contains("collapsed");

                if (isCollapsed) {
                    sidebar.classList.remove("collapsed");
                    mainContent.classList.remove("sidebar-collapsed");
                    localStorage.setItem("sidebarCollapsed", "false");
                    console.log("Sidebar expanded via header");
                } else {
                    sidebar.classList.add("collapsed");
                    mainContent.classList.add("sidebar-collapsed");
                    localStorage.setItem("sidebarCollapsed", "true");
                    console.log("Sidebar collapsed via header");
                }

                // Update dropdown behavior after state change
                setTimeout(() => this.updateDropdownBehavior(), 100);
            }.bind(this);
        }
    }

    // Initialize sidebar dropdowns
    initSidebarDropdowns() {
        const dropdownToggles = document.querySelectorAll(
            ".sidebar-menu .dropdown-toggle"
        );
        console.log("Found sidebar toggles:", dropdownToggles.length);

        dropdownToggles.forEach(function (toggle) {
            toggle.onclick = function (e) {
                e.preventDefault();
                const parentItem = this.closest(".nav-item.has-dropdown");

                if (parentItem) {
                    const isOpen = parentItem.classList.contains("open");

                    // Close all others
                    document
                        .querySelectorAll(".nav-item.has-dropdown.open")
                        .forEach(function (item) {
                            if (item !== parentItem) {
                                item.classList.remove("open");
                            }
                        });

                    // Toggle current
                    if (isOpen) {
                        parentItem.classList.remove("open");
                    } else {
                        parentItem.classList.add("open");
                    }
                }
            };
        });

        // Enhanced collapsed sidebar behavior
        const navItems = document.querySelectorAll(".nav-item.has-dropdown");
        console.log(
            "Setting up collapsed behavior for",
            navItems.length,
            "dropdown items"
        );

        navItems.forEach(function (navItem) {
            const dropdownMenu = navItem.querySelector(".dropdown-menu");
            const mainNavLink = navItem.querySelector(
                ".nav-link.dropdown-toggle"
            );

            if (!dropdownMenu || !mainNavLink) return;

            // In collapsed mode, clicking nav link should navigate to first item instead of toggling
            mainNavLink.addEventListener("click", function (e) {
                const sidebar = document.getElementById("sidebar");
                if (sidebar && sidebar.classList.contains("collapsed")) {
                    e.preventDefault();
                    e.stopPropagation();
                    const firstDropdownItem =
                        dropdownMenu.querySelector(".dropdown-item");
                    if (firstDropdownItem && firstDropdownItem.href) {
                        console.log(
                            "Navigating to first item in collapsed mode:",
                            firstDropdownItem.href
                        );
                        window.location.href = firstDropdownItem.href;
                    } else {
                        console.log("No valid first dropdown item found");
                    }
                }
                // In expanded mode, the regular dropdown toggle functionality will handle the click
            });
        });
    }

    // Update dropdown behavior when sidebar state changes
    updateDropdownBehavior() {
        const sidebar = document.getElementById("sidebar");
        const navItems = document.querySelectorAll(".nav-item.has-dropdown");

        navItems.forEach(function (navItem) {
            if (sidebar && sidebar.classList.contains("collapsed")) {
                // Reset any open dropdowns when switching to collapsed mode
                navItem.classList.remove("open");
                console.log("Reset dropdown state for collapsed mode");
            }
        });
    }

    // Initialize theme toggle functionality
    initThemeToggle() {
        const themeToggle = document.getElementById("themeToggle");
        const themeIcon = document.getElementById("themeIcon");
        const html = document.documentElement;

        if (themeToggle && themeIcon) {
            // Load saved theme
            const currentTheme = localStorage.getItem("theme") || "light";
            html.setAttribute("data-theme-mode", currentTheme);
            this.updateThemeIcon(currentTheme);

            themeToggle.onclick = () => {
                const currentTheme = html.getAttribute("data-theme-mode");
                const newTheme = currentTheme === "dark" ? "light" : "dark";

                html.setAttribute("data-theme-mode", newTheme);
                localStorage.setItem("theme", newTheme);
                this.updateThemeIcon(newTheme);
                console.log("Theme toggled to:", newTheme);
            };
        }
    }

    // Update theme icon
    updateThemeIcon(theme) {
        const themeIcon = document.getElementById("themeIcon");
        const themeToggle = document.getElementById("themeToggle");

        if (themeIcon && themeToggle) {
            if (theme === "dark") {
                themeIcon.className = "fas fa-sun";
                themeToggle.title = "Switch to light mode";
            } else {
                themeIcon.className = "fas fa-moon";
                themeToggle.title = "Switch to dark mode";
            }
        }
    }

    // Initialize keyboard shortcuts
    initKeyboardShortcuts() {
        document.addEventListener("keydown", (e) => {
            const sidebar = document.getElementById("sidebar");
            const mainContent = document.querySelector(".main-content");

            // Ctrl/Cmd + B to toggle sidebar
            if (
                (e.ctrlKey || e.metaKey) &&
                e.key === "b" &&
                window.innerWidth > 768
            ) {
                e.preventDefault();
                if (sidebar && mainContent) {
                    const isCollapsed = sidebar.classList.contains("collapsed");

                    if (isCollapsed) {
                        sidebar.classList.remove("collapsed");
                        mainContent.classList.remove("sidebar-collapsed");
                        localStorage.setItem("sidebarCollapsed", "false");
                        console.log("Sidebar toggled via keyboard (expanded)");
                    } else {
                        sidebar.classList.add("collapsed");
                        mainContent.classList.add("sidebar-collapsed");
                        localStorage.setItem("sidebarCollapsed", "true");
                        console.log("Sidebar toggled via keyboard (collapsed)");
                    }

                    // Update dropdown behavior after state change
                    setTimeout(() => this.updateDropdownBehavior(), 100);
                }
            }

            // Escape to close mobile sidebar
            if (
                e.key === "Escape" &&
                sidebar &&
                sidebar.classList.contains("show")
            ) {
                sidebar.classList.remove("show");
                const overlay = document.querySelector(".sidebar-overlay");
                if (overlay) {
                    overlay.style.opacity = "0";
                    setTimeout(() => {
                        if (overlay.parentNode) {
                            overlay.parentNode.removeChild(overlay);
                        }
                    }, 300);
                }
            }
        });
    }

    // Active menu highlighting
    initActiveMenuHighlighting() {
        const currentPath = window.location.pathname;
        const menuLinks = document.querySelectorAll(".sidebar-menu .nav-link");

        menuLinks.forEach((link) => {
            if (link.getAttribute("href") === currentPath) {
                link.classList.add("active");
            }
        });
    }

    // Initialize charts
    initCharts() {
        this.initSalesChart();
        this.initVisitorsChart();
        this.initRevenueChart();
        this.initProductionOverviewChart();
    }

    // Sales Overview Chart
    initSalesChart() {
        const ctx = document.getElementById("salesChart");
        if (ctx) {
            window.salesChart = new Chart(ctx, {
                type: "line",
                data: {
                    labels: [
                        "Jan",
                        "Feb",
                        "Mar",
                        "Apr",
                        "May",
                        "Jun",
                        "Jul",
                        "Aug",
                        "Sep",
                        "Oct",
                        "Nov",
                        "Dec",
                    ],
                    datasets: [
                        {
                            label: "Sales",
                            data: [
                                12000, 19000, 15000, 25000, 22000, 30000, 28000,
                                35000, 32000, 40000, 38000, 45000,
                            ],
                            borderColor: "rgb(99, 102, 241)",
                            backgroundColor: "rgba(99, 102, 241, 0.1)",
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                        },
                    ],
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false,
                        },
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: "rgba(0, 0, 0, 0.1)",
                            },
                        },
                        x: {
                            grid: {
                                display: false,
                            },
                        },
                    },
                },
            });
        }
    }

    // Visitors Chart (Doughnut)
    initVisitorsChart() {
        const ctx = document.getElementById("visitorsChart");
        if (ctx) {
            new Chart(ctx, {
                type: "doughnut",
                data: {
                    labels: ["Desktop", "Mobile", "Tablet"],
                    datasets: [
                        {
                            data: [65, 25, 10],
                            backgroundColor: [
                                "rgb(99, 102, 241)",
                                "rgb(139, 92, 246)",
                                "rgb(16, 185, 129)",
                            ],
                            borderWidth: 0,
                        },
                    ],
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: "70%",
                    plugins: {
                        legend: {
                            position: "bottom",
                            labels: {
                                padding: 20,
                                usePointStyle: true,
                            },
                        },
                    },
                },
            });
        }
    }

    // Revenue Chart (Bar)
    initRevenueChart() {
        const ctx = document.getElementById("revenueChart");
        if (ctx) {
            new Chart(ctx, {
                type: "bar",
                data: {
                    labels: ["Q1", "Q2", "Q3", "Q4"],
                    datasets: [
                        {
                            label: "Revenue",
                            data: [450000, 520000, 600000, 750000],
                            backgroundColor: "rgba(99, 102, 241, 0.8)",
                            borderColor: "rgb(99, 102, 241)",
                            borderWidth: 1,
                            borderRadius: 8,
                            borderSkipped: false,
                        },
                    ],
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false,
                        },
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            grid: {
                                color: "rgba(0, 0, 0, 0.1)",
                            },
                        },
                        x: {
                            grid: {
                                display: false,
                            },
                        },
                    },
                },
            });
        }
    }

    // Production Overview Chart (ApexCharts)
    async initProductionOverviewChart() {
        const chartContainer = document.querySelector("#projects-overview");
        if (chartContainer && typeof ApexCharts !== "undefined") {
            // Get chart data from database
            const chartData = await this.fetchChartData();

            const options = {
                series: chartData.series,
                chart: {
                    type: "area",
                    height: 355,
                    animations: {
                        speed: 100,
                    },
                    toolbar: {
                        show: false,
                    },
                    zoom: {
                        enabled: false,
                    },
                    dropShadow: {
                        enabled: true,
                        enabledOnSeries: undefined,
                        top: 6,
                        left: 1,
                        blur: 4,
                        color: ["transparent", "#000", "transparent"],
                        opacity: 0.12,
                    },
                },
                colors: [
                    "rgba(253, 175, 34, 1)",
                    "var(--primary-color)",
                    "rgba(50, 212, 132, 1)",
                    "rgba(255, 73, 205, 1)",
                ],
                dataLabels: {
                    enabled: false,
                },
                markers: {
                    size: [6, 0, 0, 0],
                    colors: [
                        "rgba(253, 175, 34, 1)",
                        "var(--primary-color)",
                        "rgba(50, 212, 132, 1)",
                        "rgba(255, 73, 205, 1)",
                    ],
                    strokeColors: ["#fff", "#fff", "#fff", "#fff"],
                    strokeWidth: 2,
                    hover: {
                        size: 10,
                        sizeOffset: 2,
                    },
                },
                grid: {
                    borderColor: "#f1f1f1",
                    strokeDashArray: 2,
                    xaxis: {
                        lines: {
                            show: true,
                        },
                    },
                    yaxis: {
                        lines: {
                            show: false,
                        },
                    },
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shadeIntensity: 1,
                        opacityFrom: 0.4,
                        opacityTo: 0.1,
                        stops: [0, 90, 100],
                        colorStops: [
                            [
                                {
                                    offset: 0,
                                    color: "rgba(253, 175, 34, 0.1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 75,
                                    color: "rgba(253, 175, 34, 0.1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 100,
                                    color: "rgba(253, 175, 34, 0.1)",
                                    opacity: 1,
                                },
                            ],
                            [
                                {
                                    offset: 0,
                                    color: "var(--primary-color)",
                                    opacity: 1,
                                },
                                {
                                    offset: 75,
                                    color: "var(--primary-color)",
                                    opacity: 1,
                                },
                                {
                                    offset: 100,
                                    color: "var(--primary-color)",
                                    opacity: 1,
                                },
                            ],
                            [
                                {
                                    offset: 0,
                                    color: "rgba(50, 212, 132, 1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 75,
                                    color: "rgba(50, 212, 132, 1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 100,
                                    color: "rgba(50, 212, 132, 1)",
                                    opacity: 1,
                                },
                            ],
                            [
                                {
                                    offset: 0,
                                    color: "rgba(255, 73, 205, 1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 75,
                                    color: "rgba(255, 73, 205, 1)",
                                    opacity: 1,
                                },
                                {
                                    offset: 100,
                                    color: "rgba(255, 73, 205, 1)",
                                    opacity: 1,
                                },
                            ],
                        ],
                    },
                },
                stroke: {
                    curve: ["smooth", "smooth", "smooth", "smooth"],
                    width: [4, 0, 0, 0],
                    dashArray: [4, 0, 0, 0],
                },
                xaxis: {
                    axisTicks: {
                        show: false,
                    },
                },
                yaxis: {
                    labels: {
                        formatter: function (value) {
                            return value + "M PCS";
                        },
                    },
                },
                plotOptions: {
                    bar: {
                        columnWidth: "40%",
                        borderRadius: "3",
                    },
                },
                tooltip: {
                    y: [
                        {
                            formatter: function (e) {
                                return void 0 !== e
                                    ? e.toFixed(0) + "M PCS"
                                    : e;
                            },
                        },
                        {
                            formatter: function (e) {
                                return void 0 !== e
                                    ? e.toFixed(0) + "M PCS"
                                    : e;
                            },
                        },
                        {
                            formatter: function (e) {
                                return void 0 !== e
                                    ? e.toFixed(0) + "M PCS"
                                    : e;
                            },
                        },
                        {
                            formatter: function (e) {
                                return void 0 !== e
                                    ? e.toFixed(0) + "M PCS"
                                    : e;
                            },
                        },
                    ],
                },
                legend: {
                    show: true,
                    position: "top",
                    markers: {
                        size: 5,
                        strokeWidth: 0,
                    },
                },
            };

            const manufacturingChart = new ApexCharts(chartContainer, options);
            manufacturingChart.render();

            // Store reference globally for potential updates
            window.manufacturingChart = manufacturingChart;
        }
    }

    // Fetch chart data from API using current dashboard filters
    async fetchChartData() {
        try {
            const params = new URLSearchParams({
                dashboard_date:
                    document.getElementById("dashboard_date")?.value ||
                    new Date().toISOString().split("T")[0],
                dashboard_shift:
                    document.getElementById("dashboard_shift")?.value || "all",
                dashboard_cutoff:
                    document.getElementById("dashboard_cutoff")?.value || "all",
                dashboard_work_type:
                    document.getElementById("dashboard_work_type")?.value ||
                    "all",
            });

            const response = await fetch(
                `/api/manufacturing-overview?${params.toString()}`
            );
            const result = await response.json();

            if (result.success) {
                return result.data;
            } else {
                console.error("Failed to fetch chart data:", result.message);
                return this.getFallbackChartData();
            }
        } catch (error) {
            console.error("Error fetching chart data:", error);
            return this.getFallbackChartData();
        }
    }

    // Fallback chart data in case API fails
    getFallbackChartData() {
        return {
            labels: [
                "Line A",
                "Line B",
                "Line C",
                "Line D",
                "Line E",
                "Line F",
                "Line G",
                "Line H",
                "Line I",
                "Line J",
                "Line K",
            ],
            series: [
                {
                    type: "area",
                    name: "Target",
                    data: [
                        { x: "Line A", y: 0.68 },
                        { x: "Line B", y: 0.8 },
                        { x: "Line C", y: 0.68 },
                        { x: "Line D", y: 0.84 },
                        { x: "Line E", y: 0.98 },
                        { x: "Line F", y: 0.72 },
                        { x: "Line G", y: 0.9 },
                        { x: "Line H", y: 1.0 },
                        { x: "Line I", y: 0.85 },
                        { x: "Line J", y: 0.95 },
                        { x: "Line K", y: 0.75 },
                    ],
                },
                {
                    type: "bar",
                    name: "Endtime",
                    data: [
                        { x: "Line A", y: 0.32 },
                        { x: "Line B", y: 0.56 },
                        { x: "Line C", y: 0.25 },
                        { x: "Line D", y: 0.49 },
                        { x: "Line E", y: 0.31 },
                        { x: "Line F", y: 0.56 },
                        { x: "Line G", y: 0.56 },
                        { x: "Line H", y: 0.86 },
                        { x: "Line I", y: 0.4 },
                        { x: "Line J", y: 0.5 },
                        { x: "Line K", y: 0.35 },
                    ],
                },
                {
                    type: "bar",
                    name: "Submitted",
                    chart: {
                        dropShadow: {
                            enabled: true,
                            enabledOnSeries: undefined,
                            top: 5,
                            left: 0,
                            blur: 3,
                            color: "#000",
                            opacity: 0.1,
                        },
                    },
                    data: [
                        { x: "Line A", y: 0.18 },
                        { x: "Line B", y: 0.25 },
                        { x: "Line C", y: 0.3 },
                        { x: "Line D", y: 0.35 },
                        { x: "Line E", y: 0.35 },
                        { x: "Line F", y: 0.25 },
                        { x: "Line G", y: 0.15 },
                        { x: "Line H", y: 0.25 },
                        { x: "Line I", y: 0.35 },
                        { x: "Line J", y: 0.35 },
                        { x: "Line K", y: 0.2 },
                    ],
                },
                {
                    type: "bar",
                    name: "Remaining",
                    data: [
                        { x: "Line A", y: 0.28 },
                        { x: "Line B", y: 0.32 },
                        { x: "Line C", y: 0.23 },
                        { x: "Line D", y: 0.19 },
                        { x: "Line E", y: 0.32 },
                        { x: "Line F", y: 0.21 },
                        { x: "Line G", y: 0.35 },
                        { x: "Line H", y: 0.49 },
                        { x: "Line I", y: 0.3 },
                        { x: "Line J", y: 0.3 },
                        { x: "Line K", y: 0.25 },
                    ],
                },
            ],
        };
    }

    // Initialize DataTables
    initDataTables() {
        // Note: DataTables will be initialized when we add the DataTables library
        const tables = document.querySelectorAll(".data-table");
        tables.forEach((table) => {
            if (table && typeof $.fn.DataTable !== "undefined") {
                $(table).DataTable({
                    responsive: true,
                    pageLength: 10,
                    order: [[0, "desc"]],
                    language: {
                        search: "Search:",
                        lengthMenu: "Show _MENU_ entries",
                        info: "Showing _START_ to _END_ of _TOTAL_ entries",
                        paginate: {
                            first: "First",
                            last: "Last",
                            next: "Next",
                            previous: "Previous",
                        },
                    },
                });
            }
        });
    }

    // Notifications
    initNotifications() {
        // Success notification
        window.showSuccess = (message) => {
            Swal.fire({
                icon: "success",
                title: "Success!",
                text: message,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: "top-end",
            });
        };

        // Error notification
        window.showError = (message) => {
            Swal.fire({
                icon: "error",
                title: "Error!",
                text: message,
                timer: 3000,
                showConfirmButton: false,
                toast: true,
                position: "top-end",
            });
        };

        // Confirmation dialog
        window.showConfirm = (message, callback) => {
            Swal.fire({
                title: "Are you sure?",
                text: message,
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#6366f1",
                cancelButtonColor: "#ef4444",
                confirmButtonText: "Yes, do it!",
                cancelButtonText: "Cancel",
            }).then((result) => {
                if (result.isConfirmed && callback) {
                    callback();
                }
            });
        };
    }

    // Utility functions
    static formatCurrency(amount) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "USD",
        }).format(amount);
    }

    static formatDate(date) {
        return new Intl.DateTimeFormat("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        }).format(new Date(date));
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
    new Dashboard();
});

// Make Dashboard class globally available
window.Dashboard = Dashboard;

// ==================== DASHBOARD SPECIFIC FUNCTIONS ====================

/**
 * Production Dashboard Functions
 * Handles auto refresh, shift/cutoff detection, and data updates
 */
class ProductionDashboard {
    constructor() {
        this.autoRefreshInterval = null;
        this.init();
    }

    init() {
        // Only initialize if we're on the dashboard page
        if (document.getElementById("autoRefreshToggle")) {
            this.initializeDashboardFilters();
            this.handleShiftChange();
            this.initializeEquipmentStatusChart();
            this.bindEvents();
        }
    }

    bindEvents() {
        // Make functions globally available for onclick handlers
        window.toggleAutoRefresh = this.toggleAutoRefresh.bind(this);
        window.handleIntervalChange = this.handleIntervalChange.bind(this);
        window.handleShiftChange = this.handleShiftChange.bind(this);
        window.updateDashboardData = this.updateDashboardData.bind(this);
        window.resetDashboardFilters = this.resetDashboardFilters.bind(this);
    }

    /**
     * Get current shift and cutoff based on Asia/Manila timezone
     * Shift Schedule:
     * - Day Shift: 07:00 - 18:59 (12 hours)
     * - Night Shift: 19:00 - 06:59 (12 hours, spans 2 days)
     * Cutoff Schedule (4 hours each):
     * - 1st: First 4 hours of shift
     * - 2nd: Next 4 hours of shift
     * - 3rd: Last 4 hours of shift
     */
    getCurrentShiftAndCutoff() {
        // Get current time in Asia/Manila timezone
        const now = new Date();
        const manilaTime = new Date(
            now.toLocaleString("en-US", { timeZone: "Asia/Manila" })
        );
        const hours = manilaTime.getHours();
        const minutes = manilaTime.getMinutes();

        console.log("Current Manila time:", {
            originalTime: now.toISOString(),
            manilaTime: manilaTime.toISOString(),
            hours: hours,
            minutes: minutes,
            timeString: `${hours.toString().padStart(2, "0")}:${minutes
                .toString()
                .padStart(2, "0")}`,
        });

        let shift, cutoff, shiftDate;

        if (hours >= 7 && hours <= 18) {
            // Day Shift: 07:00 - 18:59
            shift = "day";
            shiftDate = manilaTime; // Same day

            if (hours >= 7 && hours <= 10) {
                cutoff = "1"; // 07:00 - 10:59
            } else if (hours >= 11 && hours <= 14) {
                cutoff = "2"; // 11:00 - 14:59
            } else {
                cutoff = "3"; // 15:00 - 18:59
            }
        } else {
            // Night Shift: 19:00 - 06:59 (spans 2 days)
            shift = "night";

            if (hours >= 19 && hours <= 23) {
                // First part of night shift (19:00 - 23:59) - same day
                shiftDate = manilaTime;
                cutoff = "1"; // 19:00 - 22:59
            } else if (hours >= 0 && hours <= 6) {
                // Second part of night shift (00:00 - 06:59) - next day, but shift started previous day
                shiftDate = new Date(manilaTime);
                shiftDate.setDate(shiftDate.getDate() - 1); // Use previous day for shift date

                if (hours >= 0 && hours <= 2) {
                    cutoff = "2"; // 23:00 - 02:59 (crosses midnight)
                } else {
                    cutoff = "3"; // 03:00 - 06:59
                }
            } else {
                // Edge case: 23:00 - 23:59 for night shift 1st cutoff
                shiftDate = manilaTime;
                cutoff = "1";
            }
        }

        // Format date as YYYY-MM-DD for the date input
        const shiftDateString =
            shiftDate.getFullYear() +
            "-" +
            String(shiftDate.getMonth() + 1).padStart(2, "0") +
            "-" +
            String(shiftDate.getDate()).padStart(2, "0");

        return {
            shift,
            cutoff,
            date: shiftDateString,
            currentTime: `${hours.toString().padStart(2, "0")}:${minutes
                .toString()
                .padStart(2, "0")}`,
            shiftDisplay: shift === "day" ? "Day" : "Night",
        };
    }

    toggleAutoRefresh() {
        const toggle = document.getElementById("autoRefreshToggle");
        const status = document.getElementById("refreshStatus");
        const intervalInput = document.getElementById("refreshInterval");

        console.log("Toggle Auto Refresh called, checked:", toggle.checked);

        if (toggle.checked) {
            // Auto-set to current shift and cutoff
            const currentShiftInfo = this.getCurrentShiftAndCutoff();
            console.log("Current shift info:", currentShiftInfo);

            // Update UI selectors
            document.getElementById("dashboard_date").value =
                currentShiftInfo.date;
            document.getElementById("dashboard_shift").value =
                currentShiftInfo.shift;
            document.getElementById("dashboard_cutoff").value =
                currentShiftInfo.cutoff;

            console.log("Updated UI selectors to:", {
                date: currentShiftInfo.date,
                shift: currentShiftInfo.shift,
                cutoff: currentShiftInfo.cutoff,
            });

            // Enable cutoff selector since we're setting a specific shift
            document.getElementById("dashboard_cutoff").disabled = false;

            // Start auto refresh
            const intervalSeconds = parseInt(intervalInput.value) || 30;
            const intervalMs = intervalSeconds * 1000;

            this.autoRefreshInterval = setInterval(() => {
                this.updateDashboardData(true);
            }, intervalMs);
            status.style.display = "inline-block";

            // Show detailed notification
            // this.showToast(
            //     `Auto refresh enabled: ${
            //         currentShiftInfo.shiftDisplay
            //     } shift, ${currentShiftInfo.cutoff}${this.getSuffix(
            //         currentShiftInfo.cutoff
            //     )} cutoff (${currentShiftInfo.currentTime})`,
            //     "success"
            // );

            console.log("About to call updateDashboardData()");
            // Update dashboard with new settings
            this.updateDashboardData();
        } else {
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
            status.style.display = "none";
            this.showToast("Auto refresh disabled", "info");
        }
    }

    // Helper function to get ordinal suffix (1st, 2nd, 3rd)
    getSuffix(cutoff) {
        if (cutoff === "1") return "st";
        if (cutoff === "2") return "nd";
        if (cutoff === "3") return "rd";
        return "th";
    }

    handleIntervalChange() {
        const toggle = document.getElementById("autoRefreshToggle");
        const intervalInput = document.getElementById("refreshInterval");

        let intervalValue = parseInt(intervalInput.value);
        if (intervalValue < 5) {
            intervalValue = 5;
            intervalInput.value = 5;
        } else if (intervalValue > 300) {
            intervalValue = 300;
            intervalInput.value = 300;
        }

        if (toggle.checked && this.autoRefreshInterval) {
            clearInterval(this.autoRefreshInterval);
            const intervalMs = intervalValue * 1000;
            this.autoRefreshInterval = setInterval(() => {
                this.updateDashboardData(true);
            }, intervalMs);
        }
    }

    handleShiftChange() {
        const shiftSelector = document.getElementById("dashboard_shift");
        const cutoffSelector = document.getElementById("dashboard_cutoff");

        if (shiftSelector.value === "all") {
            cutoffSelector.disabled = true;
            cutoffSelector.value = "all";
        } else {
            cutoffSelector.disabled = false;
            if (cutoffSelector.value === "all") {
                cutoffSelector.value = "1";
            }
        }
        // Update both dashboard cards and chart
        this.updateDashboardData();
    }

    updateDashboardData(isAutoRefresh = false) {
        const date = document.getElementById("dashboard_date").value;
        const shift = document.getElementById("dashboard_shift").value;
        const cutoff = document.getElementById("dashboard_cutoff").value;
        const workType = document.getElementById("dashboard_work_type").value;

        console.log("updateDashboardData called with filters:", {
            date: date,
            shift: shift,
            cutoff: cutoff,
            workType: workType,
            isAutoRefresh: isAutoRefresh,
        });

        if (!isAutoRefresh) {
            this.showLoadingState();
        }

        const url = new URL("/api/dashboard-stats", window.location.origin);
        url.searchParams.set("dashboard_date", date);
        url.searchParams.set("dashboard_shift", shift);
        url.searchParams.set("dashboard_cutoff", cutoff);
        url.searchParams.set("dashboard_work_type", workType);

        console.log("Making API call to:", url.toString());

        fetch(url, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                "X-CSRF-TOKEN": document
                    .querySelector('meta[name="csrf-token"]')
                    .getAttribute("content"),
            },
        })
            .then((response) => response.json())
            .then((data) => {
                console.log("API Response received:", data);
                if (data.success) {
                    console.log(
                        "Updating dashboard cards with stats:",
                        data.stats
                    );
                    console.log("Updating summary tables with:", {
                        perLineSummary: data.perLineSummary,
                        perSizeSummary: data.perSizeSummary,
                        linePerformanceAnalysis: data.linePerformanceAnalysis,
                    });
                    this.updateDashboardCards(data.stats);
                    // Update Per Line and Per Size Summary tables
                    this.updateSummaryTables(
                        data.perLineSummary,
                        data.perSizeSummary
                    );
                    // Update Line Performance Analysis
                    if (data.linePerformanceAnalysis) {
                        this.updateLinePerformanceAnalysis(
                            data.linePerformanceAnalysis
                        );
                    }
                    // Update dynamic panels
                    if (data.previousShiftAchievement) {
                        this.updatePreviousShiftAchievement(
                            data.previousShiftAchievement
                        );
                    }
                    if (data.currentPerformanceMonitor) {
                        this.updateCurrentPerformanceMonitor(
                            data.currentPerformanceMonitor
                        );
                    }
                    // Also update the chart with new filter settings
                    this.updateChart();
                    if (!isAutoRefresh) {
                        this.showToast(
                            "Dashboard updated successfully",
                            "success"
                        );
                    }
                } else {
                    console.log("API call failed:", data.message);
                    this.showToast("Failed to update dashboard", "error");
                }
            })
            .catch((error) => {
                console.error("Dashboard update error:", error);
                this.showToast("Error updating dashboard", "error");
            })
            .finally(() => {
                this.hideLoadingState();
            });
    }

    updateDashboardCards(stats) {
        function formatNumber(num) {
            if (num === null || num === undefined) return 0;
            return parseInt(num).toLocaleString();
        }

        function formatPCS(num) {
            if (num === null || num === undefined) return "0";
            const millions = parseFloat(num) / 1000000;
            return millions.toFixed(1).replace(/\B(?=(\d{3})+(?!\d))/g, ",");
        }

        // Update main cards
        document.getElementById("targetCapacity").textContent = `${formatPCS(
            stats.target_capacity
        )} M PCS`;
        document.getElementById("targetBadge").textContent = `${formatNumber(
            stats.equipment_count
        )} EQP Count`;

        document.getElementById("totalEndtime").textContent = `${formatPCS(
            stats.total_quantity
        )} M PCS`;
        document.getElementById("totalPcs").textContent = `${formatNumber(
            stats.total_lots
        )} LOTS`;

        document.getElementById("submittedLots").textContent = `${formatPCS(
            stats.submitted_quantity
        )} M PCS`;
        document.getElementById("submittedPcs").textContent = `${formatNumber(
            stats.submitted_lots
        )} LOTS`;

        document.getElementById("ongoingLots").textContent = `${formatPCS(
            stats.ongoing_quantity
        )} M PCS`;
        document.getElementById("ongoingPcs").textContent = `${formatNumber(
            stats.ongoing_lots
        )} LOTS`;

        // Update percentages
        const submittedPercentageElement = document
            .querySelector("#submittedLots")
            .parentElement.querySelector("small");
        if (submittedPercentageElement) {
            submittedPercentageElement.textContent = `${(
                stats.submitted_percentage || 0
            ).toFixed(1)}%`;
        }

        const ongoingPercentageElement = document
            .querySelector("#ongoingLots")
            .parentElement.querySelector("small");
        if (ongoingPercentageElement) {
            ongoingPercentageElement.textContent = `${(
                stats.ongoing_percentage || 0
            ).toFixed(1)}%`;
        }

        // Update Equipment Status data
        this.updateEquipmentStatus(stats);

        // Update progress bar values (for production progress bars)
        this.updateProgressBars(stats);
    }

    updateProgressBars(stats) {
        const targetCapacity = parseFloat(stats.target_capacity || 0);
        const totalQuantity = parseFloat(stats.total_quantity || 0);
        const submittedQuantity = parseFloat(stats.submitted_quantity || 0);
        const ongoingQuantity = parseFloat(stats.ongoing_quantity || 0);

        // Calculate percentages for progress bar widths
        const endtimePercent =
            targetCapacity > 0
                ? Math.min((totalQuantity / targetCapacity) * 100, 100)
                : 0;
        const submittedPercent =
            totalQuantity > 0
                ? Math.min((submittedQuantity / totalQuantity) * 100, 100)
                : 0;
        const remainingPercent =
            totalQuantity > 0
                ? Math.min((ongoingQuantity / totalQuantity) * 100, 100)
                : 0;

        // Update only the progress bar widths
        const progressBars = document.querySelectorAll(
            ".enhanced-progress-fill"
        );
        if (progressBars[0]) progressBars[0].style.width = "100%"; // Target always 100%
        if (progressBars[1]) progressBars[1].style.width = endtimePercent + "%";
        if (progressBars[2])
            progressBars[2].style.width = submittedPercent + "%";
        if (progressBars[3])
            progressBars[3].style.width = remainingPercent + "%";
    }

    resetDashboardFilters() {
        // Get current date in Asia/Manila timezone
        const now = new Date();
        const manilaTime = new Date(
            now.toLocaleString("en-US", { timeZone: "Asia/Manila" })
        );
        const currentDateString =
            manilaTime.getFullYear() +
            "-" +
            String(manilaTime.getMonth() + 1).padStart(2, "0") +
            "-" +
            String(manilaTime.getDate()).padStart(2, "0");

        const cutoffSelector = document.getElementById("dashboard_cutoff");

        document.getElementById("dashboard_date").value = currentDateString;
        document.getElementById("dashboard_shift").value = "all";
        document.getElementById("dashboard_work_type").value = "all";

        cutoffSelector.value = "all";
        cutoffSelector.disabled = true;

        // Turn off auto refresh if it was on
        const toggle = document.getElementById("autoRefreshToggle");
        const status = document.getElementById("refreshStatus");
        if (toggle.checked) {
            toggle.checked = false;
            if (this.autoRefreshInterval) {
                clearInterval(this.autoRefreshInterval);
                this.autoRefreshInterval = null;
            }
            status.style.display = "none";
        }

        this.updateDashboardData();
        this.showToast("Filters reset to current date", "info");
    }

    /**
     * Initialize dashboard filters based on auto refresh state
     * If auto refresh is OFF, set to current date with default filters
     * If auto refresh is ON, maintain current state (shouldn't happen on page load)
     */
    initializeDashboardFilters() {
        const toggle = document.getElementById("autoRefreshToggle");
        const dateInput = document.getElementById("dashboard_date");
        const shiftSelector = document.getElementById("dashboard_shift");
        const cutoffSelector = document.getElementById("dashboard_cutoff");
        const workTypeSelector = document.getElementById("dashboard_work_type");

        // Check if auto refresh toggle is OFF (default state after page load)
        if (!toggle.checked) {
            // Set to current date (Asia/Manila timezone)
            const now = new Date();
            const manilaTime = new Date(
                now.toLocaleString("en-US", { timeZone: "Asia/Manila" })
            );
            const currentDateString =
                manilaTime.getFullYear() +
                "-" +
                String(manilaTime.getMonth() + 1).padStart(2, "0") +
                "-" +
                String(manilaTime.getDate()).padStart(2, "0");

            // Set default values for manual mode
            dateInput.value = currentDateString;
            shiftSelector.value = "all";
            workTypeSelector.value = "all";
            cutoffSelector.value = "all";
            cutoffSelector.disabled = true;

            console.log(
                `Dashboard initialized: Current date (${currentDateString}), Auto refresh OFF`
            );
        } else {
            // This case shouldn't normally happen on page load, but handle it gracefully
            console.log(
                "Dashboard initialized: Auto refresh ON state detected"
            );
        }

        // Update dashboard with initial settings
        this.updateDashboardData();
    }

    showLoadingState() {
        const cards = document.querySelectorAll(".dashboard-main-card h5");
        cards.forEach((card) => {
            card.style.opacity = "0.5";
        });
    }

    hideLoadingState() {
        const cards = document.querySelectorAll(".dashboard-main-card h5");
        cards.forEach((card) => {
            card.style.opacity = "1";
        });
    }

    showToast(message, type) {
        const toast = document.createElement("div");
        toast.className = `alert alert-${
            type === "success"
                ? "success"
                : type === "error"
                ? "danger"
                : "info"
        } alert-dismissible fade show position-fixed`;

        toast.style.cssText = `
            top: 70px; 
            right: 20px; 
            z-index: 1060; 
            min-width: 300px;
            max-width: 400px;
            font-size: 0.875rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        `;

        toast.innerHTML = `
            <i class="fas fa-${
                type === "success"
                    ? "check-circle"
                    : type === "error"
                    ? "exclamation-circle"
                    : "info-circle"
            } me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }

    // Equipment Status gradient radial chart initialization
    initializeEquipmentStatusChart() {
        const chartContainer = document.getElementById(
            "equipment-status-chart"
        );
        if (!chartContainer || typeof ApexCharts === "undefined") {
            return;
        }

        try {
            // Get initial data from global variable set by PHP
            const dashboardStats = window.dashboardStats || {};
            const totalEquipment = dashboardStats.total_equipment || 0;
            const equipmentWithOngoing =
                dashboardStats.equipment_with_ongoing || 0;
            const percentage = dashboardStats.equipment_status_percentage || 0;

            const gradientOptions = {
                series: [percentage],
                chart: {
                    height: 350,
                    type: "radialBar",
                    toolbar: {
                        show: false,
                    },
                },
                plotOptions: {
                    radialBar: {
                        startAngle: -135,
                        endAngle: 225,
                        hollow: {
                            margin: 0,
                            size: "50%",
                            background: "transparent",
                            image: undefined,
                            imageOffsetX: 0,
                            imageOffsetY: 0,
                            position: "front",
                            dropShadow: {
                                enabled: true,
                                top: 3,
                                left: 0,
                                blur: 4,
                                opacity: 0.24,
                            },
                        },
                        track: {
                            background: "#e7e7e7",
                            strokeWidth: "67%",
                            margin: 0,
                            dropShadow: {
                                enabled: true,
                                top: -3,
                                left: 0,
                                blur: 4,
                                opacity: 0.35,
                            },
                        },
                        dataLabels: {
                            show: true,
                            name: {
                                offsetY: -10,
                                show: true,
                                color: "#888",
                                fontSize: "17px",
                                fontWeight: "normal",
                            },
                            value: {
                                formatter: function (val) {
                                    return parseInt(val) + "%";
                                },
                                color: "#111",
                                fontSize: "30px",
                                show: true,
                                fontWeight: "bold",
                            },
                        },
                    },
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shade: "dark",
                        type: "horizontal",
                        shadeIntensity: 0.5,
                        gradientToColors: ["#d77cf7"],
                        inverseColors: true,
                        opacityFrom: 1,
                        opacityTo: 1,
                        stops: [0, 100],
                    },
                },
                stroke: {
                    lineCap: "round",
                },
                labels: ["RUNNING"],
                colors: ["#AB7CF8"],
            };

            window.equipmentStatusChart = new ApexCharts(
                chartContainer,
                gradientOptions
            );
            window.equipmentStatusChart.render();
        } catch (error) {
            console.error("Error initializing equipment status chart:", error);
        }
    }

    // Initialize Equipment Status Chart in ProductionDashboard class
    initializeEquipmentStatusChart() {
        const chartContainer = document.getElementById(
            "equipment-status-chart"
        );
        if (!chartContainer || typeof ApexCharts === "undefined") {
            return;
        }

        try {
            // Get initial data from global variable set by PHP
            const dashboardStats = window.dashboardStats || {};
            const percentage = dashboardStats.equipment_status_percentage || 0;

            const gradientOptions = {
                series: [percentage],
                chart: {
                    height: 350,
                    type: "radialBar",
                    toolbar: {
                        show: false,
                    },
                },
                plotOptions: {
                    radialBar: {
                        startAngle: -135,
                        endAngle: 225,
                        hollow: {
                            margin: 0,
                            size: "70%",
                            background: "transparent",
                            dropShadow: {
                                enabled: true,
                                top: 3,
                                left: 0,
                                blur: 4,
                                opacity: 0.24,
                            },
                        },
                        track: {
                            background: "#e7e7e7",
                            strokeWidth: "70%",
                            margin: 0,
                            dropShadow: {
                                enabled: true,
                                top: -3,
                                left: 0,
                                blur: 4,
                                opacity: 0.35,
                            },
                        },
                        dataLabels: {
                            show: true,
                            name: {
                                offsetY: -10,
                                show: true,
                                color: "#888",
                                fontSize: "17px",
                                fontWeight: "normal",
                            },
                            value: {
                                formatter: function (val) {
                                    return parseInt(val) + "%";
                                },
                                color: "#111",
                                fontSize: "36px",
                                show: true,
                                fontWeight: "bold",
                            },
                        },
                    },
                },
                fill: {
                    type: "gradient",
                    gradient: {
                        shade: "dark",
                        type: "horizontal",
                        shadeIntensity: 0.5,
                        gradientToColors: ["#d77cf7"],
                        inverseColors: true,
                        opacityFrom: 1,
                        opacityTo: 1,
                        stops: [0, 100],
                    },
                },
                stroke: {
                    lineCap: "round",
                },
                labels: ["RUNNING"],
                colors: ["#AB7CF8"],
            };

            window.equipmentStatusChart = new ApexCharts(
                chartContainer,
                gradientOptions
            );
            window.equipmentStatusChart.render();
        } catch (error) {
            console.error("Error initializing equipment status chart:", error);
        }
    }

    // Update Per Line and Per Size Summary tables
    updateSummaryTables(perLineSummary, perSizeSummary) {
        // Update Per Line Summary table
        if (perLineSummary) {
            this.updatePerLineTable(perLineSummary);
        }

        // Update Per Size Summary table
        if (perSizeSummary) {
            this.updatePerSizeTable(perSizeSummary);
        }
    }

    // Update Line Performance Analysis section
    updateLinePerformanceAnalysis(data) {
        console.log("Updating Line Performance Analysis with:", data);

        // Store all lines data for click handlers
        window.allLinesData = data.all_lines || {};

        // Update Top Performers section
        this.updatePerformanceSection(
            "top_performers",
            data.top_performers || [],
            "bg-success-transparent",
            "bg-success",
            "text-success"
        );

        // Update Average Performance section
        this.updatePerformanceSection(
            "average_performance",
            data.average_performance || [],
            "bg-light",
            "bg-info",
            "text-info"
        );

        // Update Needs Attention section
        this.updatePerformanceSection(
            "needs_attention",
            data.needs_attention || [],
            "bg-warning-transparent",
            "bg-warning",
            "text-warning"
        );

        // Update Area Performance
        this.updateAreaPerformance(data.area_performance || {});

        // Update selected line display
        const selectedLineDisplay = document.getElementById(
            "selected-line-display"
        );
        if (selectedLineDisplay) {
            selectedLineDisplay.textContent = data.selected_line || "A";
        }

        // Bind click handlers to line items
        this.bindLineClickHandlers();
    }

    // Bind click handlers to line performance items
    bindLineClickHandlers() {
        const lineItems = document.querySelectorAll(".line-clickable");
        lineItems.forEach((item) => {
            item.addEventListener("click", (e) => {
                const line = e.currentTarget.dataset.line;
                if (line) {
                    this.selectLine(line);
                }
            });
        });
    }

    // Handle line selection and update area performance
    async selectLine(selectedLine) {
        console.log("Line selected:", selectedLine);

        // Update selected line display
        const selectedLineDisplay = document.getElementById(
            "selected-line-display"
        );
        if (selectedLineDisplay) {
            selectedLineDisplay.textContent = selectedLine;
        }

        // Visual feedback - highlight selected line
        document.querySelectorAll(".line-clickable").forEach((item) => {
            item.classList.remove("line-selected");
        });
        document
            .querySelector(`[data-line="${selectedLine}"]`)
            ?.classList.add("line-selected");

        // Fetch area performance for selected line
        try {
            const params = new URLSearchParams({
                line: selectedLine,
                dashboard_date:
                    document.getElementById("dashboard_date")?.value ||
                    new Date().toISOString().split("T")[0],
                dashboard_shift:
                    document.getElementById("dashboard_shift")?.value || "all",
                dashboard_cutoff:
                    document.getElementById("dashboard_cutoff")?.value || "all",
                dashboard_work_type:
                    document.getElementById("dashboard_work_type")?.value ||
                    "all",
            });

            const response = await fetch(
                `/api/line-area-performance?${params.toString()}`
            );
            const result = await response.json();

            if (result.success) {
                this.updateAreaPerformance(result.area_performance);
            } else {
                console.error(
                    "Failed to get line area performance:",
                    result.message
                );
            }
        } catch (error) {
            console.error("Error fetching line area performance:", error);
        }
    }

    // Update a specific performance section (Top Performers, Average, Needs Attention)
    updatePerformanceSection(
        sectionKey,
        performersData,
        bgClass,
        badgeClass,
        textClass
    ) {
        // Find the section by looking for the specific heading
        const sectionHeadings = {
            top_performers: "Top Performers",
            average_performance: "Average Performance",
            needs_attention: "Needs Attention",
        };

        const sectionTitle = sectionHeadings[sectionKey];
        const headingElement = Array.from(document.querySelectorAll("h6")).find(
            (h) => h.textContent.includes(sectionTitle)
        );

        if (!headingElement) {
            console.warn(`Could not find section heading for: ${sectionTitle}`);
            return;
        }

        // Update the count badge
        const countBadge = headingElement.querySelector(".badge");
        if (countBadge) {
            countBadge.textContent = performersData.length;
        }

        // Find the container (should be the next sibling or in the parent's next sibling)
        let container = headingElement.parentElement;
        let performanceItems = container.querySelectorAll(".performance-item");

        // Clear existing items
        performanceItems.forEach((item) => item.remove());

        // Add new items
        performersData.forEach((performer, index) => {
            const itemElement = this.createPerformanceItem(
                performer,
                sectionKey === "needs_attention" && index === 0
                    ? "bg-danger-transparent"
                    : bgClass,
                sectionKey === "needs_attention" && index === 0
                    ? "bg-danger"
                    : badgeClass,
                sectionKey === "needs_attention" && index === 0
                    ? "text-danger"
                    : textClass
            );
            container.appendChild(itemElement);
        });
    }

    // Create a performance item element
    createPerformanceItem(performer, bgClass, badgeClass, textClass) {
        const itemDiv = document.createElement("div");
        itemDiv.className = `performance-item line-clickable d-flex justify-content-between align-items-center mb-2 p-2 ${bgClass} rounded`;
        itemDiv.style.cursor = "pointer";
        itemDiv.dataset.line = performer.line;

        itemDiv.innerHTML = `
            <div class="d-flex align-items-center">
                <span class="badge ${badgeClass} me-2">${performer.line}</span>
                <div class="flex-fill">
                    <div class="fw-medium small">Line ${performer.line}</div>
                    <div class="row g-0 text-muted small">
                        <div class="col">Target: ${
                            performer.target_formatted || "0 PCS"
                        }</div>
                    </div>
                    <div class="row g-0 text-muted small">
                        <div class="col">Result: ${
                            performer.result_formatted || "0 PCS"
                        }</div>
                    </div>
                </div>
            </div>
            <div class="text-end">
                <div class="fw-medium ${textClass}">${
            performer.performance_percent
        }%</div>
                <small class="text-muted">Achievement</small>
            </div>
        `;

        return itemDiv;
    }

    // Update Area Performance section
    updateAreaPerformance(areaData) {
        console.log("Updating Area Performance with:", areaData);

        for (let area = 1; area <= 4; area++) {
            const areaPerf = areaData[area] || 0;
            const colorClass =
                areaPerf >= 100
                    ? "success"
                    : areaPerf >= 80
                    ? "warning"
                    : "danger";

            // Find the area performance element by data attribute
            const areaElement = document.querySelector(`[data-area="${area}"]`);
            if (areaElement) {
                const perfDiv = areaElement.querySelector(".fw-medium");

                if (perfDiv) {
                    perfDiv.textContent = `${areaPerf}%`;
                    perfDiv.className = `fw-medium text-${colorClass}`;
                    areaElement.className = `p-2 bg-${colorClass}-transparent rounded area-performance-item`;
                    areaElement.setAttribute("data-area", area);
                }
            }
        }
    }

    // Update Current Period Achievement panel (Left Panel)
    updatePreviousShiftAchievement(data) {
        console.log("Updating Current Period Achievement with:", data);

        // Update title and subtitle
        const titleElement = document.getElementById(
            "previous-achievement-title"
        );
        const subtitleElement = document.getElementById(
            "previous-achievement-subtitle"
        );
        const badgeElement = document.getElementById(
            "previous-achievement-badge"
        );

        if (titleElement)
            titleElement.textContent =
                data.title || "Current Period Achievement";
        if (subtitleElement)
            subtitleElement.textContent =
                data.subtitle || "Current period results";
        if (badgeElement) badgeElement.textContent = "Current Period";

        // Update circular progress
        const progressElement = document.getElementById(
            "previous-achievement-circle"
        );
        const percentElement = document.getElementById(
            "previous-achievement-percent"
        );
        const achievementCircle = document.getElementById("achievement-circle");

        if (progressElement) {
            progressElement.setAttribute(
                "data-percentage",
                data.achievement_percent || 0
            );
        }
        if (percentElement) {
            percentElement.textContent = `${parseFloat(
                data.achievement_percent || 0
            ).toFixed(1)}%`;
            percentElement.className = `mb-0 text-${
                data.status_class || "warning"
            }`;
        }
        if (achievementCircle) {
            const percent = parseFloat(data.achievement_percent || 0);
            const dashOffset = Math.round(314 - (percent / 100) * 314);
            achievementCircle.style.strokeDashoffset = dashOffset;
            achievementCircle.style.stroke = `var(--${
                data.status_class || "warning"
            }-color)`;
        }

        // Update status text and gap text
        const statusElement = document.getElementById("previous-status-text");
        const gapElement = document.getElementById("previous-gap-text");

        if (statusElement) {
            statusElement.textContent = data.status || "Below Target";
            statusElement.className = `fw-medium text-${
                data.status_class || "warning"
            }`;
        }
        if (gapElement)
            gapElement.textContent = data.gap_text || "No data available";

        // Update metrics
        const targetElement = document.getElementById("previous-target-pcs");
        const actualElement = document.getElementById("previous-actual-pcs");
        const hoursElement = document.getElementById("previous-total-hours");
        const linesElement = document.getElementById("previous-lines-active");

        if (targetElement) targetElement.textContent = data.target_pcs || "0M";
        if (actualElement) actualElement.textContent = data.actual_pcs || "0M";
        if (hoursElement) hoursElement.textContent = data.total_hours || "0h";
        if (linesElement)
            linesElement.textContent = `${data.lines_active?.active || 0}/${
                data.lines_active?.total || 11
            }`;

        // Update status summary
        const resultBadge = document.getElementById("previous-result-badge");
        const impactElement = document.getElementById("previous-impact-text");

        if (resultBadge) {
            resultBadge.textContent = data.status || "Below Target";
            resultBadge.className = `badge bg-${
                data.status_class || "warning"
            }`;
        }
        if (impactElement)
            impactElement.textContent =
                data.impact || "Need recovery in next period";
    }

    // Update Full Day Performance Monitor panel (Right Panel)
    updateCurrentPerformanceMonitor(data) {
        console.log("Updating Full Day Performance Monitor with:", data);

        // Update title and subtitle
        const titleElement = document.getElementById("current-monitor-title");
        const subtitleElement = document.getElementById("current-time-display");
        const badgeElement = document.getElementById("current-monitor-badge");

        if (titleElement)
            titleElement.textContent =
                data.title || "Full Day Progress Monitor";
        if (subtitleElement)
            subtitleElement.textContent =
                data.subtitle || "Full day analysis (00:00 ~ 23:59)";
        if (badgeElement) badgeElement.textContent = "Full Day";

        // Update progress section
        const progressTitleElement = document.getElementById(
            "current-progress-title"
        );
        const progressTextElement = document.getElementById(
            "current-progress-text"
        );
        const progressBarElement = document.getElementById(
            "current-progress-bar"
        );

        if (progressTitleElement)
            progressTitleElement.textContent =
                data.period_desc || "Period Progress";
        if (progressTextElement)
            progressTextElement.textContent = `${parseFloat(
                data.progress_percent || 0
            ).toFixed(1)}%`;
        if (progressBarElement)
            progressBarElement.style.width = `${data.progress_percent || 0}%`;

        // Update metrics grid
        const linesRunningElement = document.getElementById(
            "current-lines-running"
        );
        const avgEfficiencyElement = document.getElementById(
            "current-avg-efficiency"
        );
        const vsTargetElement = document.getElementById("current-vs-target");
        const timeRemainingElement = document.getElementById(
            "current-time-remaining"
        );

        if (linesRunningElement)
            linesRunningElement.textContent = data.lines_running || "0/11";
        if (avgEfficiencyElement)
            avgEfficiencyElement.textContent = data.avg_efficiency || "0%";
        if (vsTargetElement) {
            vsTargetElement.textContent = data.vs_target || "0%";
            vsTargetElement.className = `fw-medium text-${
                data.vs_target_class || "primary"
            }`;
        }
        if (timeRemainingElement)
            timeRemainingElement.textContent = data.time_remaining || "0h";

        // Update best and worst performance
        const bestLineElement = document.getElementById("current-best-line");
        const bestEfficiencyElement = document.getElementById(
            "current-best-efficiency"
        );
        const worstLineElement = document.getElementById("current-worst-line");
        const worstEfficiencyElement = document.getElementById(
            "current-worst-efficiency"
        );

        if (bestLineElement && data.best_line) {
            bestLineElement.textContent = `Best: Line ${
                data.best_line.line || "A"
            }`;
        }
        if (bestEfficiencyElement && data.best_line) {
            bestEfficiencyElement.textContent =
                data.best_line.efficiency || "0% efficiency";
        }
        if (worstLineElement && data.worst_line) {
            worstLineElement.textContent = `Alert: Line ${
                data.worst_line.line || "K"
            }`;
        }
        if (worstEfficiencyElement && data.worst_line) {
            worstEfficiencyElement.textContent =
                data.worst_line.efficiency || "0% efficiency";
        }

        // Update AI Analysis sections
        if (data.ai_recommendation) {
            this.updateAIRecommendation(data.ai_recommendation);
        }
        if (data.ai_alerts && data.ai_alerts.length > 0) {
            this.updateAIAlerts(data.ai_alerts);
        }
    }

    // Update AI Recommendation display
    updateAIRecommendation(recommendation) {
        console.log("Updating AI Recommendation with:", recommendation);

        const titleElement = document.getElementById("ai-recommendation-title");
        const messageElement = document.getElementById(
            "ai-recommendation-message"
        );
        const containerElement = document.getElementById(
            "ai-recommendation-container"
        );

        if (titleElement) {
            titleElement.textContent =
                recommendation.title || "AI Recommendation";
        }
        if (messageElement) {
            messageElement.textContent =
                recommendation.message || "No recommendation available";
        }

        // Update container styling based on recommendation type
        if (containerElement) {
            // Remove existing alert classes
            containerElement.className = containerElement.className.replace(
                /alert-\w+/g,
                ""
            );

            // Add appropriate alert class based on type
            const alertClass = this.getAlertClass(
                recommendation.type || "info"
            );
            containerElement.classList.add(alertClass);
        }
    }

    // Update AI Alerts display
    updateAIAlerts(alerts) {
        console.log("Updating AI Alerts with:", alerts);

        const alertsContainer = document.getElementById("ai-alerts-container");
        if (!alertsContainer) {
            console.warn("AI Alerts container not found");
            return;
        }

        // Clear existing alerts
        alertsContainer.innerHTML = "";

        // Add new alerts
        alerts.forEach((alert) => {
            const alertElement = this.createAlertElement(alert);
            alertsContainer.appendChild(alertElement);
        });

        // Show the alerts section if there are alerts
        const alertsSection = document.getElementById("ai-alerts-section");
        if (alertsSection) {
            alertsSection.style.display = alerts.length > 0 ? "block" : "none";
        }
    }

    // Create an individual alert element
    createAlertElement(alert) {
        const alertDiv = document.createElement("div");
        const alertClass = this.getAlertClass(alert.type || "info");
        alertDiv.className = `alert ${alertClass} alert-dismissible d-flex align-items-start mb-2 py-2`;

        // Get appropriate icon based on alert type
        const icon = this.getAlertIcon(alert.type || "info");

        alertDiv.innerHTML = `
            <i class="${icon} me-2 mt-1 flex-shrink-0"></i>
            <div class="flex-grow-1">
                <div class="fw-medium small mb-1">${
                    alert.title || "Alert"
                }</div>
                <div class="small text-muted">${
                    alert.message || "No details available"
                }</div>
            </div>
            <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        return alertDiv;
    }

    // Get Bootstrap alert class based on type
    getAlertClass(type) {
        const alertClasses = {
            success: "alert-success",
            warning: "alert-warning",
            danger: "alert-danger",
            info: "alert-info",
        };
        return alertClasses[type] || "alert-info";
    }

    // Get FontAwesome icon based on alert type
    getAlertIcon(type) {
        const iconClasses = {
            success: "fas fa-check-circle text-success",
            warning: "fas fa-exclamation-triangle text-warning",
            danger: "fas fa-exclamation-circle text-danger",
            info: "fas fa-info-circle text-info",
        };
        return iconClasses[type] || "fas fa-info-circle text-info";
    }

    // Update Per Line Summary table data
    updatePerLineTable(data) {
        const colors = [
            "primary",
            "success",
            "info",
            "warning",
            "secondary",
            "danger",
        ];

        // Find the Per Line Summary table
        const tableRows = document.querySelectorAll(
            ".col-xl-8 .card-body table tbody tr"
        );
        if (tableRows.length >= 5) {
            // Update Target row
            this.updateTableRow(tableRows[0], data.lines, data.target);

            // Update ENDTIME row
            this.updateTableRow(tableRows[1], data.lines, data.endtime);

            // Update SUBMTD row
            this.updateTableRow(tableRows[2], data.lines, data.submitted);

            // Update SUBMTD % row with badges
            this.updatePercentageRow(
                tableRows[3],
                data.lines,
                data.submitted_percent,
                colors,
                true
            );

            // Update ENDTIME % row with badges
            this.updatePercentageRow(
                tableRows[4],
                data.lines,
                data.endtime_percent,
                colors,
                false
            );
        }
    }

    // Update Per Size Summary table data
    updatePerSizeTable(data) {
        console.log("updatePerSizeTable called with data:", data);
        const colors = [
            "primary",
            "success",
            "info",
            "warning",
            "secondary",
            "danger",
        ];

        // Find the Per Size Summary table
        const tableRows = document.querySelectorAll(
            ".col-xl-4 .card-body table tbody tr"
        );
        console.log("Found table rows for Per Size Summary:", tableRows.length);

        if (tableRows.length >= 5) {
            console.log("Updating Per Size Summary table rows...");
            // Update Target row (row 0) - Per Size table has NO label column, just data cells
            console.log("Updating Target row with:", data.target);
            this.updatePerSizeTableRow(tableRows[0], data.sizes, data.target);

            // Update ENDTIME row (row 1)
            this.updatePerSizeTableRow(tableRows[1], data.sizes, data.endtime);

            // Update SUBMTD row (row 2)
            this.updatePerSizeTableRow(
                tableRows[2],
                data.sizes,
                data.submitted
            );

            // Update SUBMTD % row with badges (row 3)
            this.updatePerSizePercentageRow(
                tableRows[3],
                data.sizes,
                data.submitted_percent,
                colors,
                true
            );

            // Update ENDTIME % row with badges (row 4)
            this.updatePerSizePercentageRow(
                tableRows[4],
                data.sizes,
                data.endtime_percent,
                colors,
                false
            );
        }
    }

    // Helper method to update a simple table row (Target, ENDTIME, SUBMTD)
    updateTableRow(row, keys, values) {
        const cells = row.querySelectorAll("td:not(:first-child)");
        console.log("updateTableRow:", {
            rowExists: !!row,
            cellsFound: cells.length,
            keys: keys,
            values: values,
        });

        keys.forEach((key, index) => {
            if (cells[index]) {
                const newValue = values[key] || "0 M";
                console.log(
                    `Updating cell ${index} (${key}) from '${cells[index].textContent}' to '${newValue}'`
                );
                cells[index].textContent = newValue;
            }
        });
    }

    // Helper method to update percentage rows with badges
    updatePercentageRow(row, keys, percentages, colors, isTransparent) {
        const cells = row.querySelectorAll("td:not(:first-child)");
        keys.forEach((key, index) => {
            if (cells[index]) {
                const percent = percentages[key] || 0;
                const color = colors[index % colors.length];
                const transparency = isTransparent ? "-transparent" : "";
                cells[
                    index
                ].innerHTML = `<span class="badge bg-${color}${transparency}">${parseFloat(
                    percent
                ).toFixed(1)}%</span>`;
            }
        });
    }

    // Helper method for Per Size table (no label column - all cells are data)
    updatePerSizeTableRow(row, keys, values) {
        const cells = row.querySelectorAll("td"); // Get ALL cells, no :not(:first-child)
        console.log("updatePerSizeTableRow:", {
            rowExists: !!row,
            cellsFound: cells.length,
            keys: keys,
            values: values,
        });

        keys.forEach((key, index) => {
            if (cells[index]) {
                const newValue = values[key] || "0 M";
                console.log(
                    `Updating Per Size cell ${index} (${key}) from '${cells[index].textContent}' to '${newValue}'`
                );
                cells[index].textContent = newValue;
            }
        });
    }

    // Helper method for Per Size percentage rows (no label column)
    updatePerSizePercentageRow(row, keys, percentages, colors, isTransparent) {
        const cells = row.querySelectorAll("td"); // Get ALL cells, no :not(:first-child)
        keys.forEach((key, index) => {
            if (cells[index]) {
                const percent = percentages[key] || 0;
                const color = colors[index % colors.length];
                const transparency = isTransparent ? "-transparent" : "";
                cells[
                    index
                ].innerHTML = `<span class="badge bg-${color}${transparency}">${parseFloat(
                    percent
                ).toFixed(1)}%</span>`;
            }
        });
    }

    // Update equipment status display
    updateEquipmentStatus(stats) {
        // Update equipment counts
        const totalEquipmentEl = document.getElementById("totalEquipment");
        const equipmentWithOngoingEl = document.getElementById(
            "equipmentWithOngoing"
        );
        const idleEquipmentEl = document.getElementById("idleEquipment");

        if (totalEquipmentEl) {
            totalEquipmentEl.textContent = stats.total_equipment || 0;
        }
        if (equipmentWithOngoingEl) {
            equipmentWithOngoingEl.textContent =
                stats.equipment_with_ongoing || 0;
        }
        if (idleEquipmentEl) {
            const idleCount =
                (stats.total_equipment || 0) -
                (stats.equipment_with_ongoing || 0);
            idleEquipmentEl.textContent = idleCount;
        }

        // Update progress bars
        const ongoingProgressBar = document.querySelector(
            ".equipment-ongoing-progress"
        );
        const idleProgressBar = document.querySelector(
            ".equipment-idle-progress"
        );
        const totalEquipment = stats.total_equipment || 1;

        if (ongoingProgressBar) {
            const ongoingPercentage =
                ((stats.equipment_with_ongoing || 0) / totalEquipment) * 100;
            ongoingProgressBar.style.width = ongoingPercentage + "%";
            // Ensure the green color is applied
            ongoingProgressBar.style.backgroundColor = "rgba(50, 212, 132, 1)";
        }

        if (idleProgressBar) {
            const idleCount =
                (stats.total_equipment || 0) -
                (stats.equipment_with_ongoing || 0);
            const idlePercentage = (idleCount / totalEquipment) * 100;
            idleProgressBar.style.width = idlePercentage + "%";
        }

        // Update the radial chart
        if (
            window.equipmentStatusChart &&
            stats.equipment_status_percentage !== undefined
        ) {
            window.equipmentStatusChart.updateSeries([
                stats.equipment_status_percentage,
            ]);
        }
    }

    // Update the ApexCharts Production Overview chart with new data
    async updateChart() {
        if (window.manufacturingChart) {
            try {
                // Fetch new chart data
                const params = new URLSearchParams({
                    dashboard_date:
                        document.getElementById("dashboard_date")?.value ||
                        new Date().toISOString().split("T")[0],
                    dashboard_shift:
                        document.getElementById("dashboard_shift")?.value ||
                        "all",
                    dashboard_cutoff:
                        document.getElementById("dashboard_cutoff")?.value ||
                        "all",
                    dashboard_work_type:
                        document.getElementById("dashboard_work_type")?.value ||
                        "all",
                });

                const response = await fetch(
                    `/api/manufacturing-overview?${params.toString()}`
                );
                const result = await response.json();

                if (result.success) {
                    // Update the chart with new series data
                    window.manufacturingChart.updateSeries(result.data.series);
                } else {
                    console.error(
                        "Failed to update chart data:",
                        result.message
                    );
                }
            } catch (error) {
                console.error("Error updating chart:", error);
            }
        }
    }
}

// Initialize Production Dashboard when DOM is loaded
document.addEventListener("DOMContentLoaded", () => {
    new ProductionDashboard();
});

// Make Production Dashboard globally available
window.ProductionDashboard = ProductionDashboard;

// ==================== GLOBAL DASHBOARD FUNCTIONS ====================

/**
 * View All Alerts function
 * Opens a modal or navigates to alerts page
 */
window.viewAllAlerts = function () {
    console.log("View All Alerts clicked");

    // For now, show a modal with alert details
    // In a real application, this would redirect to an alerts management page
    Swal.fire({
        title: "Production Alerts",
        html: `
            <div class="text-start">
                <div class="alert alert-danger d-flex align-items-start mb-2">
                    <i class="fas fa-exclamation-circle text-danger me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Line D Performance Drop</div>
                        <small class="text-muted">Efficiency below 80% - 5 minutes ago</small>
                        <div class="text-muted small mt-1">Current efficiency: 76.2%</div>
                    </div>
                </div>
                
                <div class="alert alert-warning d-flex align-items-start mb-2">
                    <i class="fas fa-clock text-warning me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Shift Target Behind Schedule</div>
                        <small class="text-muted">15% behind target - 12 minutes ago</small>
                        <div class="text-muted small mt-1">Current progress: 85%</div>
                    </div>
                </div>
                
                <div class="alert alert-info d-flex align-items-start mb-2">
                    <i class="fas fa-lightbulb text-info me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Optimization Suggestion</div>
                        <small class="text-muted">Line F can increase speed - 18 minutes ago</small>
                        <div class="text-muted small mt-1">Suggested speed increase: 5%</div>
                    </div>
                </div>
                
                <div class="alert alert-warning d-flex align-items-start mb-2">
                    <i class="fas fa-exclamation-triangle text-warning me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Equipment Maintenance Due</div>
                        <small class="text-muted">Line H maintenance overdue - 1 hour ago</small>
                        <div class="text-muted small mt-1">Last maintenance: 7 days ago</div>
                    </div>
                </div>
                
                <div class="alert alert-success d-flex align-items-start mb-0">
                    <i class="fas fa-check-circle text-success me-2 mt-1"></i>
                    <div class="flex-grow-1">
                        <div class="fw-medium small">Target Achievement</div>
                        <small class="text-muted">Line B exceeded target - 2 hours ago</small>
                        <div class="text-muted small mt-1">Achievement: 104.8%</div>
                    </div>
                </div>
            </div>
        `,
        icon: null,
        confirmButtonText: "Close",
        confirmButtonColor: "#6366f1",
        customClass: {
            popup: "swal-wide",
        },
        showCloseButton: true,
        width: 600,
    });
};

// ==================== PRODUCTION INSIGHTS FUNCTIONS ====================

/**
 * Production Insights Manager
 * Handles the 3rd row data: Previous Shift, Line Performance, and Live Analysis
 */
class ProductionInsights {
    constructor() {
        this.updateInterval = null;
        this.init();
    }

    init() {
        this.updateCurrentTimeDisplay();
        this.fetchPreviousShiftData();
        this.fetchLinePerformanceData();
        this.startLiveUpdates();

        // Update time display every minute
        setInterval(() => {
            this.updateCurrentTimeDisplay();
        }, 60000);
    }

    updateCurrentTimeDisplay() {
        const now = new Date();
        const manilaTime = new Date(
            now.toLocaleString("en-US", { timeZone: "Asia/Manila" })
        );

        const dateStr = manilaTime.toLocaleDateString("en-US", {
            year: "numeric",
            month: "short",
            day: "numeric",
        });

        const timeStr = manilaTime.toLocaleTimeString("en-US", {
            hour: "2-digit",
            minute: "2-digit",
            hour12: false,
        });

        const hours = manilaTime.getHours();
        const shiftType =
            hours >= 7 && hours < 19 ? "Day Shift" : "Night Shift";

        const displayElement = document.getElementById("current-time-display");
        if (displayElement) {
            displayElement.textContent = `${dateStr} ${timeStr} - ${shiftType} Progress`;
        }

        const timeMarker = document.getElementById("current-time-marker");
        if (timeMarker) {
            timeMarker.textContent = timeStr;
        }

        // Update progress based on time
        this.updateShiftProgress(hours);
    }

    updateShiftProgress(currentHour) {
        let progressPercent = 0;

        if (currentHour >= 7 && currentHour < 19) {
            // Day shift: 07:00 - 19:00 (12 hours)
            const shiftMinutes =
                (currentHour - 7) * 60 + new Date().getMinutes();
            progressPercent = Math.min((shiftMinutes / (12 * 60)) * 100, 100);
        } else {
            // Night shift: 19:00 - 07:00 (12 hours)
            let shiftMinutes;
            if (currentHour >= 19) {
                shiftMinutes =
                    (currentHour - 19) * 60 + new Date().getMinutes();
            } else {
                shiftMinutes = (currentHour + 5) * 60 + new Date().getMinutes(); // +5 because 24-19=5
            }
            progressPercent = Math.min((shiftMinutes / (12 * 60)) * 100, 100);
        }

        const progressBar = document.getElementById("current-progress-bar");
        const progressText = document.getElementById("current-progress-text");

        if (progressBar) {
            progressBar.style.width = `${progressPercent}%`;
        }

        if (progressText) {
            progressText.textContent = `${progressPercent.toFixed(1)}%`;
        }
    }

    async fetchPreviousShiftData() {
        try {
            // Simulate API call for previous night shift data
            const previousShiftData = {
                achievement_percent: 87.1,
                target_pcs: "271.7M",
                actual_pcs: "236.4M",
                total_hours: "11.98h",
                lines_active: "9/11",
                status: "Below Target",
                gap_percent: 12.9,
            };

            this.updatePreviousShiftUI(previousShiftData);
        } catch (error) {
            console.error("Error fetching previous shift data:", error);
        }
    }

    updatePreviousShiftUI(data) {
        // Update achievement percentage and circle
        const achievementPercent = document.getElementById(
            "night-achievement-percent"
        );
        const achievementCircle = document.getElementById("achievement-circle");

        if (achievementPercent) {
            achievementPercent.textContent = `${data.achievement_percent}%`;
        }

        if (achievementCircle) {
            const dashOffset = 314 - (314 * data.achievement_percent) / 100;
            achievementCircle.style.strokeDashoffset = dashOffset;
        }

        // Update metrics
        this.updateElementText("night-target-pcs", data.target_pcs);
        this.updateElementText("night-actual-pcs", data.actual_pcs);
        this.updateElementText("night-total-hours", data.total_hours);
        this.updateElementText("night-lines-active", data.lines_active);
        this.updateElementText("night-status-text", data.status);
        this.updateElementText(
            "night-gap-text",
            `${data.gap_percent}% below target capacity`
        );
    }

    async fetchLinePerformanceData() {
        try {
            // Simulate API call for line performance data
            const lineData = {
                top_performers: [
                    {
                        line: "A",
                        area: "Area 1",
                        percentage: 105.2,
                        pcs: "28.5M",
                    },
                    {
                        line: "H",
                        area: "Area 2",
                        percentage: 102.8,
                        pcs: "31.2M",
                    },
                ],
                average_performers: [
                    {
                        line: "B",
                        area: "Area 1",
                        percentage: 94.6,
                        pcs: "19.8M",
                    },
                    {
                        line: "C",
                        area: "Area 1",
                        percentage: 91.3,
                        pcs: "21.4M",
                    },
                ],
                underperformers: [
                    {
                        line: "D",
                        area: "Area 2",
                        percentage: 78.4,
                        pcs: "15.7M",
                    },
                    {
                        line: "G",
                        area: "Area 3",
                        percentage: 72.1,
                        pcs: "13.8M",
                    },
                ],
                area_performance: {
                    "area-1-perf": 98.5,
                    "area-2-perf": 89.2,
                    "area-3-perf": 75.8,
                },
            };

            this.updateLinePerformanceUI(lineData);
        } catch (error) {
            console.error("Error fetching line performance data:", error);
        }
    }

    updateLinePerformanceUI(data) {
        // Update area performance
        Object.entries(data.area_performance).forEach(([elementId, value]) => {
            this.updateElementText(elementId, `${value}%`);
        });

        // Update performer counts
        this.updateElementText(
            "top-performers-count",
            data.top_performers.length
        );
        this.updateElementText(
            "avg-performers-count",
            data.average_performers.length
        );
        this.updateElementText(
            "underperform-count",
            data.underperformers.length
        );
    }

    startLiveUpdates() {
        // Update live metrics every 30 seconds
        this.updateInterval = setInterval(() => {
            this.updateLiveMetrics();
        }, 30000);

        // Initial update
        this.updateLiveMetrics();
    }

    async updateLiveMetrics() {
        try {
            // Simulate live data fetch
            const liveData = {
                lines_running: "10/11",
                avg_efficiency: "94.2%",
                vs_target: "****%",
                time_remaining: "10.17h",
                best_line: "Best: Line E",
                best_efficiency: "108.5% efficiency",
                worst_line: "Alert: Line J",
                worst_efficiency: "68.2% efficiency",
            };

            // Update live metrics
            this.updateElementText(
                "current-lines-running",
                liveData.lines_running
            );
            this.updateElementText(
                "current-avg-efficiency",
                liveData.avg_efficiency
            );
            this.updateElementText("current-vs-target", liveData.vs_target);
            this.updateElementText(
                "current-time-remaining",
                liveData.time_remaining
            );
            this.updateElementText("current-best-line", liveData.best_line);
            this.updateElementText(
                "current-best-efficiency",
                liveData.best_efficiency
            );
            this.updateElementText("current-worst-line", liveData.worst_line);
            this.updateElementText(
                "current-worst-efficiency",
                liveData.worst_efficiency
            );
        } catch (error) {
            console.error("Error updating live metrics:", error);
        }
    }

    updateElementText(elementId, text) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = text;
        }
    }

    destroy() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
        }
    }
}

// Global functions for button handlers
window.showDetailedReport = function () {
    console.log("Detailed report requested");
    Swal.fire({
        title: "Production Detailed Report",
        text: "This would open a detailed production report with charts and analytics.",
        icon: "info",
        confirmButtonText: "Close",
        confirmButtonColor: "#6366f1",
    });
};

window.exportReport = function () {
    console.log("Export report requested");
    Swal.fire({
        title: "Export Report",
        text: "Report export initiated. You will receive an email when ready.",
        icon: "success",
        timer: 3000,
        showConfirmButton: false,
    });
};

window.setTrendView = function (viewType) {
    console.log("Trend view changed to:", viewType);
    // Update the dropdown button text and active state
    const dropdownItems = document.querySelectorAll(
        ".dropdown-menu .dropdown-item"
    );
    dropdownItems.forEach((item) => {
        item.classList.remove("active");
        if (item.textContent.toLowerCase().includes(viewType)) {
            item.classList.add("active");
        }
    });
};

// Initialize Production Insights when DOM is ready
document.addEventListener("DOMContentLoaded", () => {
    if (document.getElementById("production-insights-row")) {
        window.productionInsights = new ProductionInsights();
    }
});

// Clean up on page unload
window.addEventListener("beforeunload", () => {
    if (window.productionInsights) {
        window.productionInsights.destroy();
    }
});
