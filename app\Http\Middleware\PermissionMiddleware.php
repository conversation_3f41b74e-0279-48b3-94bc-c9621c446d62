<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @param  string  $permission
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next, string $permission)
    {
        if (!$request->user()) {
            return redirect()->route('login');
        }

        $user = $request->user();
        
        // Parse permission format: "route:action" or just "route"
        $parts = explode(':', $permission);
        $route = $parts[0];
        $action = $parts[1] ?? 'view';

        // Check if user has permission based on role and action
        if (!$this->hasPermission($user, $route, $action)) {
            // Add debugging information
            $debugInfo = [
                'user_role' => $user->role,
                'user_id' => $user->id,
                'requested_route' => $route,
                'requested_action' => $action,
                'full_permission' => $permission,
                'request_url' => $request->url(),
                'request_method' => $request->method()
            ];
            
            \Log::warning('Permission denied for user', $debugInfo);
            
            abort(403, 'Access denied. You do not have permission to perform this action. Debug: ' . json_encode($debugInfo));
        }

        return $next($request);
    }

    /**
     * Check if user has permission for a specific route and action
     */
    private function hasPermission($user, string $route, string $action): bool
    {
        $role = $user->role;

        // Define permissions matrix
        $permissions = [
            'USER' => [
                'dashboard' => ['view'],
                'endtime' => ['view', 'create', 'update'],
                'requests-rpt' => ['view'],
                'lot-requests' => ['view', 'create', 'update'],
                'lot-requests/create' => ['view', 'create', 'update'],
                'process-wip-rpt' => ['view'],
                'updatewip' => ['view'],
                'updatewip/create' => ['view', 'create', 'update'],
                'endline-rpt' => ['view'],
                'endline-wip' => ['view', 'create', 'update'],
                'profile' => ['view', 'update'],
            ],
            'MANAGER' => [
                'dashboard' => ['view'],
                'endtime' => ['view', 'create', 'update', 'delete'],
                'requests-rpt' => ['view'],
                'lot-requests' => ['view', 'create', 'update', 'delete'],
                'lot-requests/create' => ['view', 'create', 'update', 'delete'],
                'process-wip-rpt' => ['view'],
                'updatewip' => ['view'],
                'updatewip/create' => ['view', 'create', 'update'],
                'mc-alloc-rpt' => ['view'],
                'equipment' => ['view', 'create', 'update', 'delete'],
                'endline-rpt' => ['view'],
                'endline-wip' => ['view', 'create', 'update', 'delete'],
                'profile' => ['view', 'update'],
            ],
            'ADMIN' => [
                'dashboard' => ['view', 'create', 'update', 'delete'],
                'endtime' => ['view', 'create', 'update', 'delete'],
                'requests-rpt' => ['view', 'create', 'update', 'delete'],
                'lot-requests' => ['view', 'create', 'update', 'delete'],
                'lot-requests/create' => ['view', 'create', 'update', 'delete'],
                'process-wip-rpt' => ['view', 'create', 'update', 'delete'],
                'updatewip' => ['view', 'create', 'update', 'delete'],
                'updatewip/create' => ['view', 'create', 'update', 'delete'],
                'mc-alloc-rpt' => ['view', 'create', 'update', 'delete'],
                'equipment' => ['view', 'create', 'update', 'delete'],
                'endline-rpt' => ['view', 'create', 'update', 'delete'],
                'endline-wip' => ['view', 'create', 'update', 'delete'],
                'management/settings' => ['view', 'create', 'update', 'delete'],
                'management/data' => ['view', 'create', 'update', 'delete'],
                'management/users' => ['view', 'create', 'update', 'delete'],
                'profile' => ['view', 'update'],
            ],
        ];

        // Check if role exists
        if (!isset($permissions[$role])) {
            return false;
        }

        // Check if route permission exists for this role
        if (!isset($permissions[$role][$route])) {
            return false;
        }

        // Check if specific action is allowed
        return in_array($action, $permissions[$role][$route]);
    }
}
