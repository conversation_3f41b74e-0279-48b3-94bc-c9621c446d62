<x-app-layout>
    <x-slot name="header">
        System Settings
    </x-slot>

    <!-- Settings Navigation -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <h5 class="mb-3">Settings Categories</h5>
                    <div class="row g-3">
                        <div class="col-md-3">
                            <a href="{{ route('management.settings.general') }}" class="btn btn-outline-primary w-100 h-100">
                                <i class="fas fa-cog fa-2x mb-2 d-block"></i>
                                <strong>General Settings</strong>
                                <small class="d-block text-muted">App name, timezone, currency</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.settings.email') }}" class="btn btn-outline-success w-100 h-100">
                                <i class="fas fa-envelope fa-2x mb-2 d-block"></i>
                                <strong>Email Settings</strong>
                                <small class="d-block text-muted">SMTP configuration</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.settings.system') }}" class="btn btn-outline-info w-100 h-100">
                                <i class="fas fa-server fa-2x mb-2 d-block"></i>
                                <strong>System Settings</strong>
                                <small class="d-block text-muted">Performance & maintenance</small>
                            </a>
                        </div>
                        <div class="col-md-3">
                            <a href="{{ route('management.settings.security') }}" class="btn btn-outline-warning w-100 h-100">
                                <i class="fas fa-shield-alt fa-2x mb-2 d-block"></i>
                                <strong>Security Settings</strong>
                                <small class="d-block text-muted">Passwords & authentication</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Settings Overview -->
    <div class="row g-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Current Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <h6 class="text-muted">Application</h6>
                            <ul class="list-unstyled">
                                <li><strong>Name:</strong> {{ $settings['app_name'] }}</li>
                                <li><strong>Timezone:</strong> {{ $settings['timezone'] }}</li>
                                <li><strong>Currency:</strong> {{ $settings['currency'] }}</li>
                                <li><strong>Items per page:</strong> {{ $settings['items_per_page'] }}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-muted">System</h6>
                            <ul class="list-unstyled">
                                <li><strong>Debug Mode:</strong> 
                                    <span class="badge {{ $settings['debug_mode'] ? 'bg-warning' : 'bg-success' }}">
                                        {{ $settings['debug_mode'] ? 'Enabled' : 'Disabled' }}
                                    </span>
                                </li>
                                <li><strong>Cache:</strong> 
                                    <span class="badge {{ $settings['cache_enabled'] ? 'bg-success' : 'bg-warning' }}">
                                        {{ $settings['cache_enabled'] ? 'Enabled' : 'Disabled' }}
                                    </span>
                                </li>
                                <li><strong>Session Lifetime:</strong> {{ $settings['session_lifetime'] }} minutes</li>
                                <li><strong>Max Upload:</strong> {{ $settings['max_upload_size'] }} MB</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Quick Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <form action="{{ route('management.settings.clear-cache') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-warning w-100">
                                <i class="fas fa-broom me-2"></i>Clear Cache
                            </button>
                        </form>
                        
                        <form action="{{ route('management.settings.optimize') }}" method="POST" class="d-inline">
                            @csrf
                            <button type="submit" class="btn btn-outline-success w-100">
                                <i class="fas fa-tachometer-alt me-2"></i>Optimize App
                            </button>
                        </form>
                        
                        <a href="{{ route('management.settings.download-logs') }}" class="btn btn-outline-info">
                            <i class="fas fa-download me-2"></i>Download Logs
                        </a>
                        
                        <form action="{{ route('management.settings.clear-logs') }}" 
                              method="POST" 
                              class="d-inline"
                              onsubmit="return confirm('Are you sure you want to clear all logs?')">
                            @csrf
                            <button type="submit" class="btn btn-outline-danger w-100">
                                <i class="fas fa-trash me-2"></i>Clear Logs
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Security & Performance Info -->
    <div class="row g-4 mt-2">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Security Overview</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-6 text-center">
                            <div class="security-metric">
                                <i class="fas fa-lock fa-2x {{ $settings['password_min_length'] >= 8 ? 'text-success' : 'text-warning' }} mb-2"></i>
                                <h6>Password Security</h6>
                                <small class="text-muted">Min {{ $settings['password_min_length'] }} characters</small>
                            </div>
                        </div>
                        <div class="col-6 text-center">
                            <div class="security-metric">
                                <i class="fas fa-user-shield fa-2x {{ $settings['two_factor_enabled'] ? 'text-success' : 'text-muted' }} mb-2"></i>
                                <h6>Two-Factor Auth</h6>
                                <small class="text-muted">{{ $settings['two_factor_enabled'] ? 'Enabled' : 'Disabled' }}</small>
                            </div>
                        </div>
                        <div class="col-6 text-center">
                            <div class="security-metric">
                                <i class="fas fa-ban fa-2x text-info mb-2"></i>
                                <h6>Login Attempts</h6>
                                <small class="text-muted">Max {{ $settings['login_attempts_limit'] }} tries</small>
                            </div>
                        </div>
                        <div class="col-6 text-center">
                            <div class="security-metric">
                                <i class="fas fa-clock fa-2x text-warning mb-2"></i>
                                <h6>Lockout Duration</h6>
                                <small class="text-muted">{{ $settings['lockout_duration'] }} minutes</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Email Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="email-config">
                        <div class="mb-3">
                            <label class="form-label text-muted">From Address</label>
                            <p class="mb-0">{{ $settings['mail_from_address'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">From Name</label>
                            <p class="mb-0">{{ $settings['mail_from_name'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">SMTP Host</label>
                            <p class="mb-0">{{ $settings['smtp_host'] ?: 'Not configured' }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted">SMTP Port</label>
                            <p class="mb-0">{{ $settings['smtp_port'] ?: 'Not configured' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Tips -->
    <div class="row g-4 mt-2">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb text-warning me-2"></i>Settings Tips
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <div class="tip-item">
                                <i class="fas fa-shield-alt text-success mb-2"></i>
                                <h6>Security Best Practices</h6>
                                <p class="text-muted small">Enable strong password requirements and two-factor authentication for better security.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="tip-item">
                                <i class="fas fa-tachometer-alt text-primary mb-2"></i>
                                <h6>Performance Optimization</h6>
                                <p class="text-muted small">Enable caching and regularly optimize your application for better performance.</p>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="tip-item">
                                <i class="fas fa-envelope text-info mb-2"></i>
                                <h6>Email Configuration</h6>
                                <p class="text-muted small">Configure SMTP settings to enable email notifications and user communications.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <style>
        .security-metric {
            padding: 1rem;
        }
        
        .tip-item {
            text-align: center;
            padding: 1rem;
        }
        
        .tip-item i {
            font-size: 2rem;
            display: block;
        }
    </style>
</x-app-layout>