<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use App\Models\Equipment;

class EquipmentDailyCapacitySeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 
     * Calculate and populate daily_capa for all equipment records.
     * Logic: daily_capa = (eqp_oee / 100) * eqp_speed * operation_time
     */
    public function run(): void
    {
        $this->command->info('Calculating daily capacity for all equipment records...');
        
        // Get all equipment records that need daily_capa calculation
        $equipment = Equipment::whereNotNull('eqp_oee')
            ->whereNotNull('eqp_speed')
            ->whereNotNull('operation_time')
            ->get();
        
        $updatedCount = 0;
        $skippedCount = 0;
        
        foreach ($equipment as $eqp) {
            try {
                // Calculate daily capacity: eqp_oee * eqp_speed * operation_time
                // Note: eqp_oee is already stored as decimal (0.75 = 75%)
                $oee = floatval($eqp->eqp_oee); // Use as decimal directly
                $speed = floatval(str_replace(',', '', $eqp->eqp_speed)); // Remove commas
                $operationTime = floatval(str_replace(',', '', $eqp->operation_time)); // Remove commas
                
                $dailyCapacity = round($oee * $speed * $operationTime);
                
                // Update the equipment record
                $eqp->daily_capa = $dailyCapacity;
                $eqp->save();
                
                $updatedCount++;
                
                $this->command->line(sprintf(
                    'Updated %s: OEE=%.2f%%, Speed=%s, OpTime=%s => Daily Capa=%s',
                    $eqp->eqp_no,
                    $eqp->eqp_oee,
                    number_format($speed),
                    number_format($operationTime),
                    number_format($dailyCapacity)
                ));
                
            } catch (\Exception $e) {
                $skippedCount++;
                $this->command->error(sprintf(
                    'Failed to calculate daily_capa for %s: %s',
                    $eqp->eqp_no,
                    $e->getMessage()
                ));
            }
        }
        
        // Handle equipment with missing data
        $incompleteEquipment = Equipment::where(function($query) {
            $query->whereNull('eqp_oee')
                ->orWhereNull('eqp_speed')
                ->orWhereNull('operation_time');
        })->get();
        
        foreach ($incompleteEquipment as $eqp) {
            $eqp->daily_capa = null; // Set to null for incomplete data
            $eqp->save();
            $skippedCount++;
            
            $this->command->warn(sprintf(
                'Skipped %s: Missing data (OEE=%s, Speed=%s, OpTime=%s)',
                $eqp->eqp_no,
                $eqp->eqp_oee ?? 'NULL',
                $eqp->eqp_speed ?? 'NULL',
                $eqp->operation_time ?? 'NULL'
            ));
        }
        
        $this->command->info('\n=== DAILY CAPACITY SEEDING COMPLETED ===');
        $this->command->info(sprintf('Updated: %d equipment records', $updatedCount));
        $this->command->info(sprintf('Skipped: %d equipment records (missing data)', $skippedCount));
        $this->command->info(sprintf('Total: %d equipment records processed', $updatedCount + $skippedCount));
    }
}
