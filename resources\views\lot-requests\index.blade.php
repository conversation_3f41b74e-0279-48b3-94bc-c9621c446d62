<x-app-layout>
    <x-slot name="header">
        Lot Requests
    </x-slot>

    <style>
        /* Clean page background */
        .lot-requests-page {
            background: #f8f9ff;
            min-height: 100vh;
        }
        
        /* Simple request card */
        .request-card {
            background: white;
            border: 1px solid #e1e8ff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            transition: all 0.2s ease;
        }
        
        .request-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
        }
        
        /* Simple primary button */
        .btn-primary {
            background: #667eea;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        /* Simple fade-in effect */
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .action-buttons-group {
                flex-direction: row;
                justify-content: center;
            }
            
            .action-btn {
                width: 28px;
                height: 28px;
                font-size: 0.75rem;
            }
        }
        
        /* Clean table styling */
        .table-responsive {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            background: white;
        }
        
        .table thead th {
            background: #667eea;
            color: white;
            font-weight: 600;
            padding: 1rem;
            border: none;
        }
        
        .table tbody tr {
            transition: background-color 0.2s ease;
            border: none;
        }
        
        .table tbody tr:hover {
            background: #f8f9ff;
        }
        
        .table tbody td {
            padding: 1rem;
            border-top: 1px solid #e1e8ff;
            vertical-align: middle;
        }
        
        /* Simple badges */
        .badge {
            font-weight: 500;
            padding: 0.4rem 0.7rem;
            border-radius: 6px;
        }
        
        /* Action buttons styling */
        .action-buttons-group {
            gap: 0.4rem !important;
        }
        
        
        .action-btn {
            width: 32px;
            height: 32px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            border: none;
            font-size: 0.85rem;
        }
        
        .action-btn:hover {
            transform: translateY(-1px);
        }
        
        .view-btn {
            background: #17a2b8;
            color: white;
        }
        
        .view-btn:hover {
            background: #138496;
            color: white;
        }
        
        .edit-btn {
            background: #ffc107;
            color: #000;
        }
        
        .edit-btn:hover {
            background: #e0a800;
            color: #000;
        }
        
        .delete-btn {
            background: #dc3545;
            color: white;
        }
        
        .delete-btn:hover {
            background: #c82333;
            color: white;
        }
        
    </style>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Lot Requests</h4>
                    <p class="text-muted mb-0">Manage your lot requests</p>
                </div>
                <div class="d-flex gap-2 align-items-center">
                    <!-- Search Input -->
                    <div class="position-relative">
                        <input type="text" 
                               class="form-control search-input" 
                               placeholder="Search requests..."
                               id="searchInput"
                               style="width: 250px; padding-left: 2.5rem;">
                        <i class="fas fa-search position-absolute" 
                           style="left: 0.75rem; top: 50%; transform: translateY(-50%); color: #6c757d;"></i>
                    </div>
                    
                    <!-- Filter Dropdown -->
                    <div class="dropdown position-relative">
                        <button class="btn btn-outline-secondary dropdown-toggle" 
                                type="button" 
                                id="filterDropdown">
                            <i class="fas fa-filter me-2"></i>Filter
                        </button>
                        <ul class="dropdown-menu" id="filterMenu" style="display: none; position: absolute; top: 100%; left: 0; z-index: 1000; background: white; border: 1px solid #ddd; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1); min-width: 180px; margin-top: 0.5rem;">
                            <li><h6 class="dropdown-header" style="padding: 0.5rem 1rem; margin: 0; font-size: 0.875rem; color: #6c757d; border-bottom: 1px solid #eee;">Filter by Status</h6></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="all" style="padding: 0.5rem 1rem; text-decoration: none; color: #495057; display: block;">All Requests</a></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="pending" style="padding: 0.5rem 1rem; text-decoration: none; color: #495057; display: block;">Pending</a></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="getting lot" style="padding: 0.5rem 1rem; text-decoration: none; color: #495057; display: block;">Getting Lot</a></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="completed" style="padding: 0.5rem 1rem; text-decoration: none; color: #495057; display: block;">Completed</a></li>
                            <li><a class="dropdown-item filter-option" href="#" data-filter="cancelled" style="padding: 0.5rem 1rem; text-decoration: none; color: #495057; display: block;">Cancelled</a></li>
                        </ul>
                    </div>
                    
                    <a href="{{ route('lot-requests.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Create Lot Request
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="request-card fade-in">
                <div class="card-body p-4">
                    @if($lotRequests->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>Request #</th>
                                        <th>Requestor</th>
                                        <th>Area Station</th>
                                        <th>MC Count</th>
                                        <th>EQP Codes</th>
                                        <th>Total Lots</th>
                                        <th>Status</th>
                                        <th>Request Date</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($lotRequests as $lotRequest)
                                        <tr>
                                            <td>
                                                <a href="{{ route('lot-requests.show', $lotRequest) }}" 
                                                   class="text-decoration-none fw-medium">
                                                    {{ $lotRequest->request_number }}
                                                </a>
                                            </td>
                                            <td>
                                                <div>
                                                    <div class="fw-medium">{{ $lotRequest->user->emp_name }}</div>
                                                    <small class="text-muted">{{ $lotRequest->user->emp_no }}</small>
                                                </div>
                                            </td>
                                            <td>
                                                @if($lotRequest->area_stations)
                                                    <div class="d-flex flex-wrap gap-1">
                                                        @foreach(explode(', ', $lotRequest->area_stations) as $area)
                                                            <span class="badge bg-secondary">{{ $area }}</span>
                                                        @endforeach
                                                    </div>
                                                @else
                                                    <small class="text-muted">No area</small>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ $lotRequest->lotRequestItems->count() }} machines</span>
                                            </td>
                                            <td>
                                                <div class="d-flex flex-wrap gap-1">
                                                    @foreach($lotRequest->lotRequestItems->pluck('equipment_code')->unique()->filter() as $code)
                                                        <span class="badge bg-info text-dark">{{ $code }}</span>
                                                    @endforeach
                                                    @if($lotRequest->lotRequestItems->pluck('equipment_code')->unique()->filter()->isEmpty())
                                                        <small class="text-muted">No codes</small>
                                                    @endif
                                                </div>
                                            </td>
                                            <td>
                                                <span class="fw-bold text-primary">{{ $lotRequest->total_quantity }} lots</span>
                                            </td>
                                            <td>
                                                <span class="badge {{ $lotRequest->getStatusBadgeClass() }}">
                                                    {{ $lotRequest->formatted_status }}
                                                </span>
                                            </td>
                                            <td>
                                                <div>{{ $lotRequest->request_date->format('M d, Y g:iA') }}</div>
                                                @php
                                                    $responseTime = null;
                                                    $durationText = '';
                                                    $badgeClass = 'bg-secondary';
                                                    
                                                    if ($lotRequest->status === 'completed' && $lotRequest->completed_at) {
                                                        // Show elapsed time from request to completion (STOPPED at completion)
                                                        $responseTime = $lotRequest->completed_at;
                                                        $durationText = 'completed';
                                                        $badgeClass = 'bg-success';
                                                    } elseif ($lotRequest->status === 'cancelled' && $lotRequest->responded_at) {
                                                        // Show elapsed time from request to cancellation (STOPPED at cancellation)
                                                        $responseTime = $lotRequest->responded_at;
                                                        $durationText = 'cancelled';
                                                        $badgeClass = 'bg-secondary';
                                                    } elseif ($lotRequest->status === 'in_process') {
                                                        // Show current elapsed time (LIVE timer until completion)
                                                        $responseTime = now();
                                                        $durationText = 'elapsed';
                                                        $badgeClass = 'bg-info';
                                                    } elseif ($lotRequest->status === 'pending') {
                                                        // Show current elapsed time (LIVE timer until completion)
                                                        $responseTime = now();
                                                        $durationText = 'elapsed';
                                                        $badgeClass = 'bg-warning text-dark';
                                                    }
                                                    
                                                    if ($responseTime) {
                                                        $diffInMinutes = $lotRequest->request_date->diffInMinutes($responseTime);
                                                        $diffInHours = $lotRequest->request_date->diffInHours($responseTime);
                                                        $diffInDays = $lotRequest->request_date->diffInDays($responseTime);
                                                        
                                                        if ($diffInMinutes < 60) {
                                                            $timeText = intval($diffInMinutes) . 'm';
                                                        } elseif ($diffInHours < 24) {
                                                            $timeText = intval($diffInHours) . 'h ' . intval($diffInMinutes % 60) . 'm';
                                                        } elseif ($diffInDays < 7) {
                                                            $timeText = intval($diffInDays) . 'd ' . intval($diffInHours % 24) . 'h';
                                                        } else {
                                                            $weeks = intval($diffInDays / 7);
                                                            $remainingDays = $diffInDays % 7;
                                                            $timeText = $weeks . 'w' . ($remainingDays > 0 ? ' ' . $remainingDays . 'd' : '');
                                                        }
                                                        
                                                        $finalText = $timeText . ' ' . $durationText;
                                                    } else {
                                                        $finalText = 'No data';
                                                        $badgeClass = 'bg-secondary';
                                                    }
                                                @endphp
                                                <div class="mt-1">
                                                    <span class="badge {{ $badgeClass }}" 
                                                          style="font-size: 0.7rem;"
                                                          @if(in_array($lotRequest->status, ['pending', 'in_process']))
                                                              data-request-id="{{ $lotRequest->id }}"
                                                              data-request-date="{{ $lotRequest->request_date->timestamp }}"
                                                              data-status="{{ $lotRequest->status }}"
                                                              class="badge {{ $badgeClass }} elapsed-timer"
                                                          @endif>
                                                        <i class="fas fa-stopwatch me-1"></i>{{ $finalText }}
                                                    </span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="action-buttons-group d-flex gap-1">
                                                    <!-- View Button -->
                                                    <a href="{{ route('lot-requests.show', $lotRequest) }}" 
                                                       class="btn btn-info btn-sm action-btn view-btn" 
                                                       title="View Details">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    
                                    <!-- Edit Button -->
                                    @if(Auth::user()->canManageOrders())
                                        <a href="{{ route('lot-requests.edit', $lotRequest) }}" 
                                           class="btn btn-warning btn-sm action-btn edit-btn" 
                                           title="Update Request">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                    @endif
                                                    
                                                    <!-- Delete Button -->
                                                    @if(Auth::user()->canManageOrders())
                                                        <form action="{{ route('lot-requests.destroy', $lotRequest) }}" 
                                                              method="POST" 
                                                              class="d-inline"
                                                              onsubmit="return confirm('Are you sure you want to delete this lot request? This action cannot be undone.')">
                                                            @csrf
                                                            @method('DELETE')
                                                            <button type="submit" 
                                                                    class="btn btn-danger btn-sm action-btn delete-btn" 
                                                                    title="Delete Request">
                                                                <i class="fas fa-trash"></i>
                                                            </button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-center mt-4">
                            {{ $lotRequests->links() }}
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted mb-3">No Lot Requests Found</h5>
                            <p class="text-muted mb-4">You haven't created any lot requests yet.</p>
                            <a href="{{ route('lot-requests.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Create Your First Lot Request
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            const filterOptions = document.querySelectorAll('.filter-option');
            const tableRows = document.querySelectorAll('tbody tr');
            const filterDropdown = document.getElementById('filterDropdown');
            const filterMenu = document.getElementById('filterMenu');
            
            let currentFilter = 'all';
            
            // Custom dropdown functionality
            filterDropdown.addEventListener('click', function(e) {
                e.stopPropagation();
                const isVisible = filterMenu.style.display === 'block';
                filterMenu.style.display = isVisible ? 'none' : 'block';
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!filterDropdown.contains(e.target) && !filterMenu.contains(e.target)) {
                    filterMenu.style.display = 'none';
                }
            });
            
            // Add hover effects to dropdown items
            filterOptions.forEach(option => {
                option.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = '#f8f9fa';
                });
                option.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = 'transparent';
                });
            });
            
            // Search functionality
            if (searchInput) {
                searchInput.addEventListener('input', function() {
                    const searchTerm = this.value.toLowerCase().trim();
                    console.log('Search term:', searchTerm);
                    filterTable(searchTerm, currentFilter);
                });
            }
            
            // Filter functionality
            filterOptions.forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    currentFilter = this.dataset.filter;
                    console.log('Filter selected:', currentFilter);
                    
                    const searchTerm = searchInput ? searchInput.value.toLowerCase().trim() : '';
                    filterTable(searchTerm, currentFilter);
                    
                    // Update filter button text
                    const filterButton = document.getElementById('filterDropdown');
                    const filterText = currentFilter === 'all' ? 'Filter' : `Filter: ${this.textContent}`;
                    filterButton.innerHTML = `<i class="fas fa-filter me-2"></i>${filterText}`;
                    
                    // Close dropdown
                    filterMenu.style.display = 'none';
                });
            });
            
            function filterTable(searchTerm, statusFilter) {
                let visibleCount = 0;
                console.log('Filtering table with:', searchTerm, statusFilter);
                
                tableRows.forEach(row => {
                    const requestNumber = row.cells[0].textContent.toLowerCase();
                    const requestor = row.cells[1].textContent.toLowerCase();
                    const areaStation = row.cells[2].textContent.toLowerCase();
                    const statusCell = row.cells[6]; // Updated index due to new column
                    const status = statusCell.textContent.toLowerCase().trim();
                    
                    console.log('Row status:', status, 'Filter:', statusFilter);
                    
                    // Check search match (include area station in search)
                    const searchMatch = searchTerm === '' || 
                        requestNumber.includes(searchTerm) || 
                        requestor.includes(searchTerm) ||
                        areaStation.includes(searchTerm);
                    
                    // Check status filter - handle both display names and internal values
                    let statusMatch = statusFilter === 'all';
                    if (!statusMatch) {
                        // Handle specific status mappings
                        if (statusFilter === 'getting lot' && (status === 'getting lot' || status === 'in process')) {
                            statusMatch = true;
                        } else if (status === statusFilter) {
                            statusMatch = true;
                        }
                    }
                    
                    console.log('Search match:', searchMatch, 'Status match:', statusMatch);
                    
                    if (searchMatch && statusMatch) {
                        row.style.display = '';
                        visibleCount++;
                    } else {
                        row.style.display = 'none';
                    }
                });
                
                console.log('Visible rows:', visibleCount);
                // Show/hide no results message
                updateNoResultsMessage(visibleCount);
            }
            
            function updateNoResultsMessage(visibleCount) {
                let noResultsRow = document.getElementById('noResultsRow');
                
                if (visibleCount === 0) {
                    if (!noResultsRow) {
                        const tbody = document.querySelector('tbody');
                        noResultsRow = document.createElement('tr');
                        noResultsRow.id = 'noResultsRow';
                        noResultsRow.innerHTML = `
                            <td colspan="9" class="text-center py-4">
                                <i class="fas fa-search fa-2x text-muted mb-2"></i>
                                <div class="text-muted">No requests match your search criteria</div>
                            </td>
                        `;
                        tbody.appendChild(noResultsRow);
                    }
                    noResultsRow.style.display = '';
                } else if (noResultsRow) {
                    noResultsRow.style.display = 'none';
                }
            }
            
            // Real-time elapsed time updates for active requests
            function updateElapsedTimers() {
                const activeTimers = document.querySelectorAll('.elapsed-timer');
                const now = Math.floor(Date.now() / 1000);
                
                activeTimers.forEach(timer => {
                    const requestDate = parseInt(timer.dataset.requestDate);
                    const status = timer.dataset.status;
                    
                    if (requestDate && (status === 'pending' || status === 'in_process')) {
                        const diffInSeconds = now - requestDate;
                        const diffInMinutes = Math.floor(diffInSeconds / 60);
                        const diffInHours = Math.floor(diffInMinutes / 60);
                        const diffInDays = Math.floor(diffInHours / 24);
                        
                        let timeText;
                        if (diffInMinutes < 60) {
                            timeText = diffInMinutes + 'm';
                        } else if (diffInHours < 24) {
                            timeText = diffInHours + 'h ' + (diffInMinutes % 60) + 'm';
                        } else if (diffInDays < 7) {
                            timeText = diffInDays + 'd ' + (diffInHours % 24) + 'h';
                        } else {
                            const weeks = Math.floor(diffInDays / 7);
                            const remainingDays = diffInDays % 7;
                            timeText = weeks + 'w' + (remainingDays > 0 ? ' ' + remainingDays + 'd' : '');
                        }
                        
                        timer.innerHTML = '<i class="fas fa-stopwatch me-1"></i>' + timeText + ' elapsed';
                    }
                });
            }
            
            
            // Update elapsed timers every minute
            updateElapsedTimers();
            setInterval(updateElapsedTimers, 60000); // Update every 60 seconds
        });
    </script>
</x-app-layout>
