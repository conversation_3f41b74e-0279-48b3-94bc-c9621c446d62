<x-app-layout>
    <x-slot name="header">
        Customer Analytics
    </x-slot>

    <!-- Customer Overview Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success bg-opacity-10 text-success me-3">
                            <i class="fas fa-users"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($customerData['customer_lifetime_value']->count()) }}</h3>
                            <p class="text-muted mb-0">Total Customers</p>
                            <small class="text-success">Active user base</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary me-3">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($customerData['customer_lifetime_value']->avg('lifetime_value'), 2) }}</h3>
                            <p class="text-muted mb-0">Avg Lifetime Value</p>
                            <small class="text-primary">Per customer</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info bg-opacity-10 text-info me-3">
                            <i class="fas fa-sync-alt"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($customerData['retention_metrics']['retention_rate'], 1) }}%</h3>
                            <p class="text-muted mb-0">Retention Rate</p>
                            <small class="text-info">30-day active</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning bg-opacity-10 text-warning me-3">
                            <i class="fas fa-user-plus"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($customerData['acquisition_trends']->sum('new_customers')) }}</h3>
                            <p class="text-muted mb-0">New Customers</p>
                            <small class="text-warning">Last 30 days</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <!-- Customer Acquisition Trends -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Customer Acquisition Trends</h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px;">
                        <canvas id="acquisitionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Customer Segments -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Customer Segments</h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px;">
                        <canvas id="segmentsChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Customer Data Tables -->
    <div class="row g-4 mb-4">
        <!-- Top Customers -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Top Customers by Spending</h5>
                </div>
                <div class="card-body">
                    @if($customerData['top_customers']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Customer</th>
                                        <th>Orders</th>
                                        <th>Total Spent</th>
                                        <th>Avg Order</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($customerData['top_customers']->take(10) as $customer)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $customer->name }}</strong>
                                                <small class="d-block text-muted">{{ $customer->email }}</small>
                                            </div>
                                        </td>
                                        <td>{{ $customer->total_orders }}</td>
                                        <td><strong>${{ number_format($customer->total_spent, 2) }}</strong></td>
                                        <td>${{ number_format($customer->total_spent / max($customer->total_orders, 1), 2) }}</td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No customer data available</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Customer Lifetime Value -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Customer Lifetime Value Distribution</h5>
                </div>
                <div class="card-body">
                    @if($customerData['customer_lifetime_value']->count() > 0)
                        @php
                            $clvRanges = [
                                ['min' => 0, 'max' => 50, 'label' => '$0 - $50', 'color' => 'danger'],
                                ['min' => 50, 'max' => 200, 'label' => '$50 - $200', 'color' => 'warning'],
                                ['min' => 200, 'max' => 500, 'label' => '$200 - $500', 'color' => 'primary'],
                                ['min' => 500, 'max' => 1000, 'label' => '$500 - $1K', 'color' => 'success'],
                                ['min' => 1000, 'max' => 999999, 'label' => '$1K+', 'color' => 'info']
                            ];
                        @endphp
                        
                        @foreach($clvRanges as $range)
                            @php
                                $count = $customerData['customer_lifetime_value']->whereBetween('lifetime_value', [$range['min'], $range['max']])->count();
                                $percentage = $customerData['customer_lifetime_value']->count() > 0 ? ($count / $customerData['customer_lifetime_value']->count()) * 100 : 0;
                                $badgeClass = 'badge bg-' . $range['color'] . '-transparent text-' . $range['color'] . ' me-2';
                                $progressBarClass = 'progress-bar bg-' . $range['color'];
                            @endphp
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <span class="{{ $badgeClass }}">{{ $range['label'] }}</span>
                                    <span>{{ $count }} customers</span>
                                </div>
                                <div class="d-flex align-items-center">
                                    <div class="progress me-3" style="width: 100px; height: 8px;">
                                        <div class="{{ $progressBarClass }}" data-width="{{ $percentage }}"></div>
                                    </div>
                                    <span class="fw-bold">{{ number_format($percentage, 1) }}%</span>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No CLV data available</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Back Navigation -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <a href="{{ route('management.analytics.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Analytics Overview
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
    </style>

    @php
        // Prepare chart data safely
        $acquisitionData = $customerData['acquisition_trends']->pluck('new_customers')->toArray();
        $acquisitionLabels = $customerData['acquisition_trends']->map(function($item) {
            // The date is already a string from selectRaw, so no need to format
            return $item->date ?? '';
        })->toArray();
        
        $segmentsData = isset($customerData['customer_segments']) ? $customerData['customer_segments']->pluck('count')->toArray() : [];
        $segmentsLabels = isset($customerData['customer_segments']) ? $customerData['customer_segments']->pluck('segment')->toArray() : [];
        
        // Convert to JSON strings for JavaScript
        $acquisitionDataJson = json_encode($acquisitionData);
        $acquisitionLabelsJson = json_encode($acquisitionLabels);
        $segmentsDataJson = json_encode($segmentsData);
        $segmentsLabelsJson = json_encode($segmentsLabels);
    @endphp

    <!-- Data attributes for JavaScript -->
    <div id="chart-data" 
         data-acquisition-data="{{ htmlspecialchars($acquisitionDataJson) }}"
         data-acquisition-labels="{{ htmlspecialchars($acquisitionLabelsJson) }}"
         data-segments-data="{{ htmlspecialchars($segmentsDataJson) }}"
         data-segments-labels="{{ htmlspecialchars($segmentsLabelsJson) }}"
         style="display: none;"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Set progress bar widths from data attributes
            document.querySelectorAll('[data-width]').forEach(function(element) {
                const width = element.getAttribute('data-width');
                element.style.width = width + '%';
            });

            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded');
                return;
            }

            // Get data from HTML attributes
            const chartDataEl = document.getElementById('chart-data');
            if (!chartDataEl) return;
            
            const acquisitionData = JSON.parse(chartDataEl.dataset.acquisitionData || '[]');
            const acquisitionLabels = JSON.parse(chartDataEl.dataset.acquisitionLabels || '[]');
            const segmentsData = JSON.parse(chartDataEl.dataset.segmentsData || '[]');
            const segmentsLabels = JSON.parse(chartDataEl.dataset.segmentsLabels || '[]');

            // Customer Acquisition Chart
            const acquisitionCtx = document.getElementById('acquisitionChart');
            if (acquisitionCtx) {
                
                new Chart(acquisitionCtx, {
                    type: 'line',
                    data: {
                        labels: acquisitionLabels,
                        datasets: [{
                            label: 'New Customers',
                            data: acquisitionData,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                grid: {
                                    color: 'rgba(0, 0, 0, 0.1)'
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Customer Segments Chart
            const segmentsCtx = document.getElementById('segmentsChart');
            if (segmentsCtx) {
                
                new Chart(segmentsCtx, {
                    type: 'doughnut',
                    data: {
                        labels: segmentsLabels,
                        datasets: [{
                            data: segmentsData,
                            backgroundColor: [
                                '#22c55e', // VIP - Green
                                '#6366f1', // Premium - Blue
                                '#f59e0b', // Regular - Orange
                                '#ef4444'  // New - Red
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</x-app-layout>