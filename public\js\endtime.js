/**
 * Endtime Create Page JavaScript
 * Handles lot forecasting, equipment management, and endtime calculations
 */

(function () {
    "use strict";

    // Global variables
    let equipmentData = [];
    let equipmentCounter = 1;
    let currentLotData = null;

    // Initialize when DOM is ready
    document.addEventListener("DOMContentLoaded", function () {
        initializeEndtimePage();
    });

    /**
     * Initialize the endtime page functionality
     */
    function initializeEndtimePage() {
        console.log("🚀 Initializing endtime page...");

        // Initialize form handlers first
        initializeLotLookup();
        initializeEquipmentHandlers();
        initializeFormValidation();
        initializeTimeHandlers();

        // Set default current time for first equipment
        setCurrentTimeForEquipment(1);

        // Load equipment data after DOM setup
        loadEquipmentData();

        console.log('✅ Endtime page initialized successfully');
        
        // Set default focus to Lot No. field
        setTimeout(() => {
            const lotIdInput = document.getElementById('lot_id');
            if (lotIdInput) {
                lotIdInput.focus();
                console.log('🎯 Default focus set to Lot No. field');
            }
        }, 100);
            }
        }, 2000);
    }

    /**
     * Load equipment data from the server
     */
    function loadEquipmentData() {
        console.log("Loading equipment data...");

        fetch("/endtime/equipment-data", {
            method: "GET",
            headers: {
                "X-Requested-With": "XMLHttpRequest",
                Accept: "application/json",
                "Content-Type": "application/json",
            },
        })
            .then((response) => {
                console.log("Equipment data response status:", response.status);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then((data) => {
                console.log("Equipment data response:", data);
                if (data.success) {
                    equipmentData = data.equipment || [];
                    console.log(
                        "Equipment data loaded successfully:",
                        equipmentData.length,
                        "items"
                    );

                    // Log first few items for debugging
                    if (equipmentData.length > 0) {
                        console.log("First equipment item:", equipmentData[0]);
                        console.log(
                            "Sample equipment numbers:",
                            equipmentData.slice(0, 5).map((eq) => eq.eqp_no)
                        );
                    }

                    // Initialize equipment search for existing rows
                    setTimeout(() => {
                        initializeEquipmentSearch();
                        console.log(
                            "✅ Equipment search initialized with",
                            equipmentData.length,
                            "items"
                        );
                    }, 100);
                } else {
                    console.error(
                        "Failed to load equipment data:",
                        data.message || "Unknown error"
                    );
                    showNotification(
                        "Failed to load equipment data: " +
                            (data.message || "Unknown error"),
                        "error"
                    );
                }
            })
            .catch((error) => {
                console.error("Error loading equipment data:", error);
                showNotification(
                    "Error loading equipment data: " + error.message,
                    "error"
                );

                // Try to load sample data for testing
                equipmentData = [
                    {
                        eqp_no: "EQP001",
                        eqp_line: "LINE1",
                        eqp_area: "AREA1",
                        eqp_type: "TYPE1",
                        daily_capa: 1000,
                    },
                    {
                        eqp_no: "EQP002",
                        eqp_line: "LINE1",
                        eqp_area: "AREA1",
                        eqp_type: "TYPE1",
                        daily_capa: 1200,
                    },
                    {
                        eqp_no: "TEST001",
                        eqp_line: "LINE2",
                        eqp_area: "AREA2",
                        eqp_type: "TYPE2",
                        daily_capa: 800,
                    },
                ];
                console.log(
                    "Using sample equipment data for testing:",
                    equipmentData.length,
                    "items"
                );
                initializeEquipmentSearch();
            });
    }

    /**
     * Initialize lot lookup functionality
     */
    function initializeLotLookup() {
        const lotIdInput = document.getElementById("lot_id");
        const searchButton = document.querySelector(".search-btn");

        if (lotIdInput && searchButton) {
            // Auto-lookup on blur (no auto-navigation)
            lotIdInput.addEventListener('blur', function() {
                const lotId = this.value.trim().toUpperCase();
                if (lotId && lotId.length >= 6) {
                    lookupLotData(lotId, false);
                }
            });

            // Manual lookup on button click
            searchButton.addEventListener("click", function () {
                const lotId = lotIdInput.value.trim().toUpperCase();
                if (lotId) {
                    lotIdInput.value = lotId;
                    lookupLotData(lotId);
                } else {
                    showNotification("Please enter a lot ID", "warning");
                }
            });

            // Enter key handling with auto-navigation
            lotIdInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const lotId = this.value.trim().toUpperCase();
                    if (lotId) {
                        this.value = lotId;
                        lookupLotData(lotId, true); // Pass true to indicate auto-navigation
                    } else {
                        showNotification('Please enter a lot ID', 'warning');
                    }
                }
            });
        }
    }

    /**
     * Lookup lot data from the database
     */
    function lookupLotData(lotId, autoNavigate = false) {
        if (!lotId) return;
        
        showLoadingState(true);
        
        fetch(`/endtime/lookup/${encodeURIComponent(lotId)}`, {
            method: 'GET',
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'Accept': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            showLoadingState(false);
            
            if (data.success && data.lot) {
                populateLotData(data.lot);
                showNotification('Lot data loaded successfully', 'success');
                
                // Auto-navigate to equipment field if requested
                if (autoNavigate) {
                    setTimeout(() => {
                        const equipmentInput = document.getElementById('equipment_1');
                        if (equipmentInput) {
                            equipmentInput.focus();
                            equipmentInput.select(); // Select any existing text
                            console.log('🎯 Auto-navigated to Equipment field');
                        }
                    }, 500); // Small delay to allow user to see the success notification
                }
            } else {
                clearLotData();
                showNotification(data.message || 'Lot not found', 'warning');
            }
        })
        .catch(error => {
            showLoadingState(false);
            console.error('Error looking up lot:', error);
            showNotification('Error looking up lot data', 'error');
        });
    }
                showNotification("Error looking up lot data", "error");
            });
    }

    /**
     * Populate form with lot data
     */
    function populateLotData(lotData) {
        currentLotData = lotData;

        // Update quantity display
        const qtyValue = document.getElementById("lotQtyValue");
        if (qtyValue && lotData.lot_qty) {
            qtyValue.textContent = parseInt(lotData.lot_qty).toLocaleString();
        }

        // Update lot details
        const detailFields = {
            detailLotNo: lotData.lot_id,
            detailModel: lotData.model_15,
            detailLipas: lotData.lipas_yn === "Y" ? "Yes" : "No",
            detailSize: lotData.lot_size,
        };

        Object.entries(detailFields).forEach(([elementId, value]) => {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = value || "--";
            }
        });

        // Update hidden form fields
        const hiddenFields = {
            model_15: lotData.model_15,
            lot_size: lotData.lot_size,
            lot_qty: lotData.lot_qty,
            work_type: lotData.work_type,
            lot_type_hidden: lotData.lot_type,
            lipas_yn: lotData.lipas_yn,
        };

        Object.entries(hiddenFields).forEach(([fieldId, value]) => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.value = value || "";
            }
        });

        // Update lot type radio buttons
        const lotType = lotData.lot_type || "MAIN";
        const mainRadio = document.querySelector('input[value="MAIN"]');
        const rllyRadio = document.querySelector('input[value="RL/LY"]');

        if (mainRadio && rllyRadio) {
            if (lotType === "RL/LY") {
                rllyRadio.checked = true;
                mainRadio.checked = false;
            } else {
                mainRadio.checked = true;
                rllyRadio.checked = false;
            }
        }

        // Trigger calculation
        calculateEndtime();
    }

    /**
     * Clear lot data fields
     */
    function clearLotData() {
        currentLotData = null;

        // Clear quantity display
        const qtyValue = document.getElementById("lotQtyValue");
        if (qtyValue) {
            qtyValue.textContent = "--";
        }

        // Clear lot details
        const detailIds = [
            "detailLotNo",
            "detailModel",
            "detailLipas",
            "detailSize",
        ];
        detailIds.forEach((id) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = "--";
            }
        });

        // Clear hidden fields
        const hiddenFieldIds = [
            "model_15",
            "lot_size",
            "lot_qty",
            "work_type",
            "lot_type_hidden",
            "lipas_yn",
        ];
        hiddenFieldIds.forEach((id) => {
            const field = document.getElementById(id);
            if (field) {
                field.value = "";
            }
        });

        // Clear calculation results
        clearCalculationResults();
    }

    /**
     * Initialize equipment handlers
     */
    function initializeEquipmentHandlers() {
        const addEquipmentBtn = document.getElementById("addEquipmentBtn");

        if (addEquipmentBtn) {
            addEquipmentBtn.addEventListener("click", addEquipmentRow);
        }

        // Initialize existing equipment search
        initializeEquipmentSearch();
    }

    /**
     * Initialize equipment search functionality
     */
    function initializeEquipmentSearch() {
        const equipmentInputs = document.querySelectorAll(".equipment-search");

        equipmentInputs.forEach((input) => {
            if (!input.hasAttribute("data-initialized")) {
                setupEquipmentSearch(input);
                input.setAttribute("data-initialized", "true");
            }
        });
    }

    /**
     * Setup equipment search for a specific input
     */
    function setupEquipmentSearch(input) {
        let timeoutId;

        console.log("Setting up equipment search for input:", input.id);

        input.addEventListener("input", function () {
            clearTimeout(timeoutId);
            const query = this.value.trim();

            console.log(
                "Equipment search input:",
                query,
                "Equipment data count:",
                equipmentData.length
            );

            if (query.length >= 1) {
                // Reduced from 2 to 1 for faster search
                timeoutId = setTimeout(() => {
                    showEquipmentDropdown(this, query);
                }, 200); // Reduced delay for faster response
            } else {
                hideEquipmentDropdown(this);
            }
        });

        input.addEventListener("blur", function () {
            setTimeout(() => {
                hideEquipmentDropdown(this);
            }, 300); // Increased delay to allow clicking on dropdown items
        });

        input.addEventListener("focus", function () {
            const query = this.value.trim();
            if (query.length >= 1) {
                showEquipmentDropdown(this, query);
            }
        });

        // Handle equipment selection change
        input.addEventListener("change", function () {
            updateEquipmentInfo(this);
            calculateEndtime();
        });

        // Add keydown handler for arrow keys and enter
        input.addEventListener("keydown", function (e) {
            const dropdown = this.parentNode.querySelector(
                ".equipment-dropdown:not(.d-none)"
            );
            if (!dropdown) return;

            const items = dropdown.querySelectorAll(
                ".dropdown-item:not(.text-muted)"
            );
            let selectedIndex = -1;

            // Find currently selected item
            items.forEach((item, index) => {
                if (item.classList.contains("selected")) {
                    selectedIndex = index;
                }
            });

            if (e.key === "ArrowDown") {
                e.preventDefault();
                selectedIndex = Math.min(selectedIndex + 1, items.length - 1);
                updateSelectedItem(items, selectedIndex);
            } else if (e.key === "ArrowUp") {
                e.preventDefault();
                selectedIndex = Math.max(selectedIndex - 1, 0);
                updateSelectedItem(items, selectedIndex);
            } else if (e.key === "Enter") {
                e.preventDefault();
                if (selectedIndex >= 0 && items[selectedIndex]) {
                    items[selectedIndex].click();
                }
            } else if (e.key === "Escape") {
                hideEquipmentDropdown(this);
            }
        });
    }

    /**
     * Show equipment dropdown with filtered options using new structure
     */
    function showEquipmentDropdown(input, query) {
        console.log("🔍 Showing dropdown for query:", query);

        const wrapper = input.closest(".equipment-search-wrapper");
        if (!wrapper) {
            console.error("Equipment search wrapper not found");
            return;
        }

        const dropdown = wrapper.querySelector(".equipment-dropdown");
        if (!dropdown) {
            console.error("Equipment dropdown not found");
            return;
        }

        const resultsContainer = dropdown.querySelector(".dropdown-body");
        const resultsCount = dropdown.querySelector(".results-count");
        const spinner = wrapper.querySelector(".loading-spinner");

        if (!resultsContainer) {
            console.error("Results container not found");
            return;
        }

        // Show spinner
        if (spinner) spinner.classList.remove("d-none");

        const filteredEquipment = filterEquipment(query);
        console.log("🔧 Filtered equipment count:", filteredEquipment.length);

        // Update results count
        if (resultsCount) {
            resultsCount.textContent = `${filteredEquipment.length} result${
                filteredEquipment.length !== 1 ? "s" : ""
            }`;
        }

        // Clear previous results
        resultsContainer.innerHTML = "";

        // Show dropdown
        dropdown.classList.remove("d-none");

        if (filteredEquipment.length === 0) {
            resultsContainer.innerHTML =
                '<div class="no-results">No equipment found</div>';
            if (spinner) spinner.classList.add("d-none");
            return;
        }

        // Create equipment options
        filteredEquipment.slice(0, 10).forEach((equipment, index) => {
            const option = document.createElement("div");
            option.className = "equipment-option";
            option.innerHTML = `
                <div class="equipment-option-main">${equipment.eqp_no}</div>
                <div class="equipment-option-details">${equipment.eqp_line} - ${
                equipment.eqp_area
            } | Capacity: ${parseInt(
                equipment.daily_capa || 0
            ).toLocaleString()}/day</div>
            `;

            // Handle selection
            option.addEventListener("click", function (e) {
                e.stopPropagation();
                console.log("✅ Equipment selected:", equipment.eqp_no);

                input.value = equipment.eqp_no;
                input.classList.add("selected");
                hideEquipmentDropdown(input);
                updateEquipmentInfo(input);
                calculateEndtime();
                input.focus();
            });

            // Hover effects
            option.addEventListener("mouseenter", function () {
                dropdown
                    .querySelectorAll(".equipment-option")
                    .forEach((opt) => opt.classList.remove("selected"));
                this.classList.add("selected");
            });

            resultsContainer.appendChild(option);

            // Select first item by default
            if (index === 0) {
                option.classList.add("selected");
            }
        });

        // Hide spinner
        if (spinner) spinner.classList.add("d-none");
    }

    // createEquipmentDropdown function removed - using new HTML structure instead

    /**
     * Hide equipment dropdown
     */
    function hideEquipmentDropdown(input) {
        const wrapper = input.closest(".equipment-search-wrapper");
        if (wrapper) {
            const dropdown = wrapper.querySelector(".equipment-dropdown");
            if (dropdown) {
                dropdown.classList.add("d-none");
            }
        }
    }

    /**
     * Update selected item in dropdown for keyboard navigation
     */
    function updateSelectedItem(items, selectedIndex) {
        items.forEach((item, index) => {
            item.classList.remove("selected");
            item.style.backgroundColor = "";
            if (index === selectedIndex) {
                item.classList.add("selected");
                item.style.backgroundColor = "#f3f4f6";
                item.scrollIntoView({ block: "nearest" });
            }
        });
    }

    /**
     * Filter equipment based on query with wildcard search
     */
    function filterEquipment(query) {
        if (!query || equipmentData.length === 0) {
            console.log("No query or no equipment data");
            return [];
        }

        const searchTerm = query.toLowerCase().trim();
        console.log(
            "Searching for:",
            searchTerm,
            "in",
            equipmentData.length,
            "equipment items"
        );

        const filtered = equipmentData.filter((equipment) => {
            if (!equipment) return false;

            const eqpNo = (equipment.eqp_no || "").toLowerCase();
            const eqpLine = (equipment.eqp_line || "").toLowerCase();
            const eqpArea = (equipment.eqp_area || "").toLowerCase();
            const eqpType = (equipment.eqp_type || "").toLowerCase();
            const eqpClass = (equipment.eqp_class || "").toLowerCase();

            // Improved search logic
            // 1. Exact match or starts with
            if (eqpNo === searchTerm || eqpNo.startsWith(searchTerm)) {
                return true;
            }

            // 2. Contains the search term (for cases like "280" in "VI280")
            if (eqpNo.includes(searchTerm)) {
                return true;
            }

            // 3. Search in other fields
            if (
                eqpLine.includes(searchTerm) ||
                eqpArea.includes(searchTerm) ||
                eqpType.includes(searchTerm) ||
                eqpClass.includes(searchTerm)
            ) {
                return true;
            }

            // 4. Advanced pattern matching for equipment numbers
            // Handle cases where user types digits that should match equipment numbers
            if (/^\d+$/.test(searchTerm)) {
                // If search term is purely numeric, look for it anywhere in equipment number
                const numericParts = eqpNo.match(/\d+/g);
                if (numericParts) {
                    return numericParts.some(
                        (part) =>
                            part.includes(searchTerm) ||
                            part.endsWith(searchTerm)
                    );
                }
            }

            return false;
        });

        // Sort results - exact matches first, then starts with, then contains, then numeric matches
        filtered.sort((a, b) => {
            const aEqpNo = (a.eqp_no || "").toLowerCase();
            const bEqpNo = (b.eqp_no || "").toLowerCase();

            // Exact match gets highest priority
            if (aEqpNo === searchTerm && bEqpNo !== searchTerm) return -1;
            if (bEqpNo === searchTerm && aEqpNo !== searchTerm) return 1;

            // Starts with gets second priority
            if (aEqpNo.startsWith(searchTerm) && !bEqpNo.startsWith(searchTerm))
                return -1;
            if (bEqpNo.startsWith(searchTerm) && !aEqpNo.startsWith(searchTerm))
                return 1;

            // For numeric searches, prioritize matches at the end of equipment numbers
            if (/^\d+$/.test(searchTerm)) {
                const aEndsWithSearch = aEqpNo.endsWith(searchTerm);
                const bEndsWithSearch = bEqpNo.endsWith(searchTerm);

                if (aEndsWithSearch && !bEndsWithSearch) return -1;
                if (bEndsWithSearch && !aEndsWithSearch) return 1;

                // If both contain the search term, prefer shorter equipment numbers
                if (
                    aEqpNo.includes(searchTerm) &&
                    bEqpNo.includes(searchTerm)
                ) {
                    return aEqpNo.length - bEqpNo.length;
                }
            }

            // General contains match
            const aContains = aEqpNo.includes(searchTerm);
            const bContains = bEqpNo.includes(searchTerm);

            if (aContains && !bContains) return -1;
            if (bContains && !aContains) return 1;

            // Alphabetical as final sort
            return aEqpNo.localeCompare(bEqpNo);
        });

        console.log("Filtered results:", filtered.length);
        return filtered;
    }

    /**
     * Update equipment info when equipment is selected
     */
    function updateEquipmentInfo(input) {
        const equipmentNo = input.value.trim();
        const row = input.closest("tr");
        const index = row.getAttribute("data-index");

        if (!equipmentNo || !row) return;

        const equipment = equipmentData.find((eq) => eq.eqp_no === equipmentNo);

        if (equipment) {
            // Update capacity display
            const capacityCell = document.getElementById(
                `equipmentCapacity_${index}`
            );
            if (capacityCell) {
                const dailyCapacity = parseInt(equipment.daily_capa || 0);
                capacityCell.innerHTML = `<small class="text-success">${dailyCapacity.toLocaleString()}/day</small>`;
            }
        } else {
            // Clear capacity if equipment not found
            const capacityCell = document.getElementById(
                `equipmentCapacity_${index}`
            );
            if (capacityCell) {
                capacityCell.innerHTML = '<small class="text-muted">--</small>';
            }
        }
    }

    /**
     * Add new equipment row
     */
    function addEquipmentRow() {
        const tbody = document.getElementById("equipmentTableBody");
        if (!tbody) return;

        const existingRows = tbody.querySelectorAll(".equipment-row");
        if (existingRows.length >= 10) {
            showNotification("Maximum 10 equipment allowed", "warning");
            return;
        }

        equipmentCounter++;
        const newIndex = equipmentCounter;

        const row = document.createElement("tr");
        row.className = "equipment-row";
        row.setAttribute("data-index", newIndex);

        row.innerHTML = `
            <td class="row-number">${existingRows.length + 1}</td>
            <td>
                <div class="equipment-search-wrapper">
                    <div class="search-input-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="equipment-search form-control" id="equipment_${newIndex}" name="equipment[${newIndex}][eqp_no]" placeholder="Search equipment..." autocomplete="off" required />
                        <div class="loading-spinner d-none" id="equipmentSpinner_${newIndex}">
                            <i class="fas fa-spinner fa-spin"></i>
                        </div>
                    </div>
                    <div class="equipment-dropdown d-none" id="equipmentDropdown_${newIndex}">
                        <div class="dropdown-header">
                            <span class="dropdown-title">Available Equipment</span>
                            <span class="results-count" id="equipmentResultsCount_${newIndex}">0 results</span>
                        </div>
                        <div class="dropdown-body" id="equipmentResults_${newIndex}">
                            <!-- Dynamic equipment options will be inserted here -->
                        </div>
                    </div>
                </div>
            </td>
            <td>
                <div class="ng-input">
                    <input type="number" id="ng_percent_${newIndex}" name="equipment[${newIndex}][ng_percent]" value="0" min="0" max="100" step="0.1" />
                    <span>%</span>
                </div>
            </td>
            <td>
                <div class="time-input">
                    <input type="datetime-local" id="start_time_${newIndex}" name="equipment[${newIndex}][start_time]" step="60" required />
                    <button type="button" class="time-now-btn" title="Set to current time" data-equipment-index="${newIndex}">
                        <i class="fas fa-clock"></i>
                    </button>
                </div>
            </td>
            <td class="equipment-capacity" id="equipmentCapacity_${newIndex}">
                <small class="text-muted">--</small>
            </td>
            <td class="equipment-endtime" id="equipmentEndTime_${newIndex}">
                <small class="text-muted">--</small>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger equipment-remove-btn" data-equipment-index="${newIndex}" title="Remove">
                    <i class="fas fa-times"></i>
                </button>
            </td>
        `;

        tbody.appendChild(row);

        // Setup equipment search for new row
        const newInput = row.querySelector(".equipment-search");
        setupEquipmentSearch(newInput);

        // Setup remove button
        const removeBtn = row.querySelector(".equipment-remove-btn");
        removeBtn.addEventListener("click", function () {
            removeEquipmentRow(this);
        });

        // Setup time now button
        const timeNowBtn = row.querySelector(".time-now-btn");
        timeNowBtn.addEventListener("click", function () {
            setCurrentTimeForEquipment(newIndex);
        });

        // Setup change handlers
        const inputs = row.querySelectorAll("input");
        inputs.forEach((input) => {
            input.addEventListener("change", calculateEndtime);
        });

        // Show remove buttons if more than one row
        updateRemoveButtons();
        updateEquipmentCount();

        // Set current time for new equipment
        setCurrentTimeForEquipment(newIndex);

        showNotification("Equipment row added", "success");
    }

    /**
     * Remove equipment row
     */
    function removeEquipmentRow(button) {
        const row = button.closest("tr");
        if (!row) return;

        row.remove();

        // Renumber rows
        renumberEquipmentRows();
        updateRemoveButtons();
        updateEquipmentCount();
        calculateEndtime();

        showNotification("Equipment row removed", "info");
    }

    /**
     * Renumber equipment rows
     */
    function renumberEquipmentRows() {
        const rows = document.querySelectorAll(".equipment-row");
        rows.forEach((row, index) => {
            const numberCell = row.querySelector(".row-number");
            if (numberCell) {
                numberCell.textContent = index + 1;
            }
        });
    }

    /**
     * Update remove button visibility
     */
    function updateRemoveButtons() {
        const removeButtons = document.querySelectorAll(
            ".equipment-remove-btn"
        );
        const showButtons = removeButtons.length > 1;

        removeButtons.forEach((btn) => {
            btn.style.display = showButtons ? "inline-block" : "none";
        });
    }

    /**
     * Update equipment count display
     */
    function updateEquipmentCount() {
        const countBadge = document.getElementById("equipmentCount");
        const rows = document.querySelectorAll(".equipment-row");

        if (countBadge) {
            const count = rows.length;
            countBadge.textContent = `${count} Mc${count !== 1 ? "s" : ""}`;
        }
    }

    /**
     * Initialize time handlers
     */
    function initializeTimeHandlers() {
        // Handle time now buttons
        document.addEventListener("click", function (e) {
            if (e.target.closest(".time-now-btn")) {
                const btn = e.target.closest(".time-now-btn");
                const index = btn.getAttribute("data-equipment-index");
                setCurrentTimeForEquipment(index);
            }
        });

        // Handle time input changes
        document.addEventListener("change", function (e) {
            if (e.target.matches('input[type="datetime-local"]')) {
                calculateEndtime();
            }
        });
    }

    /**
     * Set current time for equipment in Asia/Manila timezone
     */
    function setCurrentTimeForEquipment(index) {
        const timeInput = document.getElementById(`start_time_${index}`);
        if (timeInput) {
            // Get current time in Asia/Manila timezone
            const now = new Date();

            // Convert to Asia/Manila timezone (UTC+8)
            const manilaOffset = 8 * 60; // 8 hours in minutes
            const localOffset = now.getTimezoneOffset(); // Local offset from UTC in minutes (negative for ahead of UTC)
            const manilaTime = new Date(
                now.getTime() + (manilaOffset + localOffset) * 60000
            );

            // Round to nearest minute
            manilaTime.setSeconds(0, 0);

            // Format for datetime-local input (YYYY-MM-DDTHH:MM)
            const year = manilaTime.getFullYear();
            const month = String(manilaTime.getMonth() + 1).padStart(2, "0");
            const day = String(manilaTime.getDate()).padStart(2, "0");
            const hours = String(manilaTime.getHours()).padStart(2, "0");
            const minutes = String(manilaTime.getMinutes()).padStart(2, "0");

            const formattedTime = `${year}-${month}-${day}T${hours}:${minutes}`;
            timeInput.value = formattedTime;

            console.log(
                "Set time for equipment",
                index,
                ":",
                formattedTime,
                "(Manila time)"
            );
            calculateEndtime();
        }
    }

    /**
     * Calculate endtime based on equipment and lot data
     */
    function calculateEndtime() {
        if (!currentLotData || !currentLotData.lot_qty) {
            clearCalculationResults();
            return;
        }

        const equipmentAssignments = getEquipmentAssignments();
        if (equipmentAssignments.length === 0) {
            clearCalculationResults();
            return;
        }

        try {
            const result = calculateEndtimeLogic(
                currentLotData.lot_qty,
                equipmentAssignments
            );
            displayCalculationResults(result);
            updateFormSubmitState(true);
        } catch (error) {
            console.error("Calculation error:", error);
            clearCalculationResults();
            updateFormSubmitState(false);
        }
    }

    /**
     * Get equipment assignments from form
     */
    function getEquipmentAssignments() {
        const assignments = [];
        const rows = document.querySelectorAll(".equipment-row");

        rows.forEach((row) => {
            const eqpInput = row.querySelector(".equipment-search");
            const timeInput = row.querySelector('input[type="datetime-local"]');
            const ngInput = row.querySelector('input[name*="[ng_percent]"]');

            const eqpNo = eqpInput?.value?.trim();
            const startTime = timeInput?.value;
            const ngPercent = parseFloat(ngInput?.value || 0);

            if (eqpNo && startTime) {
                const equipment = equipmentData.find(
                    (eq) => eq.eqp_no === eqpNo
                );
                if (equipment) {
                    assignments.push({
                        eqp_no: eqpNo,
                        start_time: startTime,
                        ng_percent: ngPercent,
                        daily_capacity: parseFloat(equipment.daily_capa || 0),
                    });
                }
            }
        });

        return assignments;
    }

    /**
     * Calculate endtime using the same logic as backend
     */
    function calculateEndtimeLogic(lotQty, equipmentAssignments) {
        if (!lotQty || lotQty <= 0 || !equipmentAssignments.length) {
            throw new Error("Invalid parameters");
        }

        const profiles = [];

        // Build equipment profiles
        equipmentAssignments.forEach((assignment) => {
            const dailyCapacity = assignment.daily_capacity;
            if (dailyCapacity <= 0) return;

            const ngPercent = Math.max(
                0,
                Math.min(100, assignment.ng_percent || 0)
            );
            const grossRatePerMinute = dailyCapacity / 1440; // Convert daily to per minute
            const netGoodRatePerMinute =
                grossRatePerMinute * (1 - ngPercent / 100);

            if (netGoodRatePerMinute <= 0) return;

            const startTime = new Date(assignment.start_time);

            profiles.push({
                eqp_no: assignment.eqp_no,
                start_time: startTime,
                rate_per_minute: netGoodRatePerMinute,
                daily_capacity: dailyCapacity,
                ng_percent: ngPercent,
            });
        });

        if (profiles.length === 0) {
            throw new Error("Invalid machine number!");
        }

        // Sort profiles by start time
        profiles.sort(
            (a, b) => a.start_time.getTime() - b.start_time.getTime()
        );

        const earliestStart = profiles[0].start_time;
        const latestStart = profiles[profiles.length - 1].start_time;
        const totalRate = profiles.reduce(
            (sum, p) => sum + p.rate_per_minute,
            0
        );

        if (totalRate <= 0) {
            throw new Error("Invalid total production rate");
        }

        // Calculate baseline completion time from the latest start
        const baselineMinutes = lotQty / totalRate;
        const baselineCompletionTime = new Date(
            latestStart.getTime() + baselineMinutes * 60000
        );

        // Calculate time savings from early starts
        const totalTimeSavingsMinutes =
            (latestStart.getTime() - earliestStart.getTime()) / 60000;
        const timeSavingsPerEquipment =
            totalTimeSavingsMinutes / profiles.length;

        // Final endtime = baseline - time savings per equipment
        const estimatedEndtime = new Date(
            baselineCompletionTime.getTime() - timeSavingsPerEquipment * 60000
        );

        return {
            success: true,
            est_endtime: estimatedEndtime,
            processing_hours: baselineMinutes / 60,
            total_capacity: profiles.reduce(
                (sum, p) => sum + p.daily_capacity,
                0
            ),
            equipment_count: profiles.length,
            earliest_start: earliestStart,
            latest_start: latestStart,
            baseline_minutes: baselineMinutes,
            total_time_savings: totalTimeSavingsMinutes,
            time_savings_per_equipment: timeSavingsPerEquipment,
            profiles: profiles,
        };
    }

    /**
     * Display calculation results
     */
    function displayCalculationResults(result) {
        const endTimeElement = document.querySelector(".end-time");
        const timeDiffElement = document.querySelector(".time-diff");

        if (endTimeElement && result.est_endtime) {
            const options = {
                weekday: "short",
                month: "short",
                day: "numeric",
                hour: "2-digit",
                minute: "2-digit",
                hour12: false,
            };
            endTimeElement.textContent = result.est_endtime.toLocaleDateString(
                "en-US",
                options
            );
        }

        if (timeDiffElement && result.processing_hours) {
            const hours = Math.floor(result.processing_hours);
            const minutes = Math.round((result.processing_hours - hours) * 60);
            timeDiffElement.textContent = `${hours}h ${minutes} min`;
        }

        // Update individual equipment end times with complete date/time and remaining time
        result.profiles?.forEach((profile, index) => {
            const endTimeCell = document.getElementById(
                `equipmentEndTime_${index + 1}`
            );
            if (endTimeCell && result.est_endtime) {
                const now = new Date();
                const endTime = result.est_endtime;

                // Format date and time
                const dateOptions = {
                    month: "short",
                    day: "numeric",
                    hour: "2-digit",
                    minute: "2-digit",
                    hour12: false,
                };
                const dateTimeStr = endTime.toLocaleDateString(
                    "en-US",
                    dateOptions
                );

                // Calculate remaining time
                const diffMs = endTime.getTime() - now.getTime();
                let remainingStr = "--";

                if (diffMs > 0) {
                    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
                    const diffMinutes = Math.floor(
                        (diffMs % (1000 * 60 * 60)) / (1000 * 60)
                    );

                    if (diffHours > 0) {
                        remainingStr = `${diffHours}h ${diffMinutes}m`;
                    } else {
                        remainingStr = `${diffMinutes}m`;
                    }
                } else {
                    remainingStr = "Overdue";
                }

                endTimeCell.innerHTML = `
                    <div class="est-end-time">
                        <div class="end-datetime text-success" style="font-size: 12px; font-weight: 600;">${dateTimeStr}</div>
                        <div class="remaining-time text-info" style="font-size: 10px; margin-top: 2px;">${remainingStr}</div>
                    </div>
                `;
            }
        });
    }

    /**
     * Clear calculation results
     */
    function clearCalculationResults() {
        const endTimeElement = document.querySelector(".end-time");
        const timeDiffElement = document.querySelector(".time-diff");

        if (endTimeElement) {
            endTimeElement.textContent = "Enter lot details to calculate";
        }

        if (timeDiffElement) {
            timeDiffElement.textContent = "--";
        }

        // Clear individual equipment end times
        const endTimeCells = document.querySelectorAll(".equipment-endtime");
        endTimeCells.forEach((cell) => {
            cell.innerHTML = '<small class="text-muted">--</small>';
        });

        updateFormSubmitState(false);
    }

    /**
     * Initialize form validation
     */
    function initializeFormValidation() {
        const form = document.getElementById("newLotForm");
        if (form) {
            form.addEventListener("submit", function (e) {
                if (!validateForm()) {
                    e.preventDefault();
                    return false;
                }
            });
        }
    }

    /**
     * Validate form before submission
     */
    function validateForm() {
        let isValid = true;
        const errors = [];

        // Check lot ID
        const lotId = document.getElementById("lot_id")?.value?.trim();
        if (!lotId) {
            errors.push("Lot ID is required");
            isValid = false;
        }

        // Check lot data
        if (!currentLotData || !currentLotData.lot_qty) {
            errors.push("Please lookup lot data first");
            isValid = false;
        }

        // Check equipment assignments
        const assignments = getEquipmentAssignments();
        if (assignments.length === 0) {
            errors.push("At least one equipment assignment is required");
            isValid = false;
        }

        // Validate equipment assignments
        assignments.forEach((assignment, index) => {
            if (!assignment.eqp_no) {
                errors.push(
                    `Equipment ${index + 1}: Equipment number is required`
                );
                isValid = false;
            }
            if (!assignment.start_time) {
                errors.push(`Equipment ${index + 1}: Start time is required`);
                isValid = false;
            }
            if (assignment.daily_capacity <= 0) {
                errors.push(
                    `Equipment ${index + 1}: Invalid equipment capacity`
                );
                isValid = false;
            }
        });

        if (!isValid) {
            showNotification(errors.join("\n"), "error");
        }

        return isValid;
    }

    /**
     * Update form submit button state
     */
    function updateFormSubmitState(enabled) {
        const submitBtn = document.getElementById("submitForecastBtn");
        if (submitBtn) {
            submitBtn.disabled = !enabled;
        }
    }

    /**
     * Show loading state
     */
    function showLoadingState(show) {
        const lotIdInput = document.getElementById("lot_id");
        const searchButton = document.querySelector(".search-btn");

        if (lotIdInput) {
            lotIdInput.disabled = show;
        }

        if (searchButton) {
            if (show) {
                searchButton.innerHTML =
                    '<i class="fas fa-spinner fa-spin"></i>';
                searchButton.disabled = true;
            } else {
                searchButton.innerHTML = '<i class="fas fa-search"></i>';
                searchButton.disabled = false;
            }
        }
    }

    /**
     * Show notification to user
     */
    function showNotification(message, type = "info") {
        // Create or update notification element
        let notification = document.getElementById("endtime-notification");

        if (!notification) {
            notification = document.createElement("div");
            notification.id = "endtime-notification";
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                max-width: 300px;
                padding: 12px 16px;
                border-radius: 4px;
                color: white;
                font-weight: 500;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                opacity: 0;
                transform: translateX(100%);
                transition: all 0.3s ease;
            `;
            document.body.appendChild(notification);
        }

        // Set background color based on type
        const colors = {
            success: "#22c55e",
            error: "#ef4444",
            warning: "#f59e0b",
            info: "#3b82f6",
        };

        notification.style.backgroundColor = colors[type] || colors.info;
        notification.textContent = message;

        // Show notification
        requestAnimationFrame(() => {
            notification.style.opacity = "1";
            notification.style.transform = "translateX(0)";
        });

        // Hide after delay
        setTimeout(
            () => {
                notification.style.opacity = "0";
                notification.style.transform = "translateX(100%)";
            },
            type === "error" ? 5000 : 3000
        );
    }

    // Make forceLotLookup globally available for onclick attribute
    window.forceLotLookup = function () {
        const lotIdInput = document.getElementById("lot_id");
        if (lotIdInput) {
            const lotId = lotIdInput.value.trim().toUpperCase();
            if (lotId) {
                lotIdInput.value = lotId;
                lookupLotData(lotId);
            } else {
                showNotification("Please enter a lot ID", "warning");
            }
        }
    };

    // Test equipment endpoint manually
    window.testEquipmentEndpoint = function () {
        console.log("👨‍🔧 Testing equipment endpoint manually...");
        fetch("/endtime/equipment-data", {
            method: "GET",
            headers: {
                "X-Requested-With": "XMLHttpRequest",
                Accept: "application/json",
                "Content-Type": "application/json",
            },
        })
            .then((response) => {
                console.log("Manual test response status:", response.status);
                return response.json();
            })
            .then((data) => {
                console.log("Manual test response data:", data);
                showNotification(
                    `Equipment endpoint test: ${
                        data.success ? "SUCCESS" : "FAILED"
                    }`,
                    data.success ? "success" : "error"
                );
            })
            .catch((error) => {
                console.error("Manual test error:", error);
                showNotification(
                    "Equipment endpoint test FAILED: " + error.message,
                    "error"
                );
            });
    };
})();
