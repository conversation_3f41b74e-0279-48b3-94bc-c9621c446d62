<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use App\Models\OrderItem;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:ADMIN');
    }

    /**
     * Display analytics dashboard.
     */
    public function index()
    {
        $analytics = [
            'overview' => $this->getOverviewAnalytics(),
            'sales_trends' => $this->getSalesTrends(),
            'customer_analytics' => $this->getCustomerAnalytics(),
            'product_performance' => $this->getProductPerformance(),
            'recent_activities' => $this->getRecentActivities(),
        ];

        return view('management.analytics.index', compact('analytics'));
    }

    /**
     * Get sales analytics data.
     */
    public function sales()
    {
        $salesData = [
            'daily_sales' => $this->getDailySales(),
            'monthly_comparison' => $this->getMonthlyComparison(),
            'top_selling_products' => $this->getTopSellingProducts(),
            'sales_by_category' => $this->getSalesByCategory(),
            'conversion_metrics' => $this->getConversionMetrics(),
        ];

        return view('management.analytics.sales', compact('salesData'));
    }

    /**
     * Get customer analytics data.
     */
    public function customers()
    {
        $customerData = [
            'customer_segments' => $this->getCustomerSegments(),
            'customer_lifetime_value' => $this->getCustomerLifetimeValue(),
            'acquisition_trends' => $this->getAcquisitionTrends(),
            'retention_metrics' => $this->getRetentionMetrics(),
            'top_customers' => $this->getTopCustomers(),
        ];

        return view('management.analytics.customers', compact('customerData'));
    }

    /**
     * Get product analytics data.
     */
    public function products()
    {
        $productData = [
            'inventory_analysis' => $this->getInventoryAnalysis(),
            'product_profitability' => $this->getProductProfitability(),
            'category_performance' => $this->getCategoryPerformance(),
            'stock_alerts' => $this->getStockAlerts(),
            'product_trends' => $this->getProductTrends(),
        ];

        return view('management.analytics.products', compact('productData'));
    }

    /**
     * Get financial analytics data.
     */
    public function financial()
    {
        $financialData = [
            'revenue_analysis' => $this->getRevenueAnalysis(),
            'profit_margins' => $this->getProfitMargins(),
            'financial_trends' => $this->getFinancialTrends(),
            'payment_analytics' => $this->getPaymentAnalytics(),
            'forecasting' => $this->getRevenueForecast(),
        ];

        return view('management.analytics.financial', compact('financialData'));
    }

    /**
     * Get overview analytics.
     */
    private function getOverviewAnalytics()
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();

        return [
            'total_revenue' => Order::sum('total_amount'),
            'total_orders' => Order::count(),
            'total_customers' => User::where('role', 'USER')->count(),
            'avg_order_value' => Order::avg('total_amount'),
            'monthly_revenue' => Order::where('created_at', '>=', $currentMonth)->sum('total_amount'),
            'monthly_orders' => Order::where('created_at', '>=', $currentMonth)->count(),
            'revenue_growth' => $this->calculateGrowthRate('total_amount', $currentMonth, $lastMonth),
            'order_growth' => $this->calculateGrowthRate('id', $currentMonth, $lastMonth, 'count'),
        ];
    }

    /**
     * Get sales trends data.
     */
    private function getSalesTrends()
    {
        $last30Days = Order::where('created_at', '>=', Carbon::now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue, COUNT(*) as orders')
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'daily_trends' => $last30Days,
            'weekly_average' => $last30Days->avg('revenue'),
            'best_day' => $last30Days->sortByDesc('revenue')->first(),
            'peak_hours' => $this->getPeakHours(),
        ];
    }

    /**
     * Get customer analytics.
     */
    private function getCustomerAnalytics()
    {
        return [
            'new_customers_today' => User::where('role', 'USER')
                ->whereDate('created_at', Carbon::today())->count(),
            'returning_customers' => DB::table('orders')
                ->select('user_id')
                ->groupBy('user_id')
                ->havingRaw('COUNT(*) > 1')
                ->count(),
            'customer_geography' => $this->getCustomerGeography(),
            'customer_engagement' => $this->getCustomerEngagement(),
        ];
    }

    /**
     * Get product performance.
     */
    private function getProductPerformance()
    {
        return [
            'best_sellers' => $this->getTopSellingProducts(5),
            'low_stock_products' => Product::where('stock', '<', 10)->count(),
            'out_of_stock' => Product::where('stock', 0)->count(),
            'category_distribution' => Product::selectRaw('category, COUNT(*) as count')
                ->groupBy('category')->get(),
        ];
    }

    /**
     * Get recent activities.
     */
    private function getRecentActivities()
    {
        $recentOrders = Order::with('user')
            ->latest()
            ->take(10)
            ->get();

        $recentUsers = User::where('role', 'user')
            ->latest()
            ->take(5)
            ->get();

        return [
            'recent_orders' => $recentOrders,
            'new_users' => $recentUsers,
        ];
    }

    /**
     * Get daily sales for the last 30 days.
     */
    private function getDailySales()
    {
        return Order::where('created_at', '>=', Carbon::now()->subDays(30))
            ->selectRaw('DATE(created_at) as date, SUM(total_amount) as revenue, COUNT(*) as orders')
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get monthly comparison data.
     */
    private function getMonthlyComparison()
    {
        $driver = config('database.default');
        $dbType = config("database.connections.{$driver}.driver");
        
        if ($dbType === 'mysql') {
            $selectRaw = 'MONTH(created_at) as month, YEAR(created_at) as year, SUM(total_amount) as revenue, COUNT(*) as orders';
        } else {
            // SQLite
            $selectRaw = 'strftime("%m", created_at) as month, strftime("%Y", created_at) as year, SUM(total_amount) as revenue, COUNT(*) as orders';
        }
        
        return Order::selectRaw($selectRaw)
            ->where('created_at', '>=', Carbon::now()->subYear())
            ->groupBy('year', 'month')
            ->orderBy('year')
            ->orderBy('month')
            ->get();
    }

    /**
     * Get top selling products.
     */
    private function getTopSellingProducts($limit = 10)
    {
        return Product::selectRaw('products.*, SUM(order_items.quantity) as total_sold, SUM(order_items.total) as total_revenue')
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->groupBy('products.id')
            ->orderByDesc('total_sold')
            ->take($limit)
            ->get();
    }

    /**
     * Get sales by category.
     */
    private function getSalesByCategory()
    {
        return Product::selectRaw('category, SUM(order_items.total) as total_revenue, SUM(order_items.quantity) as total_sold')
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->groupBy('category')
            ->orderByDesc('total_revenue')
            ->get();
    }

    /**
     * Get conversion metrics.
     */
    private function getConversionMetrics()
    {
        $totalUsers = User::where('role', 'user')->count();
        $usersWithOrders = Order::distinct('user_id')->count();
        
        return [
            'conversion_rate' => $totalUsers > 0 ? ($usersWithOrders / $totalUsers) * 100 : 0,
            'average_orders_per_customer' => $usersWithOrders > 0 ? Order::count() / $usersWithOrders : 0,
            'repeat_purchase_rate' => $this->getRepeatPurchaseRate(),
        ];
    }

    /**
     * Calculate growth rate between two periods.
     */
    private function calculateGrowthRate($field, $currentPeriod, $lastPeriod, $aggregation = 'sum')
    {
        $current = Order::where('created_at', '>=', $currentPeriod);
        $last = Order::where('created_at', '>=', $lastPeriod)
                     ->where('created_at', '<', $currentPeriod);

        if ($aggregation === 'count') {
            $currentValue = $current->count();
            $lastValue = $last->count();
        } else {
            $currentValue = $current->sum($field);
            $lastValue = $last->sum($field);
        }

        if ($lastValue == 0) return 0;
        
        return round((($currentValue - $lastValue) / $lastValue) * 100, 2);
    }

    /**
     * Get peak hours for orders.
     */
    private function getPeakHours()
    {
        $driver = config('database.default');
        $dbType = config("database.connections.{$driver}.driver");
        
        if ($dbType === 'mysql') {
            $selectRaw = 'HOUR(created_at) as hour, COUNT(*) as order_count';
        } else {
            // SQLite
            $selectRaw = 'strftime("%H", created_at) as hour, COUNT(*) as order_count';
        }
        
        return Order::selectRaw($selectRaw)
            ->groupBy('hour')
            ->orderByDesc('order_count')
            ->take(5)
            ->get();
    }

    /**
     * Get customer segments.
     */
    private function getCustomerSegments()
    {
        return User::selectRaw('
            CASE 
                WHEN order_totals.total_spent >= 1000 THEN "VIP"
                WHEN order_totals.total_spent >= 500 THEN "Premium"
                WHEN order_totals.total_spent >= 100 THEN "Regular"
                ELSE "New"
            END as segment,
            COUNT(*) as count
        ')
        ->leftJoinSub(
            Order::selectRaw('user_id, SUM(total_amount) as total_spent')
                ->groupBy('user_id'),
            'order_totals',
            'users.id',
            '=',
            'order_totals.user_id'
        )
        ->where('role', 'user')
        ->groupBy('segment')
        ->get();
    }

    /**
     * Get customer lifetime value.
     */
    private function getCustomerLifetimeValue()
    {
        return User::selectRaw('users.id, users.emp_name, users.emp_no, COALESCE(SUM(orders.total_amount), 0) as lifetime_value, COUNT(orders.id) as total_orders')
            ->leftJoin('orders', 'users.id', '=', 'orders.user_id')
            ->where('users.role', 'USER')
            ->groupBy('users.id', 'users.emp_name', 'users.emp_no')
            ->orderByDesc('lifetime_value')
            ->take(20)
            ->get();
    }

    /**
     * Get customer acquisition trends.
     */
    private function getAcquisitionTrends()
    {
        return User::selectRaw('DATE(created_at) as date, COUNT(*) as new_customers')
            ->where('role', 'user')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }

    /**
     * Get retention metrics.
     */
    private function getRetentionMetrics()
    {
        $totalCustomers = User::where('role', 'user')->count();
        $activeCustomers = Order::where('created_at', '>=', Carbon::now()->subDays(30))
            ->distinct('user_id')
            ->count();

        return [
            'retention_rate' => $totalCustomers > 0 ? ($activeCustomers / $totalCustomers) * 100 : 0,
            'active_customers' => $activeCustomers,
            'inactive_customers' => $totalCustomers - $activeCustomers,
        ];
    }

    /**
     * Get top customers by spending.
     */
    private function getTopCustomers()
    {
        return User::selectRaw('users.*, SUM(orders.total_amount) as total_spent, COUNT(orders.id) as total_orders')
            ->join('orders', 'users.id', '=', 'orders.user_id')
            ->where('users.role', 'user')
            ->groupBy('users.id')
            ->orderByDesc('total_spent')
            ->take(10)
            ->get();
    }

    /**
     * Get repeat purchase rate.
     */
    private function getRepeatPurchaseRate()
    {
        $totalCustomers = User::where('role', 'user')->count();
        $repeatCustomers = DB::table('orders')
            ->select('user_id')
            ->groupBy('user_id')
            ->havingRaw('COUNT(*) > 1')
            ->count();

        return $totalCustomers > 0 ? ($repeatCustomers / $totalCustomers) * 100 : 0;
    }

    /**
     * Get inventory analysis.
     */
    private function getInventoryAnalysis()
    {
        return [
            'total_products' => Product::count(),
            'low_stock' => Product::where('stock', '<', 10)->count(),
            'out_of_stock' => Product::where('stock', 0)->count(),
            'overstocked' => Product::where('stock', '>', 100)->count(),
            'average_stock_value' => Product::selectRaw('AVG(price * stock) as avg_value')->first()->avg_value ?? 0,
        ];
    }

    /**
     * Get product profitability (simplified - assumes 30% cost margin).
     */
    private function getProductProfitability()
    {
        return Product::selectRaw('
            products.*,
            SUM(order_items.quantity) as units_sold,
            SUM(order_items.total) as total_revenue,
            SUM(order_items.total) * 0.3 as estimated_profit
        ')
        ->leftJoin('order_items', 'products.id', '=', 'order_items.product_id')
        ->groupBy('products.id')
        ->orderByDesc('estimated_profit')
        ->take(20)
        ->get();
    }

    /**
     * Get category performance.
     */
    private function getCategoryPerformance()
    {
        return Product::selectRaw('
            category,
            COUNT(*) as product_count,
            SUM(stock) as total_stock,
            AVG(products.price) as avg_price,
            COALESCE(SUM(order_items.quantity), 0) as total_sold,
            COALESCE(SUM(order_items.total), 0) as total_revenue
        ')
        ->leftJoin('order_items', 'products.id', '=', 'order_items.product_id')
        ->groupBy('category')
        ->orderByDesc('total_revenue')
        ->get();
    }

    /**
     * Get stock alerts.
     */
    private function getStockAlerts()
    {
        return [
            'critical' => Product::where('stock', '=', 0)->get(),
            'low' => Product::whereBetween('stock', [1, 5])->get(),
            'warning' => Product::whereBetween('stock', [6, 10])->get(),
        ];
    }

    /**
     * Get product trends.
     */
    private function getProductTrends()
    {
        return Product::selectRaw('
            DATE(products.created_at) as date,
            COUNT(*) as products_added
        ')
        ->where('created_at', '>=', Carbon::now()->subDays(30))
        ->groupBy('date')
        ->orderBy('date')
        ->get();
    }

    /**
     * Placeholder methods for more complex analytics.
     */
    private function getCustomerGeography()
    {
        // This would require additional user location data
        return collect([]);
    }

    private function getCustomerEngagement()
    {
        // This would require additional engagement tracking
        return [];
    }

    private function getRevenueAnalysis()
    {
        return [
            'current_month' => Order::whereMonth('created_at', Carbon::now()->month)->sum('total_amount'),
            'last_month' => Order::whereMonth('created_at', Carbon::now()->subMonth()->month)->sum('total_amount'),
            'year_to_date' => Order::whereYear('created_at', Carbon::now()->year)->sum('total_amount'),
            'last_year' => Order::whereYear('created_at', Carbon::now()->subYear()->year)->sum('total_amount'),
        ];
    }

    private function getProfitMargins()
    {
        // Simplified profit calculation
        $totalRevenue = Order::sum('total_amount');
        $estimatedCost = $totalRevenue * 0.7; // Assuming 70% cost ratio
        
        return [
            'total_revenue' => $totalRevenue,
            'estimated_cost' => $estimatedCost,
            'estimated_profit' => $totalRevenue - $estimatedCost,
            'profit_margin' => $totalRevenue > 0 ? (($totalRevenue - $estimatedCost) / $totalRevenue) * 100 : 0,
        ];
    }

    private function getFinancialTrends()
    {
        $driver = config('database.default');
        $dbType = config("database.connections.{$driver}.driver");
        
        if ($dbType === 'mysql') {
            $selectRaw = '
                MONTH(created_at) as month,
                YEAR(created_at) as year,
                SUM(total_amount) as revenue,
                COUNT(*) as orders,
                AVG(total_amount) as avg_order_value
            ';
        } else {
            // SQLite
            $selectRaw = '
                strftime("%m", created_at) as month,
                strftime("%Y", created_at) as year,
                SUM(total_amount) as revenue,
                COUNT(*) as orders,
                AVG(total_amount) as avg_order_value
            ';
        }
        
        return Order::selectRaw($selectRaw)
        ->where('created_at', '>=', Carbon::now()->subMonths(12))
        ->groupBy('year', 'month')
        ->orderBy('year')
        ->orderBy('month')
        ->get();
    }

    private function getPaymentAnalytics()
    {
        // This would require payment method tracking
        return [
            'payment_methods' => collect([]),
            'payment_success_rate' => 100, // Placeholder
            'average_processing_time' => '2.3 seconds', // Placeholder
        ];
    }

    private function getRevenueForecast()
    {
        // Simple linear projection based on last 3 months
        $recentMonths = Order::selectRaw('SUM(total_amount) as revenue')
            ->where('created_at', '>=', Carbon::now()->subMonths(3))
            ->first();

        $monthlyAverage = ($recentMonths->revenue ?? 0) / 3;
        
        return [
            'next_month_forecast' => $monthlyAverage,
            'quarterly_forecast' => $monthlyAverage * 3,
            'yearly_forecast' => $monthlyAverage * 12,
        ];
    }
}