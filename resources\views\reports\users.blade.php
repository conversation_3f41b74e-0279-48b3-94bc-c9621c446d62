<x-app-layout>
    <x-slot name="header">
        User Analytics
    </x-slot>

    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">User Analytics & Insights</h4>
                    <p class="text-muted mb-0">Monitor user activity and customer behavior</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('reports.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Reports
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Analysis Period</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('reports.users') }}" class="row g-3">
                        <div class="col-md-4">
                            <label for="start_date" class="form-label">Start Date</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" 
                                   value="{{ request('start_date', now()->startOfMonth()->format('Y-m-d')) }}">
                        </div>
                        <div class="col-md-4">
                            <label for="end_date" class="form-label">End Date</label>
                            <input type="date" class="form-control" id="end_date" name="end_date" 
                                   value="{{ request('end_date', now()->endOfMonth()->format('Y-m-d')) }}">
                        </div>
                        <div class="col-md-4 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>Apply Date Range
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- User Statistics -->
    <div class="row g-4 mb-4">
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                    <h4 class="text-primary mb-1">{{ number_format($userStats['total_users']) }}</h4>
                    <small class="text-muted">Total Users</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-user-plus fa-2x"></i>
                    </div>
                    <h4 class="text-success mb-1">{{ number_format($userStats['new_users']) }}</h4>
                    <small class="text-muted">New Users</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-shopping-bag fa-2x"></i>
                    </div>
                    <h4 class="text-info mb-1">{{ number_format($userStats['active_users']) }}</h4>
                    <small class="text-muted">Active Users</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-user-shield fa-2x"></i>
                    </div>
                    <h4 class="text-warning mb-1">{{ number_format($userStats['admin_users']) }}</h4>
                    <small class="text-muted">Admin Users</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-secondary mb-2">
                        <i class="fas fa-user fa-2x"></i>
                    </div>
                    <h4 class="text-secondary mb-1">{{ number_format($userStats['regular_users']) }}</h4>
                    <small class="text-muted">Regular Users</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-dark mb-2">
                        <i class="fas fa-percentage fa-2x"></i>
                    </div>
                    <h4 class="text-dark mb-1">{{ $userStats['total_users'] > 0 ? number_format(($userStats['active_users'] / $userStats['total_users']) * 100, 1) : 0 }}%</h4>
                    <small class="text-muted">Active Rate</small>
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Registration Trend Chart -->
        <div class="col-xl-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">User Registration Trend (Last 30 Days)</h6>
                </div>
                <div class="card-body">
                    <div style="height: 400px;">
                        <canvas id="registrationChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Customers -->
        <div class="col-xl-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Top Customers</h6>
                </div>
                <div class="card-body">
                    @forelse($topCustomers as $customer)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <div class="fw-bold">{{ $customer->name }}</div>
                                <small class="text-muted">{{ $customer->email }}</small>
                            </div>
                            <div class="text-end">
                                <div class="text-success fw-bold">${{ number_format($customer->total_spent, 2) }}</div>
                                <small class="text-muted">{{ $customer->orders_count }} orders</small>
                            </div>
                        </div>
                        @if(!$loop->last)
                            <hr class="my-2">
                        @endif
                    @empty
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-users fa-2x mb-3"></i>
                            <p>No customer data available for the selected period.</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>

    <!-- User Activity Details -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Registration Details (Last 30 Days)</h6>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Date</th>
                                    <th>New Registrations</th>
                                    <th>Cumulative Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $cumulativeTotal = $userStats['total_users'] - $registrationTrend->sum('registrations');
                                @endphp
                                @forelse($registrationTrend as $trend)
                                    @php
                                        $cumulativeTotal += $trend->registrations;
                                    @endphp
                                    <tr>
                                        <td>{{ \Carbon\Carbon::parse($trend->date)->format('M d, Y') }}</td>
                                        <td>
                                            <span class="badge bg-success">{{ $trend->registrations }}</span>
                                        </td>
                                        <td class="text-primary fw-bold">{{ number_format($cumulativeTotal) }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="3" class="text-center text-muted py-4">
                                            No registration data found for the last 30 days.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Engagement Insights -->
    <div class="row mt-4">
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">User Engagement</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <h4 class="text-success">{{ $userStats['active_users'] }}</h4>
                                <small class="text-muted">Active Users</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-muted">{{ $userStats['total_users'] - $userStats['active_users'] }}</h4>
                            <small class="text-muted">Inactive Users</small>
                        </div>
                    </div>
                    
                    @if($userStats['total_users'] > 0)
                        <div class="progress mb-3" style="height: 10px;">
                            <div class="progress-bar bg-success" role="progressbar" 
                                 style="width: {{ ($userStats['active_users'] / $userStats['total_users']) * 100 }}%">
                            </div>
                        </div>
                        <p class="text-center text-muted mb-0">
                            <strong>{{ number_format(($userStats['active_users'] / $userStats['total_users']) * 100, 1) }}%</strong> 
                            of users have placed orders
                        </p>
                    @endif
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">User Roles Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 mb-3">
                            <div class="border-end">
                                <h4 class="text-warning">{{ $userStats['admin_users'] }}</h4>
                                <small class="text-muted">Administrators</small>
                            </div>
                        </div>
                        <div class="col-6 mb-3">
                            <h4 class="text-info">{{ $userStats['regular_users'] }}</h4>
                            <small class="text-muted">Regular Users</small>
                        </div>
                    </div>
                    
                    @if($userStats['total_users'] > 0)
                        <div class="progress mb-3" style="height: 10px;">
                            <div class="progress-bar bg-warning" role="progressbar" 
                                 style="width: {{ ($userStats['admin_users'] / $userStats['total_users']) * 100 }}%">
                            </div>
                            <div class="progress-bar bg-info" role="progressbar" 
                                 style="width: {{ ($userStats['regular_users'] / $userStats['total_users']) * 100 }}%">
                            </div>
                        </div>
                        <p class="text-center text-muted mb-0">
                            <strong>{{ number_format(($userStats['admin_users'] / $userStats['total_users']) * 100, 1) }}%</strong> 
                            are administrators
                        </p>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Registration Trend Chart
            const ctx = document.getElementById('registrationChart');
            if (ctx) {
                const registrationData = @json($registrationTrend);
                
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: registrationData.map(item => {
                            const date = new Date(item.date);
                            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
                        }),
                        datasets: [{
                            label: 'New Registrations',
                            data: registrationData.map(item => parseInt(item.registrations)),
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    stepSize: 1
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</x-app-layout>