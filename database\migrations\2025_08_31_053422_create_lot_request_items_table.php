<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lot_request_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('lot_request_id')->constrained()->onDelete('cascade');
            $table->string('equipment_number');
            $table->string('equipment_code');
            $table->integer('quantity'); // quantity in lots
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lot_request_items');
    }
};
