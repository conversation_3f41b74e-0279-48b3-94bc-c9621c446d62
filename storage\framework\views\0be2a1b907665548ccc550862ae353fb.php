<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex align-items-center justify-content-between w-100">
            
            <!-- Header Controls -->
            <div class="d-flex align-items-center gap-2 flex-shrink-0">
                
                <!-- Group 1: Time Filters -->
                <div class="header-control-group d-flex align-items-center gap-1">
                    <!-- Production Date -->
                    <div class="d-flex align-items-center gap-1">
                        <label class="form-label mb-0 small ">Date:</label>
                        <input type="date" id="dashboard_date" class="form-control form-control-sm header-control" 
                            style="width: 130px;" 
                            value="<?php echo e(request('dashboard_date', date('Y-m-d'))); ?>" 
                            onchange="updateDashboardData()">
                    </div>
                    
                    <!-- Shift Selector -->
                    <div class="d-flex align-items-center gap-1">
                        <label class="form-label mb-0 small ">Shift:</label>
                        <select id="dashboard_shift" class="form-select form-select-sm header-control" style="width: 110px;" onchange="handleShiftChange()">
                            <option value="all" <?php echo e(request('dashboard_shift', 'all') === 'all' ? 'selected' : ''); ?>>All</option>
                            <option value="day" <?php echo e(request('dashboard_shift', 'all') === 'day' ? 'selected' : ''); ?>>Day</option>
                            <option value="night" <?php echo e(request('dashboard_shift', 'all') === 'night' ? 'selected' : ''); ?>>Night</option>
                        </select>
                    </div>
                    
                    <!-- Cutoff Period -->
                    <div class="d-flex align-items-center gap-1">
                        <label class="form-label mb-0 small ">Cutoff:</label>
                        <select id="dashboard_cutoff" class="form-select form-select-sm header-control" style="width: 100px;" onchange="updateDashboardData()">
                            <option value="1" <?php echo e(request('dashboard_cutoff', '1') === '1' ? 'selected' : ''); ?>>1st</option>
                            <option value="2" <?php echo e(request('dashboard_cutoff', '1') === '2' ? 'selected' : ''); ?>>2nd</option>
                            <option value="3" <?php echo e(request('dashboard_cutoff', '1') === '3' ? 'selected' : ''); ?>>3rd</option>
                            <option value="all" <?php echo e(request('dashboard_cutoff', '1') === 'all' ? 'selected' : ''); ?>>All</option>
                        </select>
                    </div>
                </div>
                
                <!-- Separator -->
                <div class="header-separator"></div>
                
                <!-- Group 2: Data Filters -->
                <div class="header-control-group d-flex align-items-center gap-1">
                    <!-- Work Type Filter -->
                    <div class="d-flex align-items-center gap-1">
                        <label class="form-label mb-0 small ">Type:</label>
                        <select id="dashboard_work_type" class="form-select form-select-sm header-control" style="width: 80px;" onchange="updateDashboardData()">
                            <option value="all" <?php echo e(request('dashboard_work_type', 'all') === 'all' ? 'selected' : ''); ?>>All</option>
                            <option value="NOR" <?php echo e(request('dashboard_work_type', 'all') === 'NOR' ? 'selected' : ''); ?>>NOR</option>
                            <option value="OI" <?php echo e(request('dashboard_work_type', 'all') === 'OI' ? 'selected' : ''); ?>>OI</option>
                            <option value="ADV" <?php echo e(request('dashboard_work_type', 'all') === 'ADV' ? 'selected' : ''); ?>>ADV</option>
                            <option value="COMB" <?php echo e(request('dashboard_work_type', 'all') === 'COMB' ? 'selected' : ''); ?>>COMB</option>
                            <option value="LY" <?php echo e(request('dashboard_work_type', 'all') === 'LY' ? 'selected' : ''); ?>>LY</option>
                            <option value="RL" <?php echo e(request('dashboard_work_type', 'all') === 'RL' ? 'selected' : ''); ?>>RL</option>
                            <option value="PR" <?php echo e(request('dashboard_work_type', 'all') === 'PR' ? 'selected' : ''); ?>>PR</option>
                            <option value="FSTOP" <?php echo e(request('dashboard_work_type', 'all') === 'FSTOP' ? 'selected' : ''); ?>>FSTOP</option>
                            <option value="WH" <?php echo e(request('dashboard_work_type', 'all') === 'WH' ? 'selected' : ''); ?>>WH</option>
                        </select>
                    </div>
                </div>
                
                <!-- Separator -->
                <div class="header-separator"></div>
                
                <!-- Group 3: Action Buttons -->
                <div class="header-control-group d-flex gap-1">
                    <button type="button" class="btn btn-light btn-sm px-2" onclick="updateDashboardData()" title="Update Dashboard">
                        <i class="fas fa-sync"></i>
                    </button>
                    <button type="button" class="btn btn-outline-light btn-sm px-2" onclick="resetDashboardFilters()" title="Reset Filters">
                        <i class="fas fa-undo"></i>
                    </button>
                </div>
                <!-- Group 4: Auto Refresh Control -->
                <div class="header-control-group">
                    <div class="auto-refresh-toggle d-flex align-items-center">
                        <label class="form-label mb-0 small ">Auto Refresh:</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" role="switch" id="autoRefreshToggle" onchange="toggleAutoRefresh()" 
                                   style="background-color: var(--bs-gray-400); border-color: var(--bs-gray-400);">
                            <style>
                                .form-check-input:checked {
                                    background-color: var(--bs-success) !important;
                                    border-color: var(--bs-success) !important;
                                }
                            </style>
                        </div>
                        <input type="number" id="refreshInterval" class="form-control header-control" 
                               value="30" min="5" max="300" title="Refresh interval in seconds" 
                               onchange="handleIntervalChange()" oninput="handleIntervalChange()">
                        <span class="small">sec</span>
                        <span class="badge bg-success-transparent small" id="refreshStatus" style="display: none;">
                            <i class="fas fa-sync fa-spin me-1"></i>Active
                        </span>
                    </div>
                </div>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <!-- Main Application CSS (includes all styles) -->
    <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css']); ?>

    <!-- Start::app-content -->

        <!-- Start:: row-1 -->
        <div class="row">
            <div class="col-xxl-9">
                <div class="row">
                    <!-- Card 1: Target Capacity -->
                    <div class="col-xl-3">
                        <div class="card custom-card dashboard-main-card warning">
                            <div class="card-body">
                                <div class="d-flex align-items-start gap-3">
                                    <div class="lh-1">
                                        <span class="avatar avatar-md bg-warning-transparent">
                                            <i class="fas fa-bullseye text-warning fs-18"></i>
                                        </span>
                                    </div>
                                    <div class="flex-fill">
                                        <span class="d-block text-muted mb-1 small">Target Capacity</span>
                                        <h5 class="fw-semibold mb-1" id="targetCapacity"><?php echo e(number_format($dashboardStats['target_capacity'] ?? 0)); ?> PCS</h5>
                                        <div class="d-flex align-items-center gap-2">
                                            <!-- <small class="text-muted">Based on active equipment</small> -->
                                            <span class="badge bg-warning-transparent small" id="targetBadge"><?php echo e($dashboardStats['equipment_count'] ?? 0); ?> EQP Count</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Card 2: Total Endtime (Ongoing + Submitted) -->
                    <div class="col-xl-3">
                        <a href="<?php echo e(route('endtime.index')); ?>" class="text-decoration-none">
                            <div class="card custom-card dashboard-main-card primary clickable-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-start gap-3">
                                        <div class="lh-1">
                                            <span class="avatar avatar-md bg-primary-transparent">
                                                <i class="fas fa-clock text-primary fs-18"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill">
                                            <span class="d-block text-muted mb-1 small">Total Endtime</span>
                                            <h5 class="fw-semibold mb-1" id="totalEndtime"><?php echo e(number_format($dashboardStats['total_quantity'] ?? 0)); ?> PCS</h5>
                                            <div class="d-flex align-items-center gap-2">
                                                <!-- <small class="text-muted">Ongoing + Submitted</small> -->
                                                <span class="badge bg-primary-transparent small" id="totalPcs"><?php echo e($dashboardStats['total_lots'] ?? 0); ?> LOTS</span>
                                            </div>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-external-link-alt text-primary opacity-50" style="font-size: 0.75rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <!-- Card 3: Submitted Lots -->
                    <div class="col-xl-3">
                        <a href="<?php echo e(route('endtime.index', ['status' => 'Submitted'])); ?>" class="text-decoration-none">
                            <div class="card custom-card dashboard-main-card success clickable-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-start gap-3">
                                        <div class="lh-1">
                                            <span class="avatar avatar-md bg-success-transparent">
                                                <i class="fas fa-check-circle text-success fs-18"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill">
                                            <span class="d-block text-muted mb-1 small">Submitted Lots</span>
                                            <h5 class="fw-semibold mb-1" id="submittedLots"><?php echo e(number_format($dashboardStats['submitted_quantity'] ?? 0)); ?> PCS</h5>
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="badge bg-success-transparent small" id="submittedPcs"><?php echo e($dashboardStats['submitted_lots'] ?? 0); ?> LOTS</span>
                                                <small class="text-success"><?php echo e(number_format($dashboardStats['submitted_percentage'] ?? 0, 1)); ?>%</small>
                                            </div>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-external-link-alt text-success opacity-50" style="font-size: 0.75rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    
                    <!-- Card 4: Ongoing Lots -->
                    <div class="col-xl-3">
                        <a href="<?php echo e(route('endtime.index', ['status' => 'Ongoing'])); ?>" class="text-decoration-none">
                            <div class="card custom-card dashboard-main-card secondary clickable-card">
                                <div class="card-body">
                                    <div class="d-flex align-items-start gap-3">
                                        <div class="lh-1">
                                            <span class="avatar avatar-md bg-secondary-transparent">
                                                <i class="fas fa-hourglass-half text-secondary fs-18"></i>
                                            </span>
                                        </div>
                                        <div class="flex-fill">
                                            <span class="d-block text-muted mb-1 small">Remaining Lots</span>
                                            <h5 class="fw-semibold mb-1" id="ongoingLots"><?php echo e(number_format($dashboardStats['ongoing_quantity'] ?? 0)); ?> PCS</h5>
                                            <div class="d-flex align-items-center gap-2">
                                                <span class="badge bg-secondary-transparent small" id="ongoingPcs"><?php echo e($dashboardStats['ongoing_lots'] ?? 0); ?> LOTS</span>
                                                <small class="text-secondary"><?php echo e(number_format($dashboardStats['ongoing_percentage'] ?? 0, 1)); ?>%</small>
                                            </div>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fas fa-external-link-alt text-secondary opacity-50" style="font-size: 0.75rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </a>
                    </div>
                    <div class="col-xl-12">
                        <div class="card custom-card">
                            <div class="card-header">
                            <div class="card-title">
                                Production Overview
                            </div>
                            </div>
                            <div class="card-body">
                                <div id="projects-overview"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xxl-3">
                <!-- Equipment Status -->
                <div class="card custom-card">
                    <div class="card-header">
                        <div class="card-title d-flex align-items-center">
                            <i class="fas fa-cogs me-2"></i>
                            <span>Equipment Status</span>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <!-- Gradient Radial Chart -->
                        <div class="d-flex justify-content-center mb-4">
                            <div id="equipment-status-chart"></div>
                        </div>

                        <!-- Equipment Status Info -->
                        <div>
                            <!-- Total Equipment -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="d-flex align-items-center">
                                        <span class="avatar avatar-sm bg-warning-transparent me-2">
                                            <i class="fas fa-cogs text-warning fs-12"></i>
                                        </span>
                                        <span class="text-muted fw-medium">Total Equipment</span>
                                    </div>
                                    <span class="text-warning fw-bold" id="totalEquipment"><?php echo e($dashboardStats['total_equipment'] ?? 0); ?></span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-warning" style="width: 100%;"></div>
                                </div>
                            </div>

                            <!-- Equipment with Ongoing -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="d-flex align-items-center">
                                        <span class="avatar avatar-sm me-2" style="background-color: rgba(50, 212, 132, 0.1);">
                                            <i class="fas fa-play-circle fs-12" style="color: rgba(50, 212, 132, 1);"></i>
                                        </span>
                                        <span class="text-muted fw-medium">With Ongoing Lots</span>
                                    </div>
                                    <span class="fw-bold" style="color: rgba(50, 212, 132, 1);" id="equipmentWithOngoing"><?php echo e($dashboardStats['equipment_with_ongoing'] ?? 0); ?></span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar equipment-ongoing-progress" style="background-color: rgba(50, 212, 132, 1); width: <?php echo e(($dashboardStats['total_equipment'] ?? 0) > 0 ? (($dashboardStats['equipment_with_ongoing'] ?? 0) / ($dashboardStats['total_equipment'] ?? 1)) * 100 : 0); ?>%;"></div>
                                </div>
                            </div>

                            <!-- Idle Equipment -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <div class="d-flex align-items-center">
                                        <span class="avatar avatar-sm bg-secondary-transparent me-2">
                                            <i class="fas fa-pause-circle text-secondary fs-12"></i>
                                        </span>
                                        <span class="text-muted fw-medium">Idle Equipment</span>
                                    </div>
                                    <span class="text-secondary fw-bold" id="idleEquipment"><?php echo e(($dashboardStats['total_equipment'] ?? 0) - ($dashboardStats['equipment_with_ongoing'] ?? 0)); ?></span>
                                </div>
                                <div class="progress" style="height: 8px;">
                                    <div class="progress-bar bg-secondary equipment-idle-progress" style="width: <?php echo e(($dashboardStats['total_equipment'] ?? 0) > 0 ? ((($dashboardStats['total_equipment'] ?? 0) - ($dashboardStats['equipment_with_ongoing'] ?? 0)) / ($dashboardStats['total_equipment'] ?? 1)) * 100 : 0); ?>%;"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End:: row-1 -->

        <!-- Start:: row-2 -->
        <div class="row">
            <div class="col-xl-8">
                <div class="card custom-card">
                    <div class="card-header">
                        <div class="card-title d-flex align-items-center">
                            <i class="fas fa-stream me-2 text-primary"></i>
                            <span>Per Line Summary</span>
                            <span class="badge bg-primary-transparent ms-2" style="font-size: 11px;">Production Lines</span>
                        </div>
                        <div class="text-muted mt-2" style="font-size: 13px;">Target vs Performance by Production Line (A-K)</div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Summary Overview Legend -->
                        <div class="px-3 py-2 bg-light border-bottom">
                            <div class="d-flex align-items-center justify-content-between">
                                <span class="fw-medium text-dark" style="font-size: 13px;">Summary Overview</span>
                                <div class="d-flex align-items-center gap-3" style="font-size: 12px;">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-check-circle text-success me-1" style="font-size: 10px;"></i>
                                        <span class="text-success fw-medium">Target</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-bolt text-primary me-1" style="font-size: 10px;"></i>
                                        <span class="text-primary fw-medium">Endtime</span>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-paper-plane text-info me-1" style="font-size: 10px;"></i>
                                        <span class="text-info fw-medium">Submitted</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-hover mb-0 text-nowrap table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-bottom-0 ps-3">Metric</th>
                                        <th class="border-bottom-0 text-center">Line A</th>
                                        <th class="border-bottom-0 text-center">Line B</th>
                                        <th class="border-bottom-0 text-center">Line C</th>
                                        <th class="border-bottom-0 text-center">Line D</th>
                                        <th class="border-bottom-0 text-center">Line E</th>
                                        <th class="border-bottom-0 text-center">Line F</th>
                                        <th class="border-bottom-0 text-center">Line G</th>
                                        <th class="border-bottom-0 text-center">Line H</th>
                                        <th class="border-bottom-0 text-center">Line I</th>
                                        <th class="border-bottom-0 text-center">Line J</th>
                                        <th class="border-bottom-0 text-center pe-3">Line K</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="ps-3 fw-medium text-muted py-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-check-circle text-success me-2" style="font-size: 12px;"></i>
                                                <span>Target</span>
                                            </div>
                                        </td>
                                        <?php $__currentLoopData = $perLineSummary['lines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?>"><?php echo e($perLineSummary['target'][$line] ?? '0 M'); ?></td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                    <tr>
                                        <td class="ps-3 fw-medium text-muted py-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-bolt text-primary me-2" style="font-size: 12px;"></i>
                                                <span>ENDTIME</span>
                                            </div>
                                        </td>
                                        <?php $__currentLoopData = $perLineSummary['lines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?>"><?php echo e($perLineSummary['endtime'][$line] ?? '0 M'); ?></td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                    <tr>
                                        <td class="ps-3 fw-medium text-muted py-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-paper-plane text-info me-2" style="font-size: 12px;"></i>
                                                <span>SUBMTD</span>
                                            </div>
                                        </td>
                                        <?php $__currentLoopData = $perLineSummary['lines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?>"><?php echo e($perLineSummary['submitted'][$line] ?? '0 M'); ?></td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                    <tr>
                                        <td class="ps-3 fw-medium text-muted py-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-percentage text-warning me-2" style="font-size: 10px;"></i>
                                                <span>SUBMTD %</span>
                                            </div>
                                        </td>
                                        <?php $__currentLoopData = $perLineSummary['lines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $percent = $perLineSummary['submitted_percent'][$line] ?? 0;
                                                $badgeColor = $percent >= 90 ? 'success' : ($percent >= 70 ? 'warning' : 'danger');
                                                $colors = ['primary', 'success', 'info', 'warning', 'secondary', 'danger'];
                                                $colorIndex = $loop->index % count($colors);
                                                $color = $colors[$colorIndex];
                                            ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?>">
                                                <span class="badge bg-<?php echo e($color); ?>-transparent"><?php echo e(number_format($percent, 1)); ?>%</span>
                                            </td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                    <tr>
                                        <td class="ps-3 fw-medium text-muted py-2">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-chart-line text-secondary me-2" style="font-size: 10px;"></i>
                                                <span>ENDTIME %</span>
                                            </div>
                                        </td>
                                        <?php $__currentLoopData = $perLineSummary['lines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $percent = $perLineSummary['endtime_percent'][$line] ?? 0;
                                                $colors = ['primary', 'success', 'info', 'warning', 'secondary', 'danger'];
                                                $colorIndex = $loop->index % count($colors);
                                                $color = $colors[$colorIndex];
                                            ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?>">
                                                <span class="badge bg-<?php echo e($color); ?>"><?php echo e(number_format($percent, 1)); ?>%</span>
                                            </td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-xl-4">
                <div class="card custom-card">
                    <div class="card-header">
                        <div class="card-title d-flex align-items-center">
                            <i class="fas fa-ruler-combined me-2 text-info"></i>
                            <span >Per Size Summary</span>
                            <span class="badge bg-info-transparent ms-2" style="font-size: 11px;">Component Sizes</span>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <!-- Size Analysis Legend -->
                        <div class="px-3 py-2 bg-light border-bottom">
                            <div class="d-flex align-items-center justify-content-between">
                                <span class="fw-medium text-dark" style="font-size: 13px;">Size Analysis</span>
                                <div class="text-muted" style="font-size: 11px;">0603-3225</div>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover mb-0 text-nowrap table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th class="border-bottom-0 text-center">0603</th>
                                        <th class="border-bottom-0 text-center">1005</th>
                                        <th class="border-bottom-0 text-center">1608</th>
                                        <th class="border-bottom-0 text-center">2012</th>
                                        <th class="border-bottom-0 text-center">3216</th>
                                        <th class="border-bottom-0 text-center pe-3">3225</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <?php $__currentLoopData = $perSizeSummary['sizes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?> py-2"><?php echo e($perSizeSummary['target'][$size] ?? '0 M'); ?></td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                    <tr>
                                        <?php $__currentLoopData = $perSizeSummary['sizes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?> py-2"><?php echo e($perSizeSummary['endtime'][$size] ?? '0 M'); ?></td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                    <tr>
                                        <?php $__currentLoopData = $perSizeSummary['sizes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?> py-2"><?php echo e($perSizeSummary['submitted'][$size] ?? '0 M'); ?></td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                    <tr>
                                        <?php $__currentLoopData = $perSizeSummary['sizes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $percent = $perSizeSummary['submitted_percent'][$size] ?? 0;
                                                $colors = ['primary', 'success', 'info', 'warning', 'secondary', 'danger'];
                                                $colorIndex = $loop->index % count($colors);
                                                $color = $colors[$colorIndex];
                                            ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?> py-2">
                                                <span class="badge bg-<?php echo e($color); ?>-transparent"><?php echo e(number_format($percent, 1)); ?>%</span>
                                            </td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                    <tr>
                                        <?php $__currentLoopData = $perSizeSummary['sizes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                                $percent = $perSizeSummary['endtime_percent'][$size] ?? 0;
                                                $colors = ['primary', 'success', 'info', 'warning', 'secondary', 'danger'];
                                                $colorIndex = $loop->index % count($colors);
                                                $color = $colors[$colorIndex];
                                            ?>
                                            <td class="text-center<?php echo e($loop->last ? ' pe-3' : ''); ?> py-2">
                                                <span class="badge bg-<?php echo e($color); ?>"><?php echo e(number_format($percent, 1)); ?>%</span>
                                            </td>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End:: row-2 -->

        <!-- Begin:: row-3 (Production Analysis & Performance Insights) -->
        <div class="row mb-4" id="production-insights-row">
            <!-- Previous Night Shift Summary (Sept. 09, 2025 19:00 ~ Sept. 10, 2025 06:59) -->
            <div class="col-xl-4">
                <div class="card custom-card h-100">
                    <div class="card-header pb-3">
                        <div class="card-title d-flex align-items-center justify-content-between mb-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chart-bar me-2 text-primary"></i>
                                <span class="bg-primary-transparent" id="previous-achievement-title"><?php echo e($previousShiftAchievement['title'] ?? 'Current Period Achievement'); ?></span>
                            </div>
                        </div>
                        <small class="text-muted" id="previous-achievement-subtitle"><?php echo e($previousShiftAchievement['subtitle'] ?? 'Previous period results'); ?></small>
                    </div>
                    <div class="card-body pt-0">
                        <!-- Overall Achievement Circle -->
                        <div class="text-center mb-4">
                            <div class="position-relative d-inline-block">
                                <div class="circular-progress" data-percentage="<?php echo e($previousShiftAchievement['achievement_percent'] ?? 0); ?>" id="previous-achievement-circle">
                                    <svg width="250" height="250" viewBox="0 0 120 120">
                                        <circle cx="60" cy="60" r="50" fill="none" stroke="#e9ecef" stroke-width="8"></circle>
                                        <circle cx="60" cy="60" r="50" fill="none" stroke="var(--<?php echo e($previousShiftAchievement['status_class'] ?? 'warning'); ?>-color)" stroke-width="8" 
                                                stroke-linecap="round" stroke-dasharray="314" stroke-dashoffset="<?php echo e(round(314 - (($previousShiftAchievement['achievement_percent'] ?? 0) / 100) * 314)); ?>" 
                                                transform="rotate(-90 60 60)" id="achievement-circle"></circle>
                                    </svg>
                                    <div class="position-absolute top-50 start-50 translate-middle text-center">
                                        <h4 class="mb-0 text-<?php echo e($previousShiftAchievement['status_class'] ?? 'warning'); ?>" id="previous-achievement-percent"><?php echo e(number_format($previousShiftAchievement['achievement_percent'] ?? 0, 1)); ?>%</h4>
                                        <small class="text-muted">Achievement</small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-2">
                                <div class="fw-medium text-<?php echo e($previousShiftAchievement['status_class'] ?? 'warning'); ?>" id="previous-status-text"><?php echo e($previousShiftAchievement['status'] ?? 'Below Target'); ?></div>
                                <small class="text-muted" id="previous-gap-text"><?php echo e($previousShiftAchievement['gap_text'] ?? 'No data available'); ?></small>
                            </div>
                        </div>
                        
                        <!-- Night Shift Metrics Grid -->
                        <div class="row g-2 mb-3">
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-bullseye text-primary mb-1"></i>
                                    <div class="h6 mb-0" id="previous-target-pcs"><?php echo e($previousShiftAchievement['target_pcs'] ?? '0M'); ?></div>
                                    <small class="text-muted">Target PCS</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-check-circle text-success mb-1"></i>
                                    <div class="h6 mb-0" id="previous-actual-pcs"><?php echo e($previousShiftAchievement['actual_pcs'] ?? '0M'); ?></div>
                                    <small class="text-muted">Actual PCS</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-clock text-info mb-1"></i>
                                    <div class="h6 mb-0" id="previous-total-hours"><?php echo e($previousShiftAchievement['total_hours'] ?? '0h'); ?></div>
                                    <small class="text-muted">Total Hours</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-3 bg-light rounded">
                                    <i class="fas fa-industry text-warning mb-1"></i>
                                    <div class="h6 mb-0" id="previous-lines-active"><?php echo e($previousShiftAchievement['lines_active']['active'] ?? 0); ?>/<?php echo e($previousShiftAchievement['lines_active']['total'] ?? 11); ?></div>
                                    <small class="text-muted">Lines Active</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Status Summary -->
                        <div class="border-top pt-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="small fw-medium">Period Result:</span>
                                <span class="badge bg-<?php echo e($previousShiftAchievement['status_class'] ?? 'warning'); ?>" id="previous-result-badge"><?php echo e($previousShiftAchievement['status'] ?? 'Below Target'); ?></span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="small fw-medium">Impact:</span>
                                <span class="small text-primary" id="previous-impact-text"><?php echo e($previousShiftAchievement['impact'] ?? 'Need recovery in next period'); ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Per Line & Line Area Performance Breakdown -->
            <div class="col-xl-4">
                <div class="card custom-card h-100">
                    <div class="card-header pb-3">
                        <div class="card-title d-flex align-items-center justify-content-between mb-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-chart-bar me-2 text-info"></i>
                                <span class="bg-info-transparent" id="previous-achievement-title"><?php echo e($previousShiftAchievement['title'] ?? 'Current Period Achievement'); ?></span>
                            </div>
                        </div>
                        <small class="text-muted" id="previous-achievement-subtitle"><?php echo e($previousShiftAchievement['subtitle'] ?? 'Previous period results'); ?></small>
                    </div>
                    <div class="card-body pt-0 line-performance-container h-100">
                        <!-- Scrollable Line Performance Section -->
                        <div class="line-performance-scrollable" id="line-performance-scrollable">
                            <!-- Top Performers Section -->
                            <?php if(!empty($linePerformanceAnalysis['top_performers'])): ?>
                            <div class="mb-3">
                                <h6 class="text-success mb-2 d-flex align-items-center">
                                    <i class="fas fa-trophy me-1"></i> 
                                    <span>Top Performers</span>
                                    <span class="badge bg-success ms-2"><?php echo e(count($linePerformanceAnalysis['top_performers'])); ?></span>
                                </h6>
                                <?php $__currentLoopData = $linePerformanceAnalysis['top_performers']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $performer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="performance-item line-clickable d-flex justify-content-between align-items-center mb-2 p-2 bg-success-transparent rounded" 
                                     data-line="<?php echo e($performer['line']); ?>">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-success me-2"><?php echo e($performer['line']); ?></span>
                                        <div class="flex-fill">
                                            <div class="fw-medium small">Line <?php echo e($performer['line']); ?></div>
                                            <div class="row g-0 text-muted small">
                                                <div class="col">Target: <?php echo e($performer['target_formatted']); ?></div>
                                            </div>
                                            <div class="row g-0 text-muted small">
                                                <div class="col">Result: <?php echo e($performer['result_formatted']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-medium text-success"><?php echo e($performer['performance_percent']); ?>%</div>
                                        <small class="text-muted">Achievement</small>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Average Performers Section -->
                            <?php if(!empty($linePerformanceAnalysis['average_performance'])): ?>
                            <div class="mb-3">
                                <h6 class="text-info mb-2 d-flex align-items-center">
                                    <i class="fas fa-minus me-1"></i> 
                                    <span>Average Performance</span>
                                    <span class="badge bg-info ms-2"><?php echo e(count($linePerformanceAnalysis['average_performance'])); ?></span>
                                </h6>
                                <?php $__currentLoopData = $linePerformanceAnalysis['average_performance']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $performer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="performance-item line-clickable d-flex justify-content-between align-items-center mb-1 p-2 bg-light rounded" 
                                     data-line="<?php echo e($performer['line']); ?>">
                                    <div class="d-flex align-items-center">
                                        <span class="badge bg-info me-2"><?php echo e($performer['line']); ?></span>
                                        <div class="flex-fill">
                                            <div class="fw-medium small">Line <?php echo e($performer['line']); ?></div>
                                            <div class="row g-0 text-muted small">
                                                <div class="col">Target: <?php echo e($performer['target_formatted']); ?></div>
                                            </div>
                                            <div class="row g-0 text-muted small">
                                                <div class="col">Result: <?php echo e($performer['result_formatted']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-medium text-info"><?php echo e($performer['performance_percent']); ?>%</div>
                                        <small class="text-muted">Achievement</small>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php endif; ?>
                            
                            <!-- Needs Attention Section -->
                            <?php if(!empty($linePerformanceAnalysis['needs_attention'])): ?>
                            <div class="mb-3">
                                <h6 class="text-warning mb-2 d-flex align-items-center">
                                    <i class="fas fa-exclamation-triangle me-1"></i> 
                                    <span>Needs Attention</span>
                                    <span class="badge bg-warning ms-2"><?php echo e(count($linePerformanceAnalysis['needs_attention'])); ?></span>
                                </h6>
                                <?php $__currentLoopData = $linePerformanceAnalysis['needs_attention']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $performer): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="performance-item line-clickable d-flex justify-content-between align-items-center mb-1 p-2 <?php echo e($index < 1 ? 'bg-danger-transparent' : 'bg-warning-transparent'); ?> rounded" 
                                     data-line="<?php echo e($performer['line']); ?>">
                                    <div class="d-flex align-items-center">
                                        <span class="badge <?php echo e($index < 1 ? 'bg-danger' : 'bg-warning'); ?> me-2"><?php echo e($performer['line']); ?></span>
                                        <div class="flex-fill">
                                            <div class="fw-medium small">Line <?php echo e($performer['line']); ?></div>
                                            <div class="row g-0 text-muted small">
                                                <div class="col">Target: <?php echo e($performer['target_formatted']); ?></div>
                                            </div>
                                            <div class="row g-0 text-muted small">
                                                <div class="col">Result: <?php echo e($performer['result_formatted']); ?></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="text-end">
                                        <div class="fw-medium <?php echo e($index < 1 ? 'text-danger' : 'text-warning'); ?>"><?php echo e($performer['performance_percent']); ?>%</div>
                                        <small class="text-muted">Achievement</small>
                                    </div>
                                </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- Area Performance (Bottom Section) -->
                        <div class="area-performance-container">
                            <h6 class="mb-2 text-secondary d-flex align-items-center">
                                <i class="fas fa-map-marker-alt me-2"></i>
                                Area Performance
                            </h6>
                            <small class="text-muted mb-2 d-block">Line <span id="selected-line-display"><?php echo e($linePerformanceAnalysis['selected_line'] ?? 'A'); ?></span> - 4 Areas</small>
                            <div class="row g-2 text-center" id="area-performance-container">
                                <?php for($area = 1; $area <= 4; $area++): ?>
                                    <?php
                                        $areaPerf = $linePerformanceAnalysis['area_performance'][$area] ?? 0;
                                        $colorClass = $areaPerf >= 100 ? 'success' : ($areaPerf >= 80 ? 'warning' : 'danger');
                                    ?>
                                    <div class="col-3">
                                        <div class="p-2 bg-<?php echo e($colorClass); ?>-transparent rounded area-performance-item" data-area="<?php echo e($area); ?>">
                                            <div class="fw-medium text-<?php echo e($colorClass); ?>"><?php echo e($areaPerf); ?>%</div>
                                            <small class="text-muted">Area <?php echo e($area); ?></small>
                                        </div>
                                    </div>
                                <?php endfor; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Current Time Live Performance Analysis -->
            <div class="col-xl-4">
                <div class="card custom-card h-100">
                    <div class="card-header pb-3">
                        <div class="card-title d-flex align-items-center justify-content-between mb-0">
                            <div class="d-flex align-items-center">
                                <i class="fas fa-calendar-day me-2 text-warning"></i>
                                <span id="current-monitor-title"><?php echo e($currentPerformanceMonitor['title'] ?? 'Full Day Progress Monitor'); ?></span>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-warning-transparent me-2" id="current-monitor-badge"><?php echo e($currentPerformanceMonitor['badge_text'] ?? 'Full Day'); ?></span>
                                <div class="spinner-border spinner-border-sm text-warning" role="status" style="width: 12px; height: 12px;">
                                    <span class="visually-hidden">Updating...</span>
                                </div>
                            </div>
                        </div>
                        <small class="text-muted" id="current-time-display"><?php echo e($currentPerformanceMonitor['subtitle'] ?? 'Full day analysis (00:00 ~ 23:59)'); ?></small>
                    </div>
                    <div class="card-body pt-0">
                        <!-- Current Day Shift Progress -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span class="fw-medium" id="current-progress-title"><?php echo e($currentPerformanceMonitor['period_desc'] ?? 'Period Progress'); ?></span>
                                <div class="d-flex align-items-center">
                                    <span class="text-success fw-medium me-2" id="current-progress-text"><?php echo e(number_format($currentPerformanceMonitor['progress_percent'] ?? 0, 1)); ?>%</span>
                                    <small class="text-muted">Complete</small>
                                </div>
                            </div>
                            <div class="progress mb-2" style="height: 10px;">
                                <div class="progress-bar bg-gradient-success" role="progressbar" style="width: <?php echo e($currentPerformanceMonitor['progress_percent'] ?? 0); ?>%" id="current-progress-bar"></div>
                            </div>
                            <div class="d-flex justify-content-between">
                                <small class="text-muted">Start</small>
                                <small class="text-success fw-medium" id="current-time-marker"><?php echo e($currentPerformanceMonitor['current_time'] ?? 'Now'); ?></small>
                                <small class="text-muted">End</small>
                            </div>
                        </div>
                        
                        <!-- Live Metrics Grid -->
                        <div class="row g-2 mb-3">
                            <div class="col-6">
                                <div class="text-center p-2 bg-success-transparent rounded">
                                    <i class="fas fa-play text-success"></i>
                                    <div class="fw-medium text-success" id="current-lines-running"><?php echo e($currentPerformanceMonitor['lines_running'] ?? '0/11'); ?></div>
                                    <small class="text-muted">Lines Running</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-info-transparent rounded">
                                    <i class="fas fa-tachometer-alt text-info"></i>
                                    <div class="fw-medium text-info" id="current-avg-efficiency"><?php echo e($currentPerformanceMonitor['avg_efficiency'] ?? '0%'); ?></div>
                                    <small class="text-muted">Avg Efficiency</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-primary-transparent rounded">
                                    <i class="fas fa-target text-primary"></i>
                                    <div class="fw-medium text-<?php echo e($currentPerformanceMonitor['vs_target_class'] ?? 'primary'); ?>" id="current-vs-target"><?php echo e($currentPerformanceMonitor['vs_target'] ?? '0%'); ?></div>
                                    <small class="text-muted">vs Target</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center p-2 bg-warning-transparent rounded">
                                    <i class="fas fa-clock text-warning"></i>
                                    <div class="fw-medium text-warning" id="current-time-remaining"><?php echo e($currentPerformanceMonitor['time_remaining'] ?? '0h'); ?></div>
                                    <small class="text-muted">Remaining</small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Current Best & Worst Performance -->
                        <div class="mb-3">
                            <?php if(($currentPerformanceMonitor['best_line']['status'] ?? '') === 'No Data'): ?>
                            <div class="d-flex justify-content-center align-items-center p-3 bg-light rounded">
                                <div class="text-center">
                                    <i class="fas fa-info-circle text-muted mb-2" style="font-size: 24px;"></i>
                                    <div class="fw-medium text-muted mb-1">No Production Data</div>
                                    <small class="text-muted">Start production to see line performance</small>
                                </div>
                            </div>
                            <?php else: ?>
                            <div class="d-flex justify-content-between align-items-center mb-2 p-2 bg-success-transparent rounded">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-arrow-up text-success me-2"></i>
                                    <div>
                                        <div class="fw-medium small text-success" id="current-best-line">Best: Line <?php echo e($currentPerformanceMonitor['best_line']['line'] ?? 'A'); ?></div>
                                        <small class="text-muted" id="current-best-efficiency"><?php echo e($currentPerformanceMonitor['best_line']['efficiency'] ?? '0% efficiency'); ?></small>
                                    </div>
                                </div>
                                <span class="badge bg-success"><?php echo e($currentPerformanceMonitor['best_line']['status'] ?? 'Good'); ?></span>
                            </div>
                            <div class="d-flex justify-content-between align-items-center p-2 bg-<?php echo e(($currentPerformanceMonitor['worst_line']['status'] ?? '') === 'Critical' ? 'danger' : 'warning'); ?>-transparent rounded">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-arrow-down text-<?php echo e(($currentPerformanceMonitor['worst_line']['status'] ?? '') === 'Critical' ? 'danger' : 'warning'); ?> me-2"></i>
                                    <div>
                                        <div class="fw-medium small text-<?php echo e(($currentPerformanceMonitor['worst_line']['status'] ?? '') === 'Critical' ? 'danger' : 'warning'); ?>" id="current-worst-line">Alert: Line <?php echo e($currentPerformanceMonitor['worst_line']['line'] ?? 'K'); ?></div>
                                        <small class="text-muted" id="current-worst-efficiency"><?php echo e($currentPerformanceMonitor['worst_line']['efficiency'] ?? '0% efficiency'); ?></small>
                                    </div>
                                </div>
                                <span class="badge bg-<?php echo e(($currentPerformanceMonitor['worst_line']['status'] ?? '') === 'Critical' ? 'danger' : 'warning'); ?>"><?php echo e($currentPerformanceMonitor['worst_line']['status'] ?? 'Monitor'); ?></span>
                            </div>
                            <?php endif; ?>
                        </div>
                        
                        <!-- AI Dynamic Analysis -->
                        <div class="border-top pt-3">
                            <h6 class="mb-2 d-flex align-items-center">
                                <i class="fas fa-robot me-1 text-primary"></i> 
                                <span>AI Analysis</span>
                                <div class="spinner-border spinner-border-sm ms-2" role="status" style="width: 10px; height: 10px;">
                                    <span class="visually-hidden">Analyzing...</span>
                                </div>
                            </h6>
                            
                            <!-- AI Recommendation -->
                            <div class="alert alert-info p-2 mb-2" id="ai-recommendation-container">
                                <div class="d-flex align-items-start">
                                    <i class="fas fa-lightbulb me-2 mt-1 flex-shrink-0"></i>
                                    <div class="flex-grow-1">
                                        <div class="fw-medium small mb-1" id="ai-recommendation-title">
                                            <?php echo e($currentPerformanceMonitor['ai_recommendation']['title'] ?? 'AI Recommendation'); ?>

                                        </div>
                                        <div class="small" id="ai-recommendation-message">
                                            <?php echo e($currentPerformanceMonitor['ai_recommendation']['message'] ?? 'Loading recommendation...'); ?>

                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- AI Alerts Section -->
                            <div id="ai-alerts-section" style="<?php echo e(empty($currentPerformanceMonitor['ai_alerts']) ? 'display: none;' : ''); ?>">
                                <div id="ai-alerts-container">
                                    <?php if(!empty($currentPerformanceMonitor['ai_alerts'])): ?>
                                        <?php $__currentLoopData = $currentPerformanceMonitor['ai_alerts']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $alert): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <div class="alert alert-<?php echo e($alert['type'] ?? 'info'); ?> alert-dismissible d-flex align-items-start mb-2 py-2">
                                                <i class="fas fa-<?php echo e($alert['type'] === 'success' ? 'check-circle text-success' : ($alert['type'] === 'warning' ? 'exclamation-triangle text-warning' : ($alert['type'] === 'danger' ? 'exclamation-circle text-danger' : 'info-circle text-info'))); ?> me-2 mt-1 flex-shrink-0"></i>
                                                <div class="flex-grow-1">
                                                    <div class="fw-medium small mb-1"><?php echo e($alert['title'] ?? 'Alert'); ?></div>
                                                    <div class="small text-muted"><?php echo e($alert['message'] ?? 'No details available'); ?></div>
                                                </div>
                                                <button type="button" class="btn-close btn-close-sm" data-bs-dismiss="alert" aria-label="Close"></button>
                                            </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- End:: row-3 -->

    <!-- End::app-content -->

    <!-- Clickable Card Styling -->
    <style>
        /* Clickable Card Styling */
        .clickable-card {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .clickable-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15) !important;
        }
        
        .clickable-card:active {
            transform: translateY(-1px);
            transition: all 0.1s ease;
        }
        
        /* Ensure text colors are preserved in clickable cards */
        a.text-decoration-none:hover .clickable-card h5,
        a.text-decoration-none:hover .clickable-card span,
        a.text-decoration-none:hover .clickable-card small {
            color: inherit !important;
        }
        
        /* External link icon animation */
        .clickable-card:hover .fa-external-link-alt {
            opacity: 1 !important;
            transform: translateX(2px);
            transition: all 0.3s ease;
        }
        
        /* Dark mode support for clickable cards */
        [data-theme-mode="dark"] .clickable-card:hover {
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
        }
    </style>

    <!-- Dashboard Data for JavaScript -->
    <script>
        // Make dashboard stats available to JavaScript
        window.dashboardStats = <?php echo json_encode($dashboardStats ?? [], 15, 512) ?>;
        
        // Ensure equipment status data is available for the chart
        if (window.dashboardStats) {
            window.dashboardStats.total_equipment = <?php echo e($dashboardStats['total_equipment'] ?? 0); ?>;
            window.dashboardStats.equipment_with_ongoing = <?php echo e($dashboardStats['equipment_with_ongoing'] ?? 0); ?>;
            window.dashboardStats.equipment_status_percentage = <?php echo e($dashboardStats['equipment_status_percentage'] ?? 0); ?>;
        }
    </script>

 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Project\process-dashboard\resources\views/dashboard.blade.php ENDPATH**/ ?>