# Database Area Performance Analysis & Implementation Strategy

## 📊 **Current Database Structure Analysis**

### 🔧 **Equipment Table Structure**
```sql
equipment:
- id (primary key)
- eqp_no (unique equipment identifier)
- eqp_line (A, B, C, D, E, F, G, H, I, J, K)
- eqp_area (equipment area within a line)
- eqp_type, eqp_class, eqp_maker
- feeder_type, lot_size, work_type, lot_type
- lotqty_alloc
- eqp_oee (decimal 5,2)
- eqp_speed (integer)
- operation_time (integer)
- eqp_code
- ongoing_lot (nullable - current lot assignment)
- modified_by, timestamps
```

### 📋 **Endtime Table Structure**
```sql
endtime:
- id (primary key)
- lot_id, model_15, lot_size, lot_qty
- work_type, lot_type, lipas_yn
- eqp_1 to eqp_10 (equipment assignments - up to 10 equipment per lot)
- start_time_1 to start_time_10 (start times for each equipment)
- ng_percent_1 to ng_percent_10 (NG percentages per equipment)
- eqp_line (aggregated line from equipment)
- eqp_area (aggregated area from equipment)
- status (Ongoing/Submitted)
- est_endtime, actual_submitted_at
- remarks, submission_notes, modified_by
```

---

## 🎯 **Key Findings for Area Performance Implementation**

### ✅ **What We Have:**
1. **Line-Level Data**: Both tables have `eqp_line` field (A-K lines)
2. **Area-Level Data**: Both tables have `eqp_area` field for area within line
3. **Multi-Equipment Support**: Endtime table supports up to 10 equipment per lot
4. **Performance Metrics**: NG percentages, start/end times, lot quantities
5. **Status Tracking**: Ongoing vs Submitted status for performance calculation

### ⚠️ **Current Challenges:**

#### **1. Cross-Line Equipment Sharing**
- **Problem**: One lot can use equipment from multiple lines (eqp_1 to eqp_10)
- **Current Solution**: `eqp_line` and `eqp_area` are aggregated fields
- **Impact**: Area performance calculation becomes complex when lots span multiple areas

#### **2. Area Assignment Logic**
- **Question**: How is `eqp_area` determined when a lot uses multiple equipment?
- **Current**: Appears to be the primary or first equipment's area
- **Needed**: Clear business rule for area attribution

#### **3. Performance Calculation Complexity**
- **Challenge**: How to attribute lot performance to specific areas when equipment is shared
- **Current**: Simple aggregation may not accurately reflect area-specific performance

---

## 🚀 **Recommended Implementation Strategy**

### **Option 1: Enhanced Current Structure (Recommended)**

#### **Database Capabilities Assessment:**
✅ **Current tables ARE capable** with some enhancements

#### **Implementation Approach:**

```php
// 1. Area Performance Calculation Method
private function calculateAreaPerformance($line, $area, $filters) 
{
    // Get lots that have equipment in this specific area
    $lotsQuery = Endtime::query();
    $this->applyDashboardTimeFilter($lotsQuery, $filters['dashboard_date'], 
                                   $filters['dashboard_shift'], $filters['dashboard_cutoff']);
    
    // Apply work type filter
    if ($filters['dashboard_work_type'] !== 'all') {
        $lotsQuery->where('work_type', $filters['dashboard_work_type']);
    }
    
    // Get lots that use equipment from this line-area combination
    $lotsInArea = $lotsQuery->where(function($query) use ($line, $area) {
        // Check all equipment columns (eqp_1 to eqp_10)
        for ($i = 1; $i <= 10; $i++) {
            $query->orWhere(function($subQuery) use ($line, $area, $i) {
                $subQuery->whereNotNull("eqp_{$i}")
                        ->whereExists(function($equipQuery) use ($line, $area, $i) {
                            $equipQuery->select(DB::raw(1))
                                      ->from('equipment')
                                      ->whereColumn("equipment.eqp_no", "=", "endtime.eqp_{$i}")
                                      ->where('equipment.eqp_line', $line)
                                      ->where('equipment.eqp_area', $area);
                        });
            });
        }
    })->get();
    
    // Calculate performance metrics
    $totalCapacity = $this->getAreaTargetCapacity($line, $area, $filters);
    $submittedQuantity = $lotsInArea->where('status', 'Submitted')->sum('lot_qty');
    
    return $totalCapacity > 0 ? round(($submittedQuantity / $totalCapacity) * 100, 1) : 0;
}

// 2. Area Target Capacity Calculation
private function getAreaTargetCapacity($line, $area, $filters)
{
    $equipmentInArea = Equipment::where('eqp_line', $line)
                                ->where('eqp_area', $area);
    
    // Apply work type filter
    if ($filters['dashboard_work_type'] !== 'all') {
        $equipmentInArea->where('work_type', $filters['dashboard_work_type']);
    }
    
    $equipment = $equipmentInArea->get();
    $timeMultiplier = $this->getTimeMultiplier($filters['dashboard_shift'], 
                                              $filters['dashboard_cutoff']);
    
    $totalCapacity = 0;
    foreach ($equipment as $eq) {
        $oee = floatval($eq->eqp_oee) ?: 0;
        $speed = floatval(str_replace(',', '', $eq->eqp_speed)) ?: 0;
        $operationTime = floatval(str_replace(',', '', $eq->operation_time)) ?: 0;
        
        $capacity = round($oee * $speed * $operationTime * $timeMultiplier);
        $totalCapacity += $capacity;
    }
    
    return $totalCapacity;
}

// 3. Enhanced Line Performance Analysis
private function getLinePerformanceAnalysis($filters)
{
    $lines = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K'];
    $performanceData = [];
    
    foreach ($lines as $line) {
        // Calculate overall line performance
        $lineData = $this->getLineDataByFilters($line, $filters);
        
        // Calculate area performance for each area in this line (1-4)
        $areaPerformance = [];
        for ($area = 1; $area <= 4; $area++) {
            $areaPerformance[$area] = $this->calculateAreaPerformance($line, $area, $filters);
        }
        
        $performanceData[$line] = [
            'line' => $line,
            'target_capacity' => $lineData['target_capacity'],
            'target_formatted' => number_format($lineData['target_capacity'] / 1000000, 1) . 'M PCS',
            'result' => $lineData['submitted_lots'],
            'result_formatted' => number_format($lineData['submitted_lots'] / 1000000, 1) . 'M PCS',
            'performance_percent' => $lineData['target_capacity'] > 0 ? 
                round(($lineData['submitted_lots'] / $lineData['target_capacity']) * 100, 1) : 0,
            'area_performance' => $areaPerformance
        ];
    }
    
    // Rest of categorization logic remains the same...
    return $this->categorizeLinePerformance($performanceData);
}
```

### **Option 2: Database Enhancement (Future Consideration)**

For more complex scenarios, consider adding:

```sql
-- New table for equipment-lot relationships
CREATE TABLE lot_equipment_assignments (
    id BIGINT PRIMARY KEY,
    lot_id VARCHAR(255),
    equipment_no VARCHAR(255),
    area_contribution_percent DECIMAL(5,2), -- % of lot attributed to this area
    start_time DATETIME,
    end_time DATETIME,
    ng_percent DECIMAL(5,2),
    created_at TIMESTAMP,
    updated_at TIMESTAMP,
    INDEX(lot_id),
    INDEX(equipment_no),
    FOREIGN KEY (equipment_no) REFERENCES equipment(eqp_no)
);
```

---

## 🎯 **Business Logic Recommendations**

### **Area Attribution Rules:**
1. **Primary Area**: Use the area of the equipment with the highest contribution
2. **Proportional**: Split performance based on equipment contribution percentages
3. **First Equipment**: Use the area of `eqp_1` (current approach)

### **Performance Calculation:**
1. **Area Target**: Sum of all equipment capacities in that area
2. **Area Result**: Sum of submitted lots that used equipment from that area
3. **Area Performance**: (Area Result / Area Target) × 100

---

## ✅ **Implementation Feasibility**

### **Current Database Capabilities:**
- ✅ **Sufficient for basic area performance**
- ✅ **Supports multi-equipment lot tracking**
- ✅ **Has area and line fields in both tables**
- ✅ **Contains all necessary performance metrics**

### **Recommended Next Steps:**
1. **Implement Option 1** using current database structure
2. **Add business logic** for area attribution rules
3. **Test with real data** to validate calculations
4. **Consider Option 2** if more granular tracking is needed

### **Code Changes Required:**
- ✅ **Minimal database changes** (current structure works)
- ✅ **Backend controller updates** (new calculation methods)
- ✅ **Frontend integration** (already prepared)

---

## 🚀 **Conclusion**

The **current database structure IS capable** of implementing proper area performance calculation. The main challenge is implementing the business logic for handling multi-equipment lots, which can be solved with enhanced query logic without requiring database schema changes.

**Recommended approach**: Implement Option 1 first, then evaluate if Option 2 is needed based on business requirements and performance accuracy needs.

---

*Generated: {{ date('Y-m-d H:i:s') }}*
*Laravel Process Dashboard - Database Area Performance Analysis*
