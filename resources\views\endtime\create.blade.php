<x-app-layout>
    <x-slot name="header">
        ENDTIME | CREATE NEW ENDTIME
    </x-slot>

    <!-- Enhanced CSS for endtime functionality -->
    <link href="{{ asset('css/endtime.css') }}" rel="stylesheet">

    <!-- New Lot Entry & Endtime Forecast -->
    <div class="endtime-header">
        <div class="endtime-title">
            <i class="fas fa-clock"></i> New Lot Entry & Endtime Forecast
        </div>
        <div class="endtime-actions">
            <a href="{{ route('dashboard') }}" class="btn-dashboard">
                <i class="fas fa-home"></i> Dashboard
            </a>
            <a href="{{ route('endtime.index') }}" class="btn-back">
                <i class="fas fa-arrow-left"></i> Back to List
            </a>
            <a href="{{ route('endtime.submit.show') }}" class="btn-submit">
                <i class="fas fa-plus-circle"></i> ADD SUBMITTED LOT
            </a>
        </div>
    </div>

    <form id="newLotForm" action="{{ route('endtime.store') }}" method="POST">
        @csrf
        
        <!-- Lot Information Section -->
        <div class="lot-info-section">
            <div class="section-header">
                <span style="text-align: left;"><i class="fas fa-info-circle"></i> Lot Information</span>
            </div>
            <div class="lot-form-row">
                <!-- Lot ID -->
                <div class="lot-field">
                    <label>Lot No.</label>
                    <div class="lot-id-input">
                        <input type="text" id="lot_id" name="lot_id" placeholder="Lot No.." required maxlength="15" />
                        <button type="button" class="search-btn" onclick="forceLotLookup()" style="padding: 4px 8px; font-size: 12px;">
                            <i class="fa fa-search"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Lot Qty -->
                <div class="lot-field">
                    <label>Lot Qty</label>
                    <div class="lot-qty-display">
                        <div class="qty-number" id="lotQtyValue">--</div>
                        <div class="qty-label">PCS</div>
                    </div>
                </div>
                
                <!-- Lot Type -->
                <div class="lot-field">
                    <label>Lot Type</label>
                    <div class="lot-type-options">
                        <label class="type-option main-selected">
                            <input type="radio" name="lot_type" value="MAIN" checked />
                            <span class="type-badge main">MAIN</span>
                        </label>
                        <label class="type-option">
                            <input type="radio" name="lot_type" value="RL/LY" />
                            <span class="type-badge rlly">RL/LY</span>
                        </label>
                    </div>
                </div>
                
                <!-- Lot Details -->
                <div class="lot-field">
                    <label>Lot Details</label>
                    <div class="lot-details">
                        <div class="detail-item">Lot No: <span id="detailLotNo">--</span></div>
                        <div class="detail-item">Model: <span id="detailModel">--</span></div>
                        <div class="detail-item">LIPAS: <span id="detailLipas">--</span></div>
                        <div class="detail-item">Size: <span id="detailSize">--</span></div>
                    </div>
                </div>
                
                <!-- Spacer -->
                <div class="lot-field"></div>
                
                <!-- Calculation Results -->
                <div class="lot-field calculation-result">
                    <div class="calc-header">
                        <i class="fas fa-calculator"></i> Calculation Results
                    </div>
                    <div class="calc-content">
                        <div class="end-time">---,--,----</div>
                        <div class="time-diff">--:--</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Equipment Assignment Section -->
        <div class="equipment-section">
            <div class="section-header-with-btn">
                <div class="section-title">
                    <i class="fas fa-cogs"></i> Equipment Assignment & Loading Times
                    <span class="mc-count">1 Mc's</span>
                </div>
                <button type="button" class="add-equipment-btn" id="addEquipmentBtn">
                    <i class="fas fa-plus"></i> Add Equipment
                </button>
            </div>
            
            <div class="equipment-table">
                <table>
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>EQUIPMENT</th>
                            <th>NG %</th>
                            <th>START TIME</th>
                            <th>CAPACITY</th>
                            <th>EST. END</th>
                            <th></th>
                        </tr>
                    </thead>
                    <tbody id="equipmentTableBody">
                        <tr class="equipment-row" data-index="1">
                            <td class="row-number">1</td>
                            <td>
                                <div class="equipment-search-wrapper">
                                    <div class="search-input-container">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" class="equipment-search form-control" id="equipment_1" name="equipment[1][eqp_no]" placeholder="Search equipment..." autocomplete="off" required />
                                        <div class="loading-spinner d-none" id="equipmentSpinner_1">
                                            <i class="fas fa-spinner fa-spin"></i>
                                        </div>
                                    </div>
                                    <div class="equipment-dropdown d-none" id="equipmentDropdown_1">
                                        <div class="dropdown-header">
                                            <span class="dropdown-title">Available Equipment</span>
                                            <span class="results-count" id="equipmentResultsCount_1">0 results</span>
                                        </div>
                                        <div class="dropdown-body" id="equipmentResults_1">
                                            <!-- Dynamic equipment options will be inserted here -->
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <div class="ng-input">
                                    <input type="number" id="ng_percent_1" name="equipment[1][ng_percent]" value="0" min="0" max="100" step="0.1" />
                                    <span>%</span>
                                </div>
                            </td>
                            <td>
                                <div class="time-input">
                                    <input type="datetime-local" id="start_time_1" name="equipment[1][start_time]" step="60" placeholder="mm/dd/yyyy -- --" required />
                                    <button type="button" class="time-now-btn" title="Set to current time" data-equipment-index="1">
                                        <i class="fas fa-clock"></i>
                                    </button>
                                </div>
                            </td>
                            <td class="equipment-capacity" id="equipmentCapacity_1">
                                <i class="fas fa-calendar-alt"></i>
                            </td>
                            <td class="equipment-endtime" id="equipmentEndTime_1">
                                <i class="fas fa-times"></i>
                            </td>
                            <td></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- Form Actions -->
        <div class="form-actions">
            <a href="{{ route('endtime.index') }}" class="btn-cancel">
                <i class="fas fa-times"></i> Cancel
            </a>
            <button type="submit" class="btn-save" id="submitForecastBtn">
                <i class="fas fa-save"></i> Save Lot Entry
            </button>
        </div>
        
        <!-- Hidden form fields -->
        <input type="hidden" id="model_15" name="model_15" />
        <input type="hidden" id="lot_size" name="lot_size" value="10" />
        <input type="hidden" id="lot_qty" name="lot_qty" />
        <input type="hidden" id="work_type" name="work_type" value="NOR" />
        <input type="hidden" id="lot_type_hidden" name="lot_type" value="MAIN" />
        <input type="hidden" id="lipas_yn" name="lipas_yn" value="N" />
    </form>
    
    <!-- JavaScript -->
    <script src="{{ asset('js/endtime.js') }}"></script>
</x-app-layout>
