<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="d-flex align-items-center justify-content-between w-100">
            <div class="d-flex align-items-center gap-3">
                <div>
                    <h1 class="header-title mb-0">Submit Finished Inspection Lots</h1>
                </div>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <!-- Success/Error Messages -->
    <div class="messages-container">
        <?php if(session('success')): ?>
            <div class="alert alert-success modern-alert alert-dismissible fade show" role="alert">
                <div class="alert-content">
                    <div class="alert-icon">
                        <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="alert-body">
                        <h6 class="alert-title">Success!</h6>
                        <p class="alert-message"><?php echo e(session('success')); ?></p>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger modern-alert alert-dismissible fade show" role="alert">
                <div class="alert-content">
                    <div class="alert-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="alert-body">
                        <h6 class="alert-title">Error!</h6>
                        <p class="alert-message"><?php echo e(session('error')); ?></p>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if($errors->any()): ?>
            <div class="alert alert-danger modern-alert alert-dismissible fade show" role="alert">
                <div class="alert-content">
                    <div class="alert-icon">
                        <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="alert-body">
                        <h6 class="alert-title">Validation Errors</h6>
                        <ul class="alert-list mb-0">
                            <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li><?php echo e($error); ?></li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </ul>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
    </div>

    <!-- Main Content Container -->
    <div class="submission-container">
        <div class="row g-4">
            <!-- Left Panel: Form -->
            <div class="col-lg-8">
                <div class="main-card modern-card">
                    <div class="card-header modern-card-header">
                        <div class="d-flex align-items-center gap-3">
                            <h5 class="mb-0">
                                <i class="fas fa-paper-plane"></i>Submit Ongoing Lot
                            </h5>
                            <div class="d-flex gap-2">
                                <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-warning btn-sm">
                                    <i class="fas fa-home me-1"></i>Dashboard
                                </a>
                                <a href="<?php echo e(route('endtime.index')); ?>" class="btn btn-light btn-sm">
                                    <i class="fas fa-list me-1"></i>Back to List
                                </a>
                                <a href="<?php echo e(route('endtime.create')); ?>" class="btn btn-primary btn-sm modern-btn">
                                    <i class="fas fa-plus-circle me-1"></i>Add New Lot
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- <div class="card-header modern-card-header">
                        <div class="d-flex align-items-center gap-3">
                            <div class="icon-badge success">
                                <i class="fas fa-paper-plane"></i>
                            </div>
                            <div>
                                <h5 class="card-title mb-0">Submit Ongoing Lot</h5>
                                <p class="card-subtitle mb-0">Complete and submit production lots for QC inspection</p>
                                <div class="d-flex gap-2">
                                    <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-outline-primary btn-sm modern-btn">
                                        <i class="fas fa-home me-1"></i>Dashboard
                                    </a>
                                    <a href="<?php echo e(route('endtime.index')); ?>" class="btn btn-outline-secondary btn-sm modern-btn">
                                        <i class="fas fa-list me-1"></i>Back to List
                                    </a>
                                    <a href="<?php echo e(route('endtime.create')); ?>" class="btn btn-primary btn-sm modern-btn">
                                        <i class="fas fa-plus-circle me-1"></i>Add New Lot
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div> -->

                    <div class="card-body modern-card-body">
                        <!-- Purpose Banner -->
                        <div class="purpose-banner mb-4">
                            <div class="purpose-content">
                                <div class="purpose-icon">
                                    <i class="fas fa-info-circle"></i>
                                </div>
                                <div class="purpose-text">
                                    <h6>Process Overview</h6>
                                    <p class="mb-0">Update ongoing forecasted lots to "Submitted" status when they have been completed and are ready for QC inspection.</p>
                                </div>
                            </div>
                        </div>

                        <form id="submittedLotForm" action="<?php echo e(route('endtime.submit')); ?>" method="POST" class="modern-form">
                            <?php echo csrf_field(); ?>

                            <!-- Step 1: Lot Search Section -->
                            <div class="form-step active" data-step="1">
                                <div class="step-header">
                                    <div class="step-number">1</div>
                                    <div class="step-content">
                                        <h6 class="step-title">Select Ongoing Lot</h6>
                                        <p class="step-description">Search and select a lot that is ready for submission</p>
                                    </div>
                                </div>

                                <div class="step-body">
                                    <div class="search-section">
                                        <div class="search-input-group">
                                            <label for="lot_search_input" class="form-label">Search Lot ID <span class="required">*</span></label>
                                            <div class="modern-search-wrapper">
                                                <div class="search-input-container">
                                                    <i class="fas fa-search search-icon"></i>
                                                    <input type="text" class="form-control modern-input" id="lot_search_input" name="lot_search"
                                                           placeholder="Enter lot ID to search..."
                                                           autocomplete="off" required
                                                           oninput="searchOngoingLots()"
                                                           onblur="setTimeout(validateLotSelection, 200)"
                                                           onfocus="showLotDropdown()">
                                                    <div class="loading-spinner d-none" id="searchSpinner">
                                                        <i class="fas fa-spinner fa-spin"></i>
                                                    </div>
                                                </div>
                                                <div class="modern-dropdown d-none" id="lotSearchDropdown">
                                                    <div class="dropdown-header">
                                                        <span class="dropdown-title">Available Lots</span>
                                                        <span class="results-count" id="resultsCount">0 results</span>
                                                    </div>
                                                    <div class="dropdown-body" id="dropdownResults">
                                                        <!-- Dynamic lot options will be inserted here -->
                                                    </div>
                                                </div>
                                                <input type="hidden" id="selected_lot_id" name="lot_id" required>
                                            </div>
                                            <div class="form-help">Only lots with "Ongoing" status are available for submission</div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 2: Submission Details Section -->
                            <div class="form-step" data-step="2">
                                <div class="step-header">
                                    <div class="step-number">2</div>
                                    <div class="step-content">
                                        <h6 class="step-title">Submission Details</h6>
                                        <p class="step-description">Enter the actual submission time and confirm status</p>
                                    </div>
                                </div>

                                <div class="step-body">
                                    <div class="row g-4">
                                        <div class="col-md-6">
                                            <div class="input-group-modern">
                                                <label for="actual_submission_time" class="form-label">Actual Submission Time <span class="required">*</span></label>
                                                <div class="datetime-input-group">
                                                    <div class="datetime-input-wrapper">
                                                        <i class="fas fa-calendar-alt input-icon"></i>
                                                        <input type="datetime-local" class="form-control modern-input datetime-input"
                                                               id="actual_submission_time" name="actual_submitted_at"
                                                               step="60" required
                                                               onchange="calculateLotResult()">
                                                    </div>
                                                    <button type="button" class="btn btn-primary quick-action-btn" 
                                                            onclick="setCurrentTime()" title="Set to current time">
                                                        <i class="fas fa-clock"></i>
                                                        <span class="btn-text">Now</span>
                                                    </button>
                                                </div>
                                                <div class="form-help">When was the lot actually submitted to inspection?</div>
                                            </div>
                                        </div>

                                        <div class="col-md-6">
                                            <div class="input-group-modern">
                                                <label for="submitted_status" class="form-label">New Status <span class="required">*</span></label>
                                                <div class="select-wrapper">
                                                    <i class="fas fa-check-circle input-icon"></i>
                                                    <select class="form-select modern-select" id="submitted_status" name="status" required>
                                                        <option value="Submitted" selected>✓ Submitted for Inspection</option>
                                                    </select>
                                                </div>
                                                <div class="form-help">Lot will be marked as submitted and ready for inspection</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 3: Selected Lot Details -->
                            <div class="form-step" data-step="3" id="selectedLotDetails" style="display: none;">
                                <div class="step-header">
                                    <div class="step-number">3</div>
                                    <div class="step-content">
                                        <h6 class="step-title">Review Lot Information</h6>
                                        <p class="step-description">Verify the selected lot details before submission</p>
                                    </div>
                                </div>

                                <div class="step-body">
                                    <div class="lot-review-card">
                                        <div class="lot-header-info">
                                            <div class="lot-id-display">
                                                <h4 id="detail_lot_id" class="lot-id-text">-</h4>
                                                <span class="lot-id-label">Lot ID</span>
                                            </div>
                                            <div class="lot-status-indicator" id="lotStatusIndicator">
                                                <div class="status-dot ongoing"></div>
                                                <span class="status-text">Ongoing</span>
                                            </div>
                                        </div>

                                        <div class="lot-details-grid">
                                            <div class="detail-card">
                                                <div class="detail-icon">
                                                    <i class="fas fa-microchip"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Model</span>
                                                    <span id="detail_model" class="detail-value">-</span>
                                                </div>
                                            </div>

                                            <div class="detail-card">
                                                <div class="detail-icon">
                                                    <i class="fas fa-boxes"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Quantity</span>
                                                    <span id="detail_quantity" class="detail-value">-</span>
                                                </div>
                                            </div>

                                            <div class="detail-card">
                                                <div class="detail-icon">
                                                    <i class="fas fa-industry"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Line/Area</span>
                                                    <span id="detail_line_area" class="detail-value">-</span>
                                                </div>
                                            </div>

                                            <div class="detail-card">
                                                <div class="detail-icon">
                                                    <i class="fas fa-cogs"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Work Type</span>
                                                    <span id="detail_work_type" class="detail-value badge-modern">-</span>
                                                </div>
                                            </div>

                                            <div class="detail-card">
                                                <div class="detail-icon">
                                                    <i class="fas fa-clock"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Est. End Time</span>
                                                    <span id="detail_end_time" class="detail-value time-value">-</span>
                                                </div>
                                            </div>

                                            <div class="detail-card result-card">
                                                <div class="detail-icon result-icon">
                                                    <i class="fas fa-chart-line"></i>
                                                </div>
                                                <div class="detail-content">
                                                    <span class="detail-label">Result</span>
                                                    <span id="detail_result" class="detail-value result-badge">-</span>
                                                    <div id="detail_time_diff" class="time-difference">-</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Step 4: Additional Information -->
                            <div class="form-step" data-step="4">
                                <div class="step-header">
                                    <div class="step-number">4</div>
                                    <div class="step-content">
                                        <h6 class="step-title">Additional Information</h6>
                                        <p class="step-description">Add notes and review calculated results</p>
                                    </div>
                                </div>

                                <div class="step-body">
                                    <div class="row g-4">
                                        <div class="col-md-4">
                                            <div class="result-summary-card">
                                                <div class="result-header">
                                                    <i class="fas fa-calculator"></i>
                                                    <h6>Calculated Result</h6>
                                                </div>
                                                <div class="result-display">
                                                    <input type="text" class="form-control result-input" id="lot_result" name="remarks" readonly
                                                           placeholder="Will be calculated automatically">
                                                </div>
                                                <div class="result-info">
                                                    <small>Automatically calculated based on timing comparison</small>
                                                </div>
                                            </div>
                                        </div>
                                        
                                        <div class="col-md-8">
                                            <div class="notes-section">
                                                <label for="submission_notes" class="form-label">Additional Notes <span class="optional">(Optional)</span></label>
                                                <div class="notes-wrapper">
                                                    <textarea class="form-control modern-textarea" id="submission_notes" name="submission_notes" rows="4"
                                                              placeholder="Enter any additional notes about the lot submission...
                                                            • Production issues or observations
                                                            • Quality concerns
                                                            • Equipment performance notes
                                                            • Other relevant details">
                                                    </textarea>
                                                    <div class="textarea-footer">
                                                        <span class="char-count">0 / 500 characters</span>
                                                        <div class="textarea-tools">
                                                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="clearNotes()">
                                                                <i class="fas fa-eraser"></i>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Actions -->
                            <div class="form-actions">
                                <div class="action-buttons">
                                    <a href="<?php echo e(route('endtime.index')); ?>" class="btn btn-outline-secondary btn-lg modern-btn cancel-btn">
                                        <i class="fas fa-times me-2"></i>
                                        <span>Cancel</span>
                                    </a>
                                    <button type="submit" class="btn btn-success btn-lg modern-btn submit-btn" id="submitLotButton" disabled>
                                        <div class="btn-content">
                                            <i class="fas fa-paper-plane me-2"></i>
                                            <span class="btn-text">Submit Lot</span>
                                        </div>
                                        <div class="btn-loading d-none">
                                            <i class="fas fa-spinner fa-spin me-2"></i>
                                            <span>Submitting...</span>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Right Panel: Summary & Stats -->
            <div class="col-lg-4">
                <div class="side-panel">
                    <!-- Ongoing Lots Summary -->
                    <div class="summary-card modern-card mb-4">
                        <div class="card-header summary-header">
                            <div class="header-icon">
                                <i class="fas fa-list-alt"></i>
                            </div>
                            <div class="header-content">
                                <h6 class="card-title mb-0">Available Lots</h6>
                                <p class="card-subtitle mb-0">Ready for submission</p>
                            </div>
                        </div>
                        <div class="card-body text-center">
                            <div class="stat-circle" id="ongoing-lots-count">
                                <div class="stat-number">--</div>
                                <div class="stat-label">Ongoing Lots</div>
                            </div>
                            <button type="button" class="btn btn-outline-primary btn-sm refresh-btn" onclick="refreshOngoingLots()">
                                <i class="fas fa-sync-alt me-1"></i>
                                <span>Refresh List</span>
                            </button>
                        </div>
                    </div>

                    <!-- Process Steps -->
                    <div class="steps-card modern-card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">Process Steps</h6>
                        </div>
                        <div class="card-body">
                            <div class="steps-list">
                                <div class="step-item active" data-target="1">
                                    <div class="step-marker">1</div>
                                    <div class="step-text">Select Lot</div>
                                    <div class="step-status"><i class="fas fa-circle"></i></div>
                                </div>
                                <div class="step-item" data-target="2">
                                    <div class="step-marker">2</div>
                                    <div class="step-text">Set Details</div>
                                    <div class="step-status"><i class="fas fa-circle"></i></div>
                                </div>
                                <div class="step-item" data-target="3">
                                    <div class="step-marker">3</div>
                                    <div class="step-text">Review Info</div>
                                    <div class="step-status"><i class="fas fa-circle"></i></div>
                                </div>
                                <div class="step-item" data-target="4">
                                    <div class="step-marker">4</div>
                                    <div class="step-text">Add Notes</div>
                                    <div class="step-status"><i class="fas fa-circle"></i></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Integration -->
    <script>
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Set current time for submission input
            const now = new Date();
            const currentDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            const submissionTimeInput = document.getElementById('actual_submission_time');
            if (submissionTimeInput) {
                submissionTimeInput.value = currentDateTime;
            }

            // Load ongoing lots count
            loadOngoingLotsCount();
        });

        // Set current time button functionality
        function setCurrentTime() {
            const now = new Date();
            const currentDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
            document.getElementById('actual_submission_time').value = currentDateTime;
            calculateLotResult();
        }

        // Load ongoing lots count
        function loadOngoingLotsCount() {
            fetch('/endtime/ongoing-lots')
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const countElement = document.querySelector('#ongoing-lots-count .stat-number');
                        if (countElement) {
                            countElement.textContent = data.lots.length;
                        }
                    }
                })
                .catch(error => {
                    console.error('Error loading ongoing lots count:', error);
                });
        }

        // Refresh ongoing lots count
        function refreshOngoingLots() {
            const button = event.target;
            const icon = button.querySelector('i');

            // Show loading state
            icon.classList.add('fa-spin');
            button.disabled = true;

            loadOngoingLotsCount();

            // Reset button state
            setTimeout(() => {
                icon.classList.remove('fa-spin');
                button.disabled = false;
            }, 1000);
        }

        // Search ongoing lots functionality
        // This function is now defined later in the enhanced search section

        // Display lot options in dropdown
        function displayLotOptions(lots) {
            const dropdown = document.getElementById('lotSearchDropdown');
            const resultsCount = document.getElementById('resultsCount');
            const dropdownResults = document.getElementById('dropdownResults');

            // Update results count
            resultsCount.textContent = `${lots.length} result${lots.length !== 1 ? 's' : ''}`;

            if (lots.length === 0) {
                dropdownResults.innerHTML = `
                    <div class="no-results">
                        <div class="no-results-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="no-results-text">
                            <h6>No lots found</h6>
                            <p>Try a different search term or refresh the list</p>
                        </div>
                    </div>
                `;
            } else {
                dropdownResults.innerHTML = lots.map(lot => `
                    <div class="modern-lot-option" onclick="selectLot(${lot.id}, '${lot.lot_id}')">
                        <div class="lot-option-content">
                            <div class="lot-main-info">
                                <div class="lot-id-chip">${lot.lot_id}</div>
                                <div class="lot-model-badge">${lot.model_15}</div>
                            </div>
                            <div class="lot-sub-info">
                                <div class="info-item">
                                    <i class="fas fa-industry"></i>
                                    <span>${lot.eqp_line}/${lot.eqp_area}</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-boxes"></i>
                                    <span>${lot.lot_qty.toLocaleString()} pcs</span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-clock"></i>
                                    <span>${new Date(lot.est_endtime).toLocaleDateString()} ${new Date(lot.est_endtime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</span>
                                </div>
                            </div>
                        </div>
                        <div class="lot-option-arrow">
                            <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                `).join('');
            }

            dropdown.classList.remove('d-none');
        }

        // Select a lot from dropdown
        function selectLot(lotId, lotIdString) {
            document.getElementById('lot_search_input').value = lotIdString;
            document.getElementById('selected_lot_id').value = lotId;
            document.getElementById('lotSearchDropdown').classList.add('d-none');

            // Add visual feedback
            const searchInput = document.getElementById('lot_search_input');
            searchInput.classList.add('selected');

            // Load lot details
            loadLotDetails(lotId);

            // Update step progress
            updateStepProgress(2);

            // Show success animation
            showSelectionFeedback();
        }

        // Show selection feedback
        function showSelectionFeedback() {
            const searchInput = document.getElementById('lot_search_input');
            const feedback = document.createElement('div');
            feedback.className = 'selection-feedback';
            feedback.innerHTML = '<i class="fas fa-check"></i>';
            
            searchInput.parentNode.appendChild(feedback);
            
            setTimeout(() => {
                feedback.remove();
            }, 2000);
        }

        // Update step progress
        function updateStepProgress(currentStep) {
            // Update form steps
            document.querySelectorAll('.form-step').forEach((step, index) => {
                step.classList.toggle('active', index + 1 === currentStep);
                if (index + 1 < currentStep) {
                    step.classList.add('completed');
                }
            });

            // Update side panel steps
            document.querySelectorAll('.step-item').forEach((step, index) => {
                const stepNumber = index + 1;
                step.classList.remove('active', 'completed');
                
                if (stepNumber < currentStep) {
                    step.classList.add('completed');
                    step.querySelector('.step-status i').className = 'fas fa-check';
                } else if (stepNumber === currentStep) {
                    step.classList.add('active');
                    step.querySelector('.step-status i').className = 'fas fa-circle';
                } else {
                    step.querySelector('.step-status i').className = 'far fa-circle';
                }
            });

            // Enable submit button at final step
            if (currentStep >= 3) {
                document.getElementById('submitLotButton').disabled = false;
            }
        }

        // Load lot details
        function loadLotDetails(lotId) {
            fetch(`/api/endtime/${lotId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        const lot = data.lot;

                        // Populate lot details
                        document.getElementById('detail_lot_id').textContent = lot.lot_id;
                        document.getElementById('detail_model').textContent = lot.model_15;
                        document.getElementById('detail_quantity').textContent = lot.lot_qty.toLocaleString() + ' pcs';
                        document.getElementById('detail_work_type').textContent = lot.work_type;
                        document.getElementById('detail_work_type').className = 'detail-value badge-modern ' + (lot.work_type.toLowerCase().includes('rework') ? 'warning' : 'primary');
                        document.getElementById('detail_line_area').textContent = `${lot.eqp_line}/${lot.eqp_area}`;
                        document.getElementById('detail_end_time').textContent = new Date(lot.est_endtime).toLocaleString();

                        // Show details section with animation
                        const detailsSection = document.getElementById('selectedLotDetails');
                        detailsSection.style.display = 'block';
                        detailsSection.classList.add('fade-in');

                        // Update step progress to show lot details
                        updateStepProgress(3);

                        // Calculate result based on current submission time
                        calculateLotResult();
                    }
                })
                .catch(error => {
                    console.error('Error loading lot details:', error);
                });
        }

        // Show lot dropdown
        function showLotDropdown() {
            if (document.getElementById('lot_search_input').value.length > 0) {
                searchOngoingLots();
            }
        }

        // Validate lot selection
        function validateLotSelection() {
            const searchInput = document.getElementById('lot_search_input');
            const selectedLotId = document.getElementById('selected_lot_id');

            if (searchInput.value && !selectedLotId.value) {
                // Clear invalid selection
                searchInput.value = '';
                document.getElementById('selectedLotDetails').style.display = 'none';
                document.getElementById('submitLotButton').disabled = true;
            }
        }

        // Calculate lot result
        function calculateLotResult() {
            const selectedLotId = document.getElementById('selected_lot_id').value;
            const submissionTime = document.getElementById('actual_submission_time').value;

            if (!selectedLotId || !submissionTime) {
                return;
            }

            // Get the estimated end time from the display
            const estEndTimeText = document.getElementById('detail_end_time').textContent;
            if (estEndTimeText === '-') {
                return;
            }

            try {
                const actualTime = new Date(submissionTime);
                const estimatedTime = new Date(estEndTimeText);

                // Calculate difference in minutes
                const diffMinutes = Math.round((actualTime - estimatedTime) / (1000 * 60));

                let result = '';
                let timeDiffText = '';
                let resultClass = '';

                if (diffMinutes > 30) {
                    result = 'Delayed';
                    resultClass = 'bg-danger';
                    timeDiffText = `${diffMinutes} minutes late`;
                } else if (diffMinutes < -30) {
                    result = 'Early';
                    resultClass = 'bg-success';
                    timeDiffText = `${Math.abs(diffMinutes)} minutes early`;
                } else {
                    result = 'OK';
                    resultClass = 'bg-primary';
                    if (diffMinutes === 0) {
                        timeDiffText = 'On time';
                    } else if (diffMinutes > 0) {
                        timeDiffText = `${diffMinutes} minutes late (within tolerance)`;
                    } else {
                        timeDiffText = `${Math.abs(diffMinutes)} minutes early (within tolerance)`;
                    }
                }

                // Update result display
                document.getElementById('lot_result').value = result;
                const resultElement = document.getElementById('detail_result');
                const timeDiffElement = document.getElementById('detail_time_diff');
                
                resultElement.textContent = result;
                resultElement.className = `detail-value result-badge ${result.toLowerCase()}`;
                timeDiffElement.textContent = timeDiffText;
                
                // Update result card styling
                const resultCard = document.querySelector('.result-card');
                if (resultCard) {
                    resultCard.className = `detail-card result-card ${result.toLowerCase()}`;
                }
                
                // Enable final step if everything is filled
                if (result && document.getElementById('actual_submission_time').value) {
                    updateStepProgress(4);
                }

            } catch (error) {
                console.error('Error calculating lot result:', error);
            }
        }

        // Hide dropdown when clicking outside
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('lotSearchDropdown');
            const searchInput = document.getElementById('lot_search_input');

            if (!searchInput.contains(event.target) && !dropdown.contains(event.target)) {
                dropdown.classList.add('d-none');
            }
        });

        // Additional modern UI functions
        function clearNotes() {
            document.getElementById('submission_notes').value = '';
            updateCharCount();
        }

        function updateCharCount() {
            const textarea = document.getElementById('submission_notes');
            const charCount = document.querySelector('.char-count');
            if (textarea && charCount) {
                const length = textarea.value.length;
                charCount.textContent = `${length} / 500 characters`;
                
                if (length > 450) {
                    charCount.classList.add('warning');
                } else {
                    charCount.classList.remove('warning');
                }
            }
        }

        // Enhanced form submission with loading state
        document.getElementById('submittedLotForm').addEventListener('submit', function(e) {
            const submitBtn = document.getElementById('submitLotButton');
            const btnContent = submitBtn.querySelector('.btn-content');
            const btnLoading = submitBtn.querySelector('.btn-loading');
            
            btnContent.classList.add('d-none');
            btnLoading.classList.remove('d-none');
            submitBtn.disabled = true;
        });

        // Character count for notes
        document.addEventListener('DOMContentLoaded', function() {
            const notesTextarea = document.getElementById('submission_notes');
            if (notesTextarea) {
                notesTextarea.addEventListener('input', updateCharCount);
            }
        });

        // Step navigation (optional click navigation)
        document.querySelectorAll('.step-item').forEach(step => {
            step.addEventListener('click', function() {
                const targetStep = parseInt(this.dataset.target);
                const currentActiveStep = document.querySelector('.form-step.active');
                
                if (currentActiveStep && parseInt(currentActiveStep.dataset.step) >= targetStep) {
                    updateStepProgress(targetStep);
                }
            });
        });

        // Enhanced search with better UX
        let searchTimeout;
        function searchOngoingLots() {
            clearTimeout(searchTimeout);
            const spinner = document.getElementById('searchSpinner');
            
            searchTimeout = setTimeout(() => {
                const searchInput = document.getElementById('lot_search_input');
                const searchTerm = searchInput.value.trim();
                const dropdown = document.getElementById('lotSearchDropdown');

                if (searchTerm.length < 1) {
                    dropdown.classList.add('d-none');
                    return;
                }

                // Show loading
                spinner.classList.remove('d-none');

                fetch('/endtime/ongoing-lots')
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            const filteredLots = data.lots.filter(lot =>
                                lot.lot_id.toLowerCase().includes(searchTerm.toLowerCase())
                            );
                            displayLotOptions(filteredLots);
                        }
                    })
                    .catch(error => {
                        console.error('Error searching ongoing lots:', error);
                    })
                    .finally(() => {
                        spinner.classList.add('d-none');
                    });
            }, 300); // Debounce search
        }
    </script>

    <!-- Additional Styles for Modern Design -->
    <style>
        /* Modern Submit Page Styles */
        .submission-container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header-icon-wrapper {
            width: 3.5rem;
            height: 3.5rem;
            border-radius: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .header-icon-wrapper i {
            font-size: 1.5rem;
            color: white;
        }

        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header-title {
            font-size: 1.75rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 0.25rem;
        }

        .header-subtitle {
            color: var(--text-muted);
            font-size: 0.95rem;
        }

        .modern-btn {
            border-radius: 0.75rem;
            font-weight: 600;
            padding: 0.5rem 1rem;
            transition: all 0.3s ease;
            border-width: 2px;
            position: relative;
            overflow: hidden;
        }

        .modern-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .modern-btn:active {
            transform: translateY(0);
        }

        /* Messages */
        .messages-container {
            margin-bottom: 2rem;
        }

        .modern-alert {
            border: none;
            border-radius: 1rem;
            padding: 1.25rem;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .alert-content {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .alert-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-shrink: 0;
        }

        .alert-success .alert-icon {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        .alert-danger .alert-icon {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
        }

        .alert-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .alert-message {
            margin: 0;
            opacity: 0.9;
        }

        .alert-list {
            list-style: none;
            padding-left: 0;
        }

        .alert-list li {
            padding: 0.25rem 0;
        }

        /* Main Cards */
        .modern-card {
            border: none;
            border-radius: 1.25rem;
            box-shadow: 0 6px 30px rgba(0, 0, 0, 0.08);
            overflow: hidden;
            background: var(--surface-color);
            border: 1px solid rgba(0, 0, 0, 0.05);
        }

        .modern-card-header {
            background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
            padding: 1.5rem;
        }

        .icon-badge {
            width: 3rem;
            height: 3rem;
            border-radius: 0.75rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.25rem;
            color: white;
        }

        .icon-badge.success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 20px rgba(16, 185, 129, 0.3);
        }

        .card-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-color);
        }

        .card-subtitle {
            color: var(--text-muted);
            font-size: 0.9rem;
        }

        .modern-card-body {
            padding: 2rem;
        }

        /* Purpose Banner */
        .purpose-banner {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(99, 102, 241, 0.05) 100%);
            border: 1px solid rgba(99, 102, 241, 0.1);
            border-radius: 1rem;
            padding: 1.5rem;
        }

        .purpose-content {
            display: flex;
            align-items: flex-start;
            gap: 1rem;
        }

        .purpose-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            background: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            flex-shrink: 0;
        }

        .purpose-text h6 {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
        }

        .purpose-text p {
            color: var(--text-muted);
            line-height: 1.5;
        }

        /* Form Steps */
        .form-step {
            display: none;
            animation: fadeInUp 0.4s ease-out;
        }

        .form-step.active {
            display: block;
        }

        .form-step.completed {
            opacity: 0.8;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .step-header {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 2px solid #e5e7eb;
        }

        .step-number {
            width: 3rem;
            height: 3rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 1.25rem;
            box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
        }

        .step-content {
            flex: 1;
        }

        .step-title {
            font-size: 1.25rem;
            font-weight: 700;
            color: var(--text-color);
            margin-bottom: 0.25rem;
        }

        .step-description {
            color: var(--text-muted);
            margin: 0;
        }

        .step-body {
            margin-left: 4rem;
        }

        /* Search Section */
        .modern-search-wrapper {
            position: relative;
        }

        .search-input-container {
            position: relative;
        }

        .search-input-container .search-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            z-index: 2;
        }

        .loading-spinner {
            position: absolute;
            right: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--primary-color);
        }

        .modern-input {
            border: 2px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 0.875rem 3rem 0.875rem 3rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: var(--surface-color);
        }

        .modern-input:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            transform: translateY(-1px);
        }

        .modern-input.selected {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.02);
        }

        /* Modern Dropdown */
        .modern-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: var(--surface-color);
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            margin-top: 0.5rem;
            overflow: hidden;
        }

        .dropdown-header {
            padding: 1rem;
            background: #f8fafc;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .dropdown-title {
            font-weight: 600;
            color: var(--text-color);
        }

        .results-count {
            font-size: 0.875rem;
            color: var(--text-muted);
        }

        .dropdown-body {
            max-height: 300px;
            overflow-y: auto;
        }

        .modern-lot-option {
            padding: 1rem;
            border-bottom: 1px solid #f3f4f6;
            cursor: pointer;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .modern-lot-option:hover {
            background: #f8faff;
            transform: translateX(4px);
        }

        .modern-lot-option:last-child {
            border-bottom: none;
        }

        .lot-option-content {
            flex: 1;
        }

        .lot-main-info {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 0.5rem;
        }

        .lot-id-chip {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .lot-model-badge {
            background: #e5e7eb;
            color: var(--text-color);
            padding: 0.25rem 0.5rem;
            border-radius: 0.375rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .lot-sub-info {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }

        .info-item {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .info-item i {
            width: 12px;
            text-align: center;
        }

        .lot-option-arrow {
            color: var(--text-muted);
            font-size: 0.875rem;
        }

        .no-results {
            text-align: center;
            padding: 2rem;
            color: var(--text-muted);
        }

        .no-results-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            opacity: 0.5;
        }

        .no-results h6 {
            margin-bottom: 0.5rem;
        }

        .no-results p {
            margin: 0;
            font-size: 0.875rem;
        }

        /* Form Elements */
        .form-label {
            font-weight: 600;
            color: var(--text-color);
            margin-bottom: 0.5rem;
            display: block;
        }

        .required {
            color: #ef4444;
        }

        .optional {
            color: var(--text-muted);
            font-weight: 400;
            font-size: 0.875rem;
        }

        .form-help {
            font-size: 0.875rem;
            color: var(--text-muted);
            margin-top: 0.5rem;
        }

        .input-group-modern {
            margin-bottom: 1.5rem;
        }

        .datetime-input-group {
            display: flex;
            gap: 0.75rem;
            align-items: flex-end;
        }

        .datetime-input-wrapper {
            flex: 1;
            position: relative;
        }

        .input-icon {
            position: absolute;
            left: 1rem;
            top: 50%;
            transform: translateY(-50%);
            color: var(--text-muted);
            z-index: 2;
        }

        .datetime-input {
            padding-left: 3rem;
        }

        .quick-action-btn {
            border-radius: 0.75rem;
            padding: 0.875rem 1.25rem;
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            transition: all 0.3s ease;
            white-space: nowrap;
        }

        .quick-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .select-wrapper {
            position: relative;
        }

        .modern-select {
            padding-left: 3rem;
            border: 2px solid #e5e7eb;
            border-radius: 0.75rem;
            transition: all 0.3s ease;
        }

        .modern-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        /* Lot Review Card */
        .lot-review-card {
            background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 1rem;
            padding: 2rem;
        }

        .lot-header-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid rgba(102, 126, 234, 0.1);
        }

        .lot-id-display {
            text-align: center;
        }

        .lot-id-text {
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 0.5rem;
        }

        .lot-id-label {
            color: var(--text-muted);
            font-size: 0.875rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .lot-status-indicator {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.5rem 1rem;
            background: rgba(255, 193, 7, 0.1);
            border-radius: 1rem;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        .status-dot.ongoing {
            background: #ffc107;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4);
            }
            70% {
                box-shadow: 0 0 0 8px rgba(255, 193, 7, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
            }
        }

        .status-text {
            font-weight: 500;
            color: #b45309;
        }

        .lot-details-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 1rem;
        }

        .detail-card {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1.25rem;
            display: flex;
            align-items: center;
            gap: 1rem;
            transition: all 0.3s ease;
        }

        .detail-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }

        .detail-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 0.5rem;
            background: linear-gradient(135deg, #e5e7eb 0%, #d1d5db 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-muted);
            flex-shrink: 0;
        }

        .detail-content {
            flex: 1;
        }

        .detail-label {
            display: block;
            font-size: 0.75rem;
            font-weight: 500;
            color: var(--text-muted);
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 0.25rem;
        }

        .detail-value {
            font-weight: 600;
            color: var(--text-color);
            font-size: 1rem;
        }

        .badge-modern {
            background: #e5e7eb;
            color: var(--text-color);
            padding: 0.25rem 0.75rem;
            border-radius: 1rem;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .badge-modern.warning {
            background: rgba(255, 193, 7, 0.1);
            color: #b45309;
            border: 1px solid rgba(255, 193, 7, 0.2);
        }

        .badge-modern.primary {
            background: rgba(99, 102, 241, 0.1);
            color: #5b21b6;
            border: 1px solid rgba(99, 102, 241, 0.2);
        }

        .time-value {
            font-family: 'Courier New', monospace;
        }

        .result-card {
            border: 2px solid #e5e7eb;
        }

        .result-card.ok {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.02);
        }

        .result-card.ok .detail-icon {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
        }

        .result-card.early {
            border-color: #3b82f6;
            background: rgba(59, 130, 246, 0.02);
        }

        .result-card.early .detail-icon {
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
        }

        .result-card.delayed {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.02);
        }

        .result-card.delayed .detail-icon {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
        }

        .result-badge {
            padding: 0.375rem 0.875rem;
            border-radius: 1rem;
            font-weight: 600;
            font-size: 0.875rem;
        }

        .result-badge.ok {
            background: rgba(16, 185, 129, 0.1);
            color: #10b981;
            border: 1px solid rgba(16, 185, 129, 0.2);
        }

        .result-badge.early {
            background: rgba(59, 130, 246, 0.1);
            color: #3b82f6;
            border: 1px solid rgba(59, 130, 246, 0.2);
        }

        .result-badge.delayed {
            background: rgba(239, 68, 68, 0.1);
            color: #ef4444;
            border: 1px solid rgba(239, 68, 68, 0.2);
        }

        .time-difference {
            font-size: 0.75rem;
            color: var(--text-muted);
            margin-top: 0.25rem;
        }

        /* Notes Section */
        .notes-section {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1.25rem;
        }

        .notes-wrapper {
            position: relative;
        }

        .modern-textarea {
            border: 2px solid #e5e7eb;
            border-radius: 0.75rem;
            padding: 1rem;
            resize: vertical;
            min-height: 120px;
            transition: all 0.3s ease;
        }

        .modern-textarea:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .textarea-footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 0.75rem;
            padding-top: 0.75rem;
            border-top: 1px solid #e5e7eb;
        }

        .char-count {
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .char-count.warning {
            color: #ef4444;
        }

        .textarea-tools {
            display: flex;
            gap: 0.5rem;
        }

        /* Result Summary */
        .result-summary-card {
            background: linear-gradient(135deg, #f8faff 0%, #f0f4ff 100%);
            border: 1px solid rgba(102, 126, 234, 0.1);
            border-radius: 1rem;
            padding: 1.5rem;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .result-header {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            margin-bottom: 1rem;
        }

        .result-header i {
            color: #667eea;
        }

        .result-header h6 {
            margin: 0;
            font-weight: 600;
            color: var(--text-color);
        }

        .result-display {
            flex: 1;
            display: flex;
            align-items: center;
        }

        .result-input {
            text-align: center;
            font-weight: 700;
            font-size: 1.125rem;
            background: white;
            border: 2px solid #e5e7eb;
            border-radius: 0.75rem;
        }

        .result-info {
            margin-top: 1rem;
            text-align: center;
        }

        /* Form Actions */
        .form-actions {
            margin-top: 3rem;
            padding-top: 2rem;
            border-top: 1px solid #e5e7eb;
        }

        .action-buttons {
            display: flex;
            justify-content: center;
            gap: 1rem;
        }

        .cancel-btn {
            background: white;
            color: var(--text-muted);
            border-color: #e5e7eb;
        }

        .cancel-btn:hover {
            background: #f9fafb;
            color: var(--text-color);
            border-color: #d1d5db;
        }

        .submit-btn {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            border: none;
            position: relative;
            overflow: hidden;
        }

        .submit-btn:hover:not(:disabled) {
            background: linear-gradient(135deg, #059669 0%, #047857 100%);
        }

        .submit-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .btn-content, .btn-loading {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* Side Panel */
        .side-panel {
            position: sticky;
            top: 2rem;
        }

        .summary-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom: none;
        }

        .header-icon {
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 0.5rem;
        }

        .header-content {
            flex: 1;
        }

        .stat-circle {
            width: 5rem;
            height: 5rem;
            border-radius: 50%;
            background: linear-gradient(135deg, #10b981 0%, #059669 100());
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: white;
            margin: 1.5rem auto;
            box-shadow: 0 8px 30px rgba(16, 185, 129, 0.3);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            line-height: 1;
        }

        .stat-label {
            font-size: 0.625rem;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            opacity: 0.9;
        }

        .refresh-btn {
            border-radius: 0.75rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .refresh-btn:hover {
            transform: translateY(-1px);
        }

        /* Steps List */
        .steps-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .step-item {
            display: flex;
            align-items: center;
            gap: 0.75rem;
            padding: 0.75rem;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .step-item:hover {
            background: #f8fafc;
        }

        .step-item.active {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
        }

        .step-item.completed {
            background: rgba(16, 185, 129, 0.05);
            color: #10b981;
        }

        .step-marker {
            width: 1.75rem;
            height: 1.75rem;
            border-radius: 50%;
            background: #e5e7eb;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.75rem;
            flex-shrink: 0;
        }

        .step-item.active .step-marker {
            background: #667eea;
            color: white;
        }

        .step-item.completed .step-marker {
            background: #10b981;
            color: white;
        }

        .step-text {
            flex: 1;
            font-weight: 500;
        }

        .step-status {
            font-size: 0.625rem;
        }

        /* Tips */
        .tips-list {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
        }

        .tip-item {
            display: flex;
            align-items: flex-start;
            gap: 0.75rem;
            padding: 0.5rem 0;
        }

        .tip-item i {
            color: #667eea;
            margin-top: 0.125rem;
            flex-shrink: 0;
        }

        .tip-item span {
            font-size: 0.875rem;
            color: var(--text-muted);
            line-height: 1.4;
        }

        /* Selection Feedback */
        .selection-feedback {
            position: absolute;
            right: -2rem;
            top: 50%;
            transform: translateY(-50%);
            background: #10b981;
            color: white;
            width: 1.5rem;
            height: 1.5rem;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.75rem;
            animation: feedbackAnimation 2s ease-out;
        }

        @keyframes feedbackAnimation {
            0% {
                opacity: 0;
                transform: translateY(-50%) scale(0.5);
            }
            10% {
                opacity: 1;
                transform: translateY(-50%) scale(1.2);
            }
            20% {
                transform: translateY(-50%) scale(1);
            }
            80% {
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: translateY(-50%) translateX(1rem);
            }
        }

        /* Fade in animation */
        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Responsive Design */
        @media (max-width: 992px) {
            .side-panel {
                position: static;
                margin-top: 2rem;
            }

            .step-body {
                margin-left: 0;
                margin-top: 1rem;
            }

            .header-actions {
                flex-wrap: wrap;
                gap: 0.5rem;
            }

            .action-buttons {
                flex-direction: column;
            }
        }

        @media (max-width: 768px) {
            .header-title {
                font-size: 1.5rem;
            }

            .lot-details-grid {
                grid-template-columns: 1fr;
            }

            .datetime-input-group {
                flex-direction: column;
                align-items: stretch;
            }

            .quick-action-btn {
                width: 100%;
                justify-content: center;
                margin-top: 0.5rem;
            }

            .modern-card-body {
                padding: 1.25rem;
            }

            .step-number {
                width: 2.5rem;
                height: 2.5rem;
                font-size: 1rem;
            }

            .lot-id-text {
                font-size: 1.5rem;
            }

            .stat-circle {
                width: 4rem;
                height: 4rem;
            }

            .stat-number {
                font-size: 1.25rem;
            }
        }

        /* Dark mode adjustments */
        [data-theme-mode="dark"] .modern-card {
            background: var(--custom-white);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .modern-card-header {
            background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(99, 102, 241, 0.02) 100%);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .purpose-banner {
            background: rgba(59, 130, 246, 0.05);
            border-color: rgba(99, 102, 241, 0.1);
        }

        [data-theme-mode="dark"] .modern-input,
        [data-theme-mode="dark"] .modern-select,
        [data-theme-mode="dark"] .modern-textarea {
            background: var(--form-control-bg);
            border-color: var(--input-border);
            color: var(--default-text-color);
        }

        [data-theme-mode="dark"] .modern-dropdown {
            background: var(--custom-white);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .dropdown-header {
            background: var(--default-background);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .modern-lot-option:hover {
            background: var(--list-hover-focus-bg);
        }

        [data-theme-mode="dark"] .lot-review-card {
            background: var(--custom-white);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .detail-card {
            background: var(--default-background);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .notes-section {
            background: var(--custom-white);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .result-summary-card {
            background: var(--custom-white);
            border-color: var(--default-border);
        }

        [data-theme-mode="dark"] .step-item:hover {
            background: var(--list-hover-focus-bg);
        }
    </style>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\inetpub\wwwroot\process-dashboard\resources\views/endtime/submit.blade.php ENDPATH**/ ?>