<x-app-layout>
    <x-slot name="header">
        Order Details
    </x-slot>

    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Order {{ $order->order_number }}</h4>
                    <p class="text-muted mb-0">Order placed on {{ $order->created_at->format('M d, Y \a\t g:i A') }}</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('orders.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Orders
                    </a>
                    @if(Auth::user()->canManageOrders())
                        <a href="{{ route('orders.edit', $order) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-2"></i>Edit Order
                        </a>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="row g-4">
        <!-- Order Summary -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Order Summary</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Order Number</label>
                            <p class="fw-bold mb-0">{{ $order->order_number }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Status</label>
                            <p class="mb-0">
                                <span class="badge {{ $order->getStatusBadgeClass() }} fs-6">
                                    {{ ucfirst($order->status) }}
                                </span>
                            </p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Total Items</label>
                            <p class="fw-bold mb-0">{{ $order->total_items }}</p>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Total Amount</label>
                            <p class="fw-bold text-success mb-0 fs-4">${{ number_format($order->total_amount, 2) }}</p>
                        </div>
                        @if($order->notes)
                            <div class="col-12 mb-3">
                                <label class="form-label text-muted">Notes</label>
                                <div class="border rounded p-3 bg-light">
                                    <p class="mb-0">{{ $order->notes }}</p>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Order Items -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Order Items</h6>
                </div>
                <div class="card-body">
                    @if($order->orderItems && $order->orderItems->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>Product</th>
                                        <th>Price</th>
                                        <th>Quantity</th>
                                        <th>Total</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($order->orderItems as $item)
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    @if($item->product && $item->product->image)
                                                        <img src="{{ asset('storage/' . $item->product->image) }}" 
                                                             alt="{{ $item->product->name ?? 'Product' }}" 
                                                             class="rounded me-3" 
                                                             style="width: 50px; height: 50px; object-fit: cover;">
                                                    @else
                                                        <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                             style="width: 50px; height: 50px;">
                                                            <i class="fas fa-image text-muted"></i>
                                                        </div>
                                                    @endif
                                                    <div>
                                                        @if($item->product)
                                                            <div class="fw-bold">{{ $item->product->name }}</div>
                                                            @if($item->product->category)
                                                                <small class="text-muted">{{ $item->product->category }}</small>
                                                            @endif
                                                        @else
                                                            <div class="text-muted">Product no longer available</div>
                                                        @endif
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-success fw-bold">${{ number_format($item->price, 2) }}</td>
                                            <td>
                                                <span class="badge bg-secondary">{{ $item->quantity }}</span>
                                            </td>
                                            <td class="text-primary fw-bold">${{ number_format($item->total, 2) }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <th colspan="3" class="text-end">Total:</th>
                                        <th class="text-primary">${{ number_format($order->total_amount, 2) }}</th>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-box-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No items found for this order.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Customer Information -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Customer Information</h6>
                </div>
                <div class="card-body">
                    <div class="text-center mb-3">
                        <div class="bg-primary bg-opacity-10 text-primary rounded-circle d-inline-flex align-items-center justify-content-center" 
                             style="width: 60px; height: 60px;">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                    </div>
                    <div class="text-center">
                        <h6 class="fw-bold">{{ $order->user->name }}</h6>
                        <p class="text-muted mb-2">{{ $order->user->email }}</p>
                        <span class="badge {{ $order->user->isAdmin() ? 'bg-warning' : 'bg-success' }}">
                            {{ ucfirst($order->user->role) }}
                        </span>
                    </div>
                    
                    <hr class="my-3">
                    
                    <div class="mb-2">
                        <small class="text-muted">Member Since</small>
                        <div class="fw-bold">{{ $order->user->created_at->format('M d, Y') }}</div>
                    </div>
                    
                    @php
                        $userOrdersCount = $order->user->orders()->count();
                        $userTotalSpent = $order->user->orders()->where('status', '!=', 'cancelled')->sum('total_amount');
                    @endphp
                    
                    <div class="mb-2">
                        <small class="text-muted">Total Orders</small>
                        <div class="fw-bold">{{ $userOrdersCount }}</div>
                    </div>
                    
                    <div class="mb-3">
                        <small class="text-muted">Total Spent</small>
                        <div class="fw-bold text-success">${{ number_format($userTotalSpent, 2) }}</div>
                    </div>
                </div>
            </div>

            <!-- Order Timeline -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Order Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="mb-1">Order Placed</h6>
                                <small class="text-muted">{{ $order->created_at->format('M d, Y \a\t g:i A') }}</small>
                            </div>
                        </div>
                        
                        @if($order->status !== 'pending')
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <h6 class="mb-1">Status: {{ ucfirst($order->status) }}</h6>
                                    <small class="text-muted">{{ $order->updated_at->format('M d, Y \a\t g:i A') }}</small>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Order Management Actions -->
    @if(Auth::user()->canManageOrders())
        <div class="row mt-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white border-bottom">
                        <h6 class="mb-0">Order Management Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-flex gap-2 flex-wrap">
                            <a href="{{ route('orders.edit', $order) }}" class="btn btn-warning">
                                <i class="fas fa-edit me-2"></i>Edit Order
                            </a>
                            
                            @if($order->status === 'pending')
                                <form action="{{ route('orders.update', $order) }}" method="POST" class="d-inline order-update-form">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="paid">
                                    <input type="hidden" name="notes" value="{{ $order->notes }}">
                                    <button type="submit" class="btn btn-success" onclick="return confirmStatusUpdate('paid')">
                                        <i class="fas fa-check me-2"></i>Mark as Paid
                                    </button>
                                </form>
                            @endif
                            
                            @if($order->status === 'paid')
                                <form action="{{ route('orders.update', $order) }}" method="POST" class="d-inline order-update-form">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="shipped">
                                    <input type="hidden" name="notes" value="{{ $order->notes }}">
                                    <button type="submit" class="btn btn-info" onclick="return confirmStatusUpdate('shipped')">
                                        <i class="fas fa-shipping-fast me-2"></i>Mark as Shipped
                                    </button>
                                </form>
                            @endif
                            
                            @if($order->status === 'shipped')
                                <form action="{{ route('orders.update', $order) }}" method="POST" class="d-inline order-update-form">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="delivered">
                                    <input type="hidden" name="notes" value="{{ $order->notes }}">
                                    <button type="submit" class="btn btn-primary" onclick="return confirmStatusUpdate('delivered')">
                                        <i class="fas fa-box me-2"></i>Mark as Delivered
                                    </button>
                                </form>
                            @endif
                            
                            <form action="{{ route('orders.destroy', $order) }}" method="POST" class="d-inline" 
                                  onsubmit="return confirm('Are you sure you want to delete this order? This action cannot be undone.')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-2"></i>Delete Order
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <style>
        .timeline {
            position: relative;
            padding: 0;
        }
        
        .timeline-item {
            position: relative;
            padding-left: 30px;
            margin-bottom: 20px;
        }
        
        .timeline-item:not(:last-child)::after {
            content: '';
            position: absolute;
            left: 7px;
            top: 20px;
            height: calc(100% + 10px);
            width: 2px;
            background-color: #dee2e6;
        }
        
        .timeline-marker {
            position: absolute;
            left: 0;
            top: 5px;
            width: 15px;
            height: 15px;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 0 2px #dee2e6;
        }
        
        .timeline-content h6 {
            margin-bottom: 4px;
            font-size: 0.9rem;
        }
        
        .timeline-content small {
            font-size: 0.8rem;
        }
    </style>

    <script>
        function confirmStatusUpdate(status) {
            const statusLabels = {
                'paid': 'Paid',
                'shipped': 'Shipped',
                'delivered': 'Delivered',
                'cancelled': 'Cancelled'
            };
            
            return confirm(`Are you sure you want to mark this order as ${statusLabels[status]}?`);
        }

        // Handle form submission errors gracefully
        document.addEventListener('DOMContentLoaded', function() {
            const forms = document.querySelectorAll('.order-update-form');
            
            forms.forEach(form => {
                form.addEventListener('submit', function(e) {
                    const button = form.querySelector('button[type="submit"]');
                    const originalText = button.innerHTML;
                    
                    // Disable button and show loading
                    button.disabled = true;
                    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Processing...';
                    
                    // Check if CSRF token exists
                    const csrfToken = form.querySelector('input[name="_token"]');
                    if (!csrfToken || !csrfToken.value) {
                        e.preventDefault();
                        alert('Security token expired. Please refresh the page and try again.');
                        button.disabled = false;
                        button.innerHTML = originalText;
                        return false;
                    }
                    
                    // Re-enable button after 5 seconds if no response
                    setTimeout(() => {
                        if (button.disabled) {
                            button.disabled = false;
                            button.innerHTML = originalText;
                        }
                    }, 5000);
                });
            });
            
            // Refresh CSRF token every 30 minutes
            setInterval(function() {
                fetch('/csrf-refresh', {
                    method: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.token) {
                        document.querySelectorAll('input[name="_token"]').forEach(input => {
                            input.value = data.token;
                        });
                    }
                })
                .catch(error => {
                    console.log('CSRF token refresh failed:', error);
                    // Show warning to user
                    showSessionWarning();
                });
            }, 30 * 60 * 1000); // 30 minutes
            
            // Show session warning 5 minutes before session expires (115 minutes)
            setTimeout(function() {
                showSessionWarning();
            }, 115 * 60 * 1000);
        });
        
        function showSessionWarning() {
            if (document.querySelector('.session-warning')) return; // Don't show multiple warnings
            
            const warning = document.createElement('div');
            warning.className = 'alert alert-warning alert-dismissible fade show session-warning';
            warning.style.cssText = 'position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;';
            warning.innerHTML = `
                <strong>Session Warning:</strong> Your session will expire soon. Save any changes and refresh the page to avoid being logged out.
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            
            document.body.appendChild(warning);
            
            // Auto-remove after 10 seconds
            setTimeout(() => {
                if (warning.parentNode) {
                    warning.remove();
                }
            }, 10000);
        }
    </script>
</x-app-layout>