<x-guest-layout>
    <div class="text-center mb-4">
        <h4 class="fw-bold mb-2">Reset Password</h4>
        <p class="text-muted">Set your new password here.</p>
    </div>

    <form method="POST" action="{{ route('password.store') }}">
        @csrf

        <!-- Password Reset Token -->
        <input type="hidden" name="token" value="{{ $request->route('token') }}">

        <!-- Employee Number -->
        <div class="mb-3">
            <label for="emp_no" class="form-label fw-medium">Employee Number</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-id-card text-muted"></i>
                </span>
                <input id="emp_no" 
                       type="text" 
                       class="form-control border-start-0 @error('emp_no') is-invalid @enderror" 
                       name="emp_no" 
                       value="{{ old('emp_no', $request->emp_no) }}" 
                       required 
                       autofocus 
                       autocomplete="username"
                       placeholder="Enter your employee number">
            </div>
            @error('emp_no')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Password -->
        <div class="mb-3">
            <label for="password" class="form-label fw-medium">New Password</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-lock text-muted"></i>
                </span>
                <input id="password" 
                       type="password" 
                       class="form-control border-start-0 @error('password') is-invalid @enderror" 
                       name="password" 
                       required 
                       autocomplete="new-password"
                       placeholder="Enter new password">
            </div>
            @error('password')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Confirm Password -->
        <div class="mb-4">
            <label for="password_confirmation" class="form-label fw-medium">Confirm Password</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-lock text-muted"></i>
                </span>
                <input id="password_confirmation" 
                       type="password" 
                       class="form-control border-start-0 @error('password_confirmation') is-invalid @enderror" 
                       name="password_confirmation" 
                       required 
                       autocomplete="new-password"
                       placeholder="Confirm new password">
            </div>
            @error('password_confirmation')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Reset Button -->
        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-primary btn-lg py-2">
                <i class="fas fa-key me-2"></i>
                Reset Password
            </button>
        </div>
    </form>

    <!-- Login Link -->
    <div class="text-center">
        <p class="mb-0 text-muted">
            Don't want to reset? 
            <a href="{{ route('login') }}" class="text-decoration-none text-primary fw-medium">
                Login Here
            </a>
        </p>
    </div>
</x-guest-layout>
