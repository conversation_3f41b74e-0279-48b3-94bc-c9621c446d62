/* Endtime Create Page Styling */

/* Header Section */
.endtime-header {
    background: #4f46e5;
    color: white;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0;
}

.endtime-title {
    font-size: 16px;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 8px;
}

.endtime-actions {
    display: flex;
    gap: 8px;
}

.btn-dashboard, .btn-back, .btn-submit {
    padding: 6px 12px;
    border-radius: 4px;
    text-decoration: none;
    font-size: 13px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
}

.btn-dashboard {
    background: #f59e0b;
    color: white;
}

.btn-back {
    background: #6b7280;
    color: white;
}

.btn-submit {
    background: #10b981;
    color: white;
}

.btn-dashboard:hover, .btn-back:hover, .btn-submit:hover {
    opacity: 0.9;
    transform: translateY(-1px);
    color: white;
}

/* Form Container */
#newLotForm {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 0 0 8px 8px;
    padding: 20px;
}

/* Lot Information Section */
.lot-info-section {
    margin-bottom: 25px;
}

.section-header {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    padding: 10px 15px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 600;
    color: #475569;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* Horizontal Layout for Lot Form */
.lot-form-row {
    display: grid;
    grid-template-columns: 200px 140px 120px 200px 40px 200px;
    gap: 15px 20px;
    align-items: start;
    width: 100%;
    max-width: 1200px;
}

.lot-field {
    display: flex;
    flex-direction: column;
    min-width: 0;
    position: relative;
}

/* Empty spacer for separation between Lot Details and Calculation Results */
.lot-field:nth-child(5) {
    width: 40px;
    min-width: 40px;
}

/* Calculation Results is now the 6th child */
.lot-field:nth-child(6) {
    grid-column: 6;
}

.lot-field label {
    font-size: 12px;
    font-weight: 600;
    color: #374151;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Lot ID Input */
.lot-id-input {
    display: flex;
    gap: 6px;
    align-items: stretch;
    width: 100%;
}

.lot-id-input input {
    flex: 1;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 700;
    text-align: center;
    letter-spacing: 1px;
    background: #f9fafb;
    min-width: 0;
    height: 38px;
    box-sizing: border-box;
}

.search-btn {
    background: #4f46e5;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 0 12px;
    cursor: pointer;
    transition: all 0.2s;
    height: 38px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    min-width: 38px;
}

.search-btn:hover {
    background: #4338ca;
}

/* Lot Qty Display */
.lot-qty-display {
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 6px;
    padding: 12px 10px;
    text-align: center;
    width: 100%;
    box-sizing: border-box;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.qty-number {
    font-size: 15px;
    font-weight: 700;
    color: #0c4a6e;
    margin-bottom: 3px;
    line-height: 1.2;
}

.qty-label {
    font-size: 10px;
    color: #64748b;
    text-transform: uppercase;
    line-height: 1;
}

/* Lot Type Options */
.lot-type-options {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.type-option {
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
}

.type-option input[type="radio"] {
    margin: 0;
}

.type-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    letter-spacing: 0.5px;
    min-width: 45px;
    text-align: center;
}

.type-badge.main {
    background: #4f46e5;
    color: white;
}

.type-badge.rlly {
    background: #f59e0b;
    color: #92400e;
}

/* Lot Details */
.lot-details {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
    padding: 6px 10px;
    width: 100%;
    box-sizing: border-box;
    min-height: 60px;
}

.detail-item {
    font-size: 11px;
    color: #64748b;
    margin-bottom: 1px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    line-height: 1.1;
    height: 14px;
}

.detail-item:last-child {
    margin-bottom: 0;
}

.detail-item span {
    font-weight: 600;
    color: #374151;
    text-align: right;
    flex-shrink: 0;
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Calculation Results */
.calculation-result {
    background: #f0fdf4;
    border: 1px solid #22c55e;
    border-radius: 6px;
    padding: 15px 12px;
    width: 100%;
    box-sizing: border-box;
    min-height: 100px;
    display: flex;
    flex-direction: column;
}

.calc-header {
    font-size: 11px;
    font-weight: 600;
    color: #166534;
    margin-bottom: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    justify-content: center;
}

.calc-content {
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.end-time {
    font-size: 16px;
    font-weight: 700;
    color: #15803d;
    margin-bottom: 6px;
    line-height: 1.2;
}

.time-diff {
    font-size: 12px;
    color: #16a34a;
    font-weight: 500;
    line-height: 1;
}

/* Equipment Section */
.equipment-section {
    margin-bottom: 25px;
}

.section-header-with-btn {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    padding: 10px 15px;
    border-radius: 6px;
    margin-bottom: 15px;
}

.section-title {
    font-size: 14px;
    font-weight: 600;
    color: #475569;
    display: flex;
    align-items: center;
    gap: 8px;
}

.mc-count {
    background: #4f46e5;
    color: white;
    padding: 2px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: 600;
    margin-left: 8px;
}

.add-equipment-btn {
    background: transparent;
    border: 1px solid #4f46e5;
    color: #4f46e5;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s;
}

.add-equipment-btn:hover {
    background: #4f46e5;
    color: white;
}

/* Equipment Table */
.equipment-table {
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    overflow: visible; /* Changed from hidden to visible for dropdown */
}

.equipment-table table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.equipment-table th {
    background: #f9fafb;
    color: #374151;
    padding: 10px 12px;
    text-align: left;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-bottom: 1px solid #e5e7eb;
}

.equipment-table td {
    padding: 10px 12px;
    border-bottom: 1px solid #f3f4f6;
    vertical-align: middle;
    position: relative; /* Added for dropdown positioning */
    overflow: visible; /* Allow dropdown to show outside cell */
}

.equipment-table tbody tr:last-child td {
    border-bottom: none;
}

.row-number {
    font-weight: 700;
    color: #4f46e5;
    text-align: center;
    font-size: 14px;
}

.equipment-search {
    width: 100%;
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
}

.ng-input {
    display: flex;
    align-items: center;
    gap: 4px;
}

.ng-input input {
    width: 60px;
    padding: 6px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
    text-align: center;
}

.ng-input span {
    font-size: 12px;
    color: #6b7280;
}

.time-input {
    display: flex;
    gap: 4px;
    align-items: center;
}

.time-input input {
    flex: 1;
    padding: 6px 10px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
}

.time-now-btn {
    background: transparent;
    border: 1px solid #6b7280;
    color: #6b7280;
    padding: 6px 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.time-now-btn:hover {
    background: #6b7280;
    color: white;
}

.equipment-capacity, .equipment-endtime {
    text-align: center;
    color: #6b7280;
    font-size: 14px;
}

/* EST. END column styling */
.est-end-time {
    text-align: center;
    line-height: 1.2;
}

.end-datetime {
    font-weight: 600;
    color: #059669 !important;
}

.remaining-time {
    color: #0891b2 !important;
    font-style: italic;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding-top: 20px;
    border-top: 1px solid #e5e7eb;
    margin-top: 20px;
}

.btn-cancel, .btn-save {
    padding: 10px 20px;
    border-radius: 6px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.2s;
    border: none;
}

.btn-cancel {
    background: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
}

.btn-cancel:hover {
    background: #e5e7eb;
    color: #374151;
}

.btn-save {
    background: #4f46e5;
    color: white;
}

.btn-save:hover {
    background: #4338ca;
}

/* Equipment Search Dropdown Styles */
.equipment-search-wrapper {
    position: relative;
    width: 100%;
}

.search-input-container {
    position: relative;
}

.search-input-container .search-icon {
    position: absolute;
    left: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #6b7280;
    z-index: 2;
    font-size: 12px;
}

.loading-spinner {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    color: #4f46e5;
    z-index: 2;
}

.equipment-search {
    width: 100%;
    padding: 6px 30px 6px 25px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 13px;
    transition: all 0.3s ease;
    background: white;
}

.equipment-search:focus {
    border-color: #4f46e5;
    box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.1);
    outline: none;
}

.equipment-search.selected {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.02);
}

/* Modern Equipment Dropdown */
.equipment-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    z-index: 9999; /* Increased z-index to appear above table */
    margin-top: 4px;
    overflow: hidden;
    max-height: 300px;
    min-width: 250px; /* Minimum width for better visibility */
}

.dropdown-header {
    padding: 8px 12px;
    background: #f8fafc;
    border-bottom: 1px solid #e5e7eb;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.dropdown-title {
    font-weight: 600;
    color: #374151;
    font-size: 12px;
}

.results-count {
    font-size: 11px;
    color: #6b7280;
}

.dropdown-body {
    max-height: 200px;
    overflow-y: auto;
}

.equipment-option {
    padding: 10px 12px;
    border-bottom: 1px solid #f3f4f6;
    cursor: pointer;
    transition: all 0.2s ease;
}

.equipment-option:hover {
    background: #f3f4f6;
}

.equipment-option.selected {
    background: #f3f4f6;
}

.equipment-option:last-child {
    border-bottom: none;
}

.equipment-option-main {
    font-weight: 600;
    color: #374151;
    font-size: 13px;
    margin-bottom: 2px;
}

.equipment-option-details {
    font-size: 11px;
    color: #6b7280;
    line-height: 1.3;
}

.no-results {
    padding: 12px;
    text-align: center;
    color: #6b7280;
    font-size: 12px;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 1200px) {
    .lot-form-row {
        grid-template-columns: 150px 130px 110px 200px 160px;
        gap: 15px;
        max-width: 900px;
    }
    
    .lot-field:nth-child(5) {
        width: 30px;
        min-width: 30px;
    }
}

@media (max-width: 900px) {
    .lot-form-row {
        grid-template-columns: 1fr 1fr;
        grid-template-rows: auto auto auto;
        gap: 20px;
        max-width: 100%;
    }
    
    .lot-field:nth-child(5) {
        display: none; /* Hide spacer on smaller screens */
    }
    
    .lot-field:nth-child(6) {
        grid-column: 1 / -1;
    }
}

@media (max-width: 768px) {
    .endtime-header {
        flex-direction: column;
        gap: 10px;
        text-align: center;
        padding: 15px;
    }
    
    .endtime-actions {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .lot-form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .lot-field:nth-child(5) {
        display: none; /* Hide spacer on mobile */
    }
    
    .lot-field:nth-child(6) {
        grid-column: 1;
    }
    
    #newLotForm {
        padding: 15px;
    }
}
