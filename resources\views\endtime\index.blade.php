<x-app-layout>
    <x-slot name="header">
        ENDTIME | SUBMITTED
    </x-slot>



    <!-- Success/Error Messages -->
    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><strong>Validation Errors:</strong>
            <ul class="mb-0 mt-2">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- Merged Filter & Action Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="merged-controls-wrapper">
                <!-- Unified Filter Card -->
                <div class="card border-0 shadow-sm unified-filter-card">
                    <div class="card-header bg-gradient-primary text-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h6 class="mb-0 me-3">
                                    <i class="fas fa-filter me-2"></i>
                                    <span id="filter-toggle" onclick="toggleFilters()" style="cursor: pointer;">Filters & Controls</span>
                                </h6>
                                <!-- Quick Time Range Display -->
                                <div class="time-range-quick-display">
                                    <span class="badge bg-light text-primary px-3 py-2" id="cutoff_range_badge">Loading...</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-3">
                                <!-- Summary Stats -->
                                <div class="filter-stats d-flex gap-2">
                                    <div class="stat-badge">
                                        <span class="badge bg-light text-primary px-2 py-1">
                                            <i class="fas fa-list-ol me-1"></i>
                                            <strong id="total-lots-count">{{ $stats['total_filtered_lots'] ?? 0 }}</strong>
                                            <small>LOTS</small>
                                        </span>
                                    </div>
                                    <div class="stat-badge">
                                        <span class="badge bg-light text-success px-2 py-1">
                                            <i class="fas fa-cogs me-1"></i>
                                            <strong id="ideal-equipment-count">{{ $stats['ideal_equipment_count'] ?? 0 }}</strong>
                                            <small>EQP</small>
                                        </span>
                                    </div>
                                </div>


                                <!-- Action Buttons -->
                                <div class="action-buttons-compact d-flex gap-2">
                                    <a href="{{ route('dashboard') }}" class="btn btn-warning btn-sm nav-button action-btn-labeled" title="Dashboard">
                                        <i class="fas fa-home me-1"></i>
                                        <span>Dashboard</span>
                                    </a>
                                    <a href="{{ route('endtime.create') }}" class="btn btn-primary btn-sm action-btn-labeled" title="Create New Lot">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        <span>ADD ENDTIME Lot</span>
                                    </a>
                                    <a href="{{ route('endtime.submit.show') }}" class="btn btn-success btn-sm action-btn-labeled" title="Submit Ongoing Lot">
                                        <i class="fas fa-check-circle me-1"></i>
                                        <span>SUBMIT Lot</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Collapsible Filter Controls -->
                    <div id="filter-controls" class="collapse show">
                        <div class="card-body bg-light py-3">
                            <form method="GET" action="{{ route('endtime.index') }}" id="endtime-filters-form">
                                <!-- Ultra Compact Single Row Filter -->
                                <div class="single-row-filter">
                                    <!-- Production Schedule Group -->
                                    <div class="filter-group schedule-group">
                                        <div class="group-badge schedule-badge">
                                            <i class="fas fa-clock me-2"></i>
                                            <span></span>
                                        </div>
                                        <div class="group-controls">
                                            <div class="mini-group">
                                                <label>Date</label>
                                                <input type="date" id="cutoff_date_picker" class="form-control form-control-sm mini-input"
                                                       value="{{ request('cutoff_date', date('Y-m-d')) }}"
                                                       onchange="updateCutoffFilter()">
                                            </div>
                                            <div class="mini-group">
                                                <label>Shift</label>
                                                <select id="shift_selector" class="form-select form-select-sm mini-input"
                                                        onchange="updateCutoffFilter()">
                                                    <option value="all" {{ request('cutoff_shift', 'day') === 'all' ? 'selected' : '' }}>All</option>
                                                    <option value="day" {{ request('cutoff_shift', 'day') === 'day' ? 'selected' : '' }}>Day</option>
                                                    <option value="night" {{ request('cutoff_shift', 'day') === 'night' ? 'selected' : '' }}>Night</option>
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Period</label>
                                                <select id="cutoff_period_selector" class="form-select form-select-sm mini-input"
                                                        onchange="updateCutoffFilter()">
                                                    <option value="1" {{ request('cutoff_period', '1') === '1' ? 'selected' : '' }}>1st</option>
                                                    <option value="2" {{ request('cutoff_period', '1') === '2' ? 'selected' : '' }}>2nd</option>
                                                    <option value="3" {{ request('cutoff_period', '1') === '3' ? 'selected' : '' }}>3rd</option>
                                                    <option value="all" {{ request('cutoff_period', '1') === 'all' ? 'selected' : '' }}>All</option>
                                                </select>
                                            </div>
                                            <div class="mini-group range-display">
                                                <label>Range</label>
                                                <div class="mini-range" id="shift_description">Day - 1st</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Divider -->
                                    <div class="filter-divider"></div>

                                    <!-- Equipment & Status Group -->
                                    <div class="filter-group equipment-group">
                                        <div class="group-badge equipment-badge">
                                            <i class="fas fa-cogs me-2"></i>
                                            <span></span>
                                        </div>
                                        <div class="group-controls">
                                            <div class="mini-group">
                                                <label>Line</label>
                                                <select name="eqp_line" id="eqp_line" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['equipment_lines']))
                                                        @foreach($filterOptions['equipment_lines'] as $line)
                                                            <option value="{{ $line }}" {{ (isset($filters['eqp_line']) && $filters['eqp_line'] === $line) ? 'selected' : '' }}>{{ $line }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Area</label>
                                                <select name="eqp_area" id="eqp_area" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['equipment_areas']))
                                                        @foreach($filterOptions['equipment_areas'] as $area)
                                                            <option value="{{ $area }}" {{ (isset($filters['eqp_area']) && $filters['eqp_area'] === $area) ? 'selected' : '' }}>{{ $area }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Work</label>
                                                <select name="work_type" id="work_type" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['work_types']))
                                                        @foreach($filterOptions['work_types'] as $type)
                                                            <option value="{{ $type }}" {{ (isset($filters['work_type']) && $filters['work_type'] === $type) ? 'selected' : '' }}>{{ $type }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Lot</label>
                                                <select name="lot_type" id="lot_type" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['lot_types']))
                                                        @foreach($filterOptions['lot_types'] as $type)
                                                            <option value="{{ $type }}" {{ (isset($filters['lot_type']) && $filters['lot_type'] === $type) ? 'selected' : '' }}>{{ $type }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Status</label>
                                                <select name="status" id="status" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    @if(isset($filterOptions['statuses']))
                                                        @foreach($filterOptions['statuses'] as $status)
                                                            <option value="{{ $status }}" {{ (isset($filters['status']) && $filters['status'] === $status) ? 'selected' : '' }}>{{ $status }}</option>
                                                        @endforeach
                                                    @endif
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Filter Action Buttons -->
                                    <div class="filter-actions-inline">
                                        <button type="button" class="btn btn-primary btn-sm filter-btn" onclick="applyCutoffFilter()" title="Apply Filters">
                                            <i class="fas fa-search me-1"></i>Apply
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm filter-btn" onclick="resetAllFilters()" title="Reset All">
                                            <i class="fas fa-undo me-1"></i>Reset
                                        </button>
                                    </div>
                                </div>

                                <!-- Hidden fields for cutoff parameters -->
                                <input type="hidden" name="cutoff_date" id="cutoff_date" value="{{ request('cutoff_date', date('Y-m-d')) }}">
                                <input type="hidden" name="cutoff_shift" id="cutoff_shift" value="{{ request('cutoff_shift', 'day') }}">
                                <input type="hidden" name="cutoff_period" id="cutoff_period" value="{{ request('cutoff_period', '1') }}">
                                <input type="hidden" name="search" id="hidden_search" value="{{ $filters['search'] ?? '' }}">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!-- Lots Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Forecasted End Times</h6>
        </div>


        <!-- Lots Table -->
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Lot ID</th>
                            <th>Model</th>
                            <th>Size/Qty</th>
                            <th>Work Type</th>
                            <th>Lot Type</th>
                            <th>Equipment</th>
                            <th>Line/Area</th>
                            <th>Est. End Time</th>
                            <th>Status</th>
                            <th>Result</th>
                            <th>LIPAS</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @forelse($endtimeLots as $lot)
                        <tr class="{{ $lot->est_endtime < now() ? 'table-danger' : ($lot->est_endtime < now()->addHours(2) ? 'table-warning' : '') }}">
                            <td>
                                <strong>{{ $lot->lot_id }}</strong>
                            </td>
                            <td>{{ $lot->model_15 }}</td>
                            <td>
                                <span class="badge bg-secondary">{{ $lot->lot_size }}</span>
                                <br><small class="text-muted">Qty: {{ number_format($lot->lot_qty) }}</small>
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->work_type === 'PROD' ? 'primary' : 'secondary' }}">
                                    {{ $lot->work_type }}
                                </span>
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->lot_type === 'HOT' ? 'danger' : ($lot->lot_type === 'SUPER_HOT' ? 'warning' : 'info') }}">
                                    {{ $lot->lot_type }}
                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    {{ $lot->eqp_1 }}
                                    @if($lot->eqp_2), {{ $lot->eqp_2 }}@endif
                                    @if($lot->eqp_3), {{ $lot->eqp_3 }}@endif
                                    @if($lot->eqp_4), {{ $lot->eqp_4 }}@endif
                                    @if($lot->eqp_5), {{ $lot->eqp_5 }}@endif
                                    @if($lot->eqp_6), {{ $lot->eqp_6 }}@endif
                                    @if($lot->eqp_7), {{ $lot->eqp_7 }}@endif
                                    @if($lot->eqp_8), {{ $lot->eqp_8 }}@endif
                                    @if($lot->eqp_9), {{ $lot->eqp_9 }}@endif
                                    @if($lot->eqp_10), {{ $lot->eqp_10 }}@endif
                                </small>
                            </td>
                            <td>
                                <strong>{{ $lot->eqp_line }}</strong>
                                <br><small class="text-muted">{{ $lot->eqp_area }}</small>
                            </td>
                            <td>
                                <div class="text-{{ $lot->est_endtime < now() ? 'danger' : ($lot->est_endtime < now()->addHours(2) ? 'warning' : 'success') }}">
                                    <strong>{{ $lot->est_endtime->format('M d, H:i') }}</strong>
                                    <br>
                                    <small>{{ $lot->est_endtime->diffForHumans() }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-{{
                                    $lot->status === 'Submitted' ? 'success' :
                                    ($lot->status === 'Ongoing' ? 'primary' :
                                    ($lot->status === 'COMPLETED' ? 'info' :
                                    ($lot->status === 'MAINTENANCE' ? 'danger' : 'secondary')))
                                }}">
                                    {{ $lot->status }}
                                </span>
                                @if($lot->status === 'Submitted' && $lot->actual_submitted_at)
                                    <br><small class="text-muted">Submitted: {{ $lot->actual_submitted_at->format('M d, H:i') }}</small>
                                @endif
                            </td>
                            <td>
                                @if($lot->remarks)
                                    <span class="badge bg-{{
                                        $lot->remarks === 'Early' ? 'success' :
                                        ($lot->remarks === 'Delayed' ? 'danger' : 'primary')
                                    }}">
                                        {{ $lot->remarks }}
                                    </span>
                                    @if($lot->actual_submitted_at)
                                        <br><small class="text-muted">Actual: {{ $lot->actual_submitted_at->format('M d, H:i') }}</small>
                                    @endif
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-{{ $lot->lipas_yn === 'Y' ? 'success' : 'secondary' }}">
                                    {{ $lot->lipas_yn === 'Y' ? 'Yes' : 'No' }}
                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <!-- View Details - Available to all authenticated users -->
                                    <button type="button" class="btn btn-sm btn-outline-info"
                                            onclick="viewLot({{ $lot->id }})"
                                            title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>

                                    <!-- Edit/Modify - Available to all authenticated users (USER, MANAGER, ADMIN) -->
                                    @if(Auth::user()->isUser() || Auth::user()->isManager() || Auth::user()->isAdmin())
                                    <button type="button" class="btn btn-sm btn-outline-warning"
                                            onclick="editLot({{ $lot->id }})"
                                            title="Edit/Modify">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    @endif

                                    <!-- Delete - Available to Managers and Admins only -->
                                    @if(Auth::user()->isManager() || Auth::user()->isAdmin())
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteLot({{ $lot->id }}, '{{ $lot->lot_id }}')"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @empty
                        <tr>
                            <td colspan="12" class="text-center py-4">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No forecasted end times found for the selected criteria</h6>
                            </td>
                        </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>
        </div>
        @if($endtimeLots->hasPages())
        <div class="card-footer bg-light">
            {{ $endtimeLots->links() }}
        </div>
        @endif
    </div>


    <!-- JavaScript Integration -->
    <script>
        // Inject available equipment data for JavaScript consumption
        window.availableEquipment = @json($availableEquipment ?? []);

        // Debug equipment data
        console.log('Equipment data injected:', {
            count: window.availableEquipment.length,
            sample: window.availableEquipment.slice(0, 2)
        });

        // Add global calculation function for manual testing
        window.triggerCalculation = function() {
            console.log('=== Manual Calculation Trigger ===');
            if (typeof calculateEndtime === 'function') {
                calculateEndtime();
            } else {
                console.error('calculateEndtime function not available');
            }
        };
    </script>

    <!-- Include Endtime JavaScript -->
    <script src="{{ asset('js/endtime.js') }}"></script>

    <!-- Additional JavaScript for specific functions not in main file -->
    <script>
        // Functions that need to be global for inline handlers

        /**
         * View lot details
         */
        function viewLot(lotId) {
            // Implementation for viewing lot details
            console.log('View lot:', lotId);
            // This could redirect to a detail page
        }

        /**
         * Edit lot
         */
        function editLot(lotId) {
            // Implementation for editing lot
            console.log('Edit lot:', lotId);
            // This could redirect to edit page
        }

        /**
         * Delete lot
         */
        function deleteLot(lotId, lotIdString) {
            if (confirm(`Are you sure you want to delete lot ${lotIdString}?`)) {
                // Implementation for deleting lot
                console.log('Delete lot:', lotId);
                // Make AJAX call to delete endpoint
                fetch(`/endtime/${lotId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast(data.message || 'Failed to delete lot', 'error');
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    showToast('Failed to delete lot', 'error');
                });
            }
        }

        /**
         * Filter and utility functions from existing inline scripts
         */

        // Initialize existing filter functions if they don't exist
        if (typeof toggleFilters === 'undefined') {
            function toggleFilters() {
                const filterControls = document.getElementById('filter-controls');
                if (filterControls) {
                    if (filterControls.classList.contains('show')) {
                        filterControls.classList.remove('show');
                    } else {
                        filterControls.classList.add('show');
                    }
                }
            }
        }

        if (typeof updateCutoffFilter === 'undefined') {
            function updateCutoffFilter() {
                // Update cutoff filter display
                const date = document.getElementById('cutoff_date_picker')?.value;
                const shift = document.getElementById('shift_selector')?.value;
                const period = document.getElementById('cutoff_period_selector')?.value;

                // Update hidden fields
                if (document.getElementById('cutoff_date')) document.getElementById('cutoff_date').value = date;
                if (document.getElementById('cutoff_shift')) document.getElementById('cutoff_shift').value = shift;
                if (document.getElementById('cutoff_period')) document.getElementById('cutoff_period').value = period;

                // Update range display
                updateShiftDescription();
            }
        }

        if (typeof updateShiftDescription === 'undefined') {
            function updateShiftDescription() {
                const shift = document.getElementById('shift_selector')?.value || 'day';
                const period = document.getElementById('cutoff_period_selector')?.value || '1';
                const shiftDesc = document.getElementById('shift_description');

                if (shiftDesc) {
                    let description = '';
                    if (shift === 'all') {
                        description = 'All Shifts';
                    } else if (shift === 'day') {
                        switch(period) {
                            case '1': description = 'Day - 1st (7AM-12PM)'; break;
                            case '2': description = 'Day - 2nd (12PM-4PM)'; break;
                            case '3': description = 'Day - 3rd (4PM-7PM)'; break;
                            case 'all': description = 'Day - All (7AM-7PM)'; break;
                        }
                    } else if (shift === 'night') {
                        switch(period) {
                            case '1': description = 'Night - 1st (7PM-12AM)'; break;
                            case '2': description = 'Night - 2nd (12AM-4AM)'; break;
                            case '3': description = 'Night - 3rd (4AM-7AM)'; break;
                            case 'all': description = 'Night - All (7PM-7AM)'; break;
                        }
                    }
                    shiftDesc.textContent = description;
                }
            }
        }

        if (typeof applyCutoffFilter === 'undefined') {
            function applyCutoffFilter() {
                updateCutoffFilter();
                document.getElementById('endtime-filters-form')?.submit();
            }
        }

        if (typeof resetAllFilters === 'undefined') {
            function resetAllFilters() {
                // Reset all filter inputs to defaults
                const form = document.getElementById('endtime-filters-form');
                if (form) {
                    form.reset();
                    // Set default values
                    const today = new Date().toISOString().split('T')[0];
                    if (document.getElementById('cutoff_date_picker')) document.getElementById('cutoff_date_picker').value = today;
                    if (document.getElementById('shift_selector')) document.getElementById('shift_selector').value = 'day';
                    if (document.getElementById('cutoff_period_selector')) document.getElementById('cutoff_period_selector').value = '1';

                    updateCutoffFilter();
                    form.submit();
                }
            }
        }

        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Set current cutoff as default
            setCurrentCutoffDefaults();

            // Initialize shift description
            updateShiftDescription();

            // Update cutoff range badge
            updateCutoffRangeBadge();
        });

        function updateCutoffRangeBadge() {
            const badge = document.getElementById('cutoff_range_badge');
            if (badge) {
                const date = document.getElementById('cutoff_date_picker')?.value || new Date().toISOString().split('T')[0];
                const shift = document.getElementById('shift_selector')?.value || 'day';
                const period = document.getElementById('cutoff_period_selector')?.value || '1';

                let badgeText = '';
                const dateObj = new Date(date);
                const formattedDate = dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

                if (shift === 'all') {
                    badgeText = `${formattedDate} - All Shifts`;
                } else {
                    const shiftText = shift.charAt(0).toUpperCase() + shift.slice(1);
                    const periodText = period === 'all' ? 'All' : `${period}${period === '1' ? 'st' : period === '2' ? 'nd' : 'rd'}`;
                    badgeText = `${formattedDate} - ${shiftText} ${periodText}`;
                }

                badge.textContent = badgeText;
            }
        }

        /**
         * Calculate and set current cutoff defaults based on Asia/Manila timezone
         * Shift Schedule:
         * - Day Shift: 07:00 - 18:59 (3 cutoffs: 7-12, 12-16, 16-19)
         * - Night Shift: 19:00 - 06:59 (3 cutoffs: 19-24, 0-4, 4-7)
         */
        function setCurrentCutoffDefaults() {
            // Get current time in Asia/Manila timezone
            const now = new Date();
            const manilaTime = new Date(now.toLocaleString("en-US", { timeZone: "Asia/Manila" }));
            const hours = manilaTime.getHours();
            const minutes = manilaTime.getMinutes();

            console.log('Current Manila time:', {
                time: manilaTime.toLocaleString(),
                hours: hours,
                minutes: minutes
            });

            let shift, cutoff, cutoffDate;

            if (hours >= 7 && hours <= 18) {
                // Day Shift: 07:00 - 18:59
                shift = 'day';
                cutoffDate = manilaTime; // Same day

                if (hours >= 7 && hours < 12) {
                    cutoff = '1'; // 07:00 - 11:59
                } else if (hours >= 12 && hours < 16) {
                    cutoff = '2'; // 12:00 - 15:59
                } else {
                    cutoff = '3'; // 16:00 - 18:59
                }
            } else {
                // Night Shift: 19:00 - 06:59
                shift = 'night';

                if (hours >= 19) {
                    // 19:00 - 23:59 (same day)
                    cutoff = '1';
                    cutoffDate = manilaTime; // Same day
                } else if (hours >= 0 && hours < 4) {
                    // 00:00 - 03:59 (next day, but shift started previous day)
                    cutoff = '2';
                    cutoffDate = new Date(manilaTime);
                    cutoffDate.setDate(cutoffDate.getDate() - 1); // Previous day
                } else {
                    // 04:00 - 06:59 (next day, but shift started previous day)
                    cutoff = '3';
                    cutoffDate = new Date(manilaTime);
                    cutoffDate.setDate(cutoffDate.getDate() - 1); // Previous day
                }
            }

            // Format date for input
            const dateString = cutoffDate.getFullYear() + '-' +
                String(cutoffDate.getMonth() + 1).padStart(2, '0') + '-' +
                String(cutoffDate.getDate()).padStart(2, '0');

            console.log('Calculated cutoff:', {
                shift: shift,
                cutoff: cutoff,
                date: dateString,
                description: `${shift} shift, ${cutoff}${cutoff === '1' ? 'st' : cutoff === '2' ? 'nd' : 'rd'} cutoff`
            });

            // Only set defaults if no URL parameters are present (fresh page load)
            const urlParams = new URLSearchParams(window.location.search);
            const hasDateParam = urlParams.has('cutoff_date');
            const hasShiftParam = urlParams.has('cutoff_shift');
            const hasPeriodParam = urlParams.has('cutoff_period');

            if (!hasDateParam && !hasShiftParam && !hasPeriodParam) {
                // Set the calculated values
                const dateInput = document.getElementById('cutoff_date_picker');
                const shiftSelect = document.getElementById('shift_selector');
                const periodSelect = document.getElementById('cutoff_period_selector');
                const hiddenDate = document.getElementById('cutoff_date');
                const hiddenShift = document.getElementById('cutoff_shift');
                const hiddenPeriod = document.getElementById('cutoff_period');

                if (dateInput) dateInput.value = dateString;
                if (shiftSelect) shiftSelect.value = shift;
                if (periodSelect) periodSelect.value = cutoff;
                if (hiddenDate) hiddenDate.value = dateString;
                if (hiddenShift) hiddenShift.value = shift;
                if (hiddenPeriod) hiddenPeriod.value = cutoff;

                console.log('✓ Default cutoff values set successfully');
            } else {
                console.log('URL parameters detected, keeping existing values');
            }
        }
    </script>
</x-app-layout>
