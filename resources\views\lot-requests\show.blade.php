<x-app-layout>
    <x-slot name="header">
        Lot Request Details
    </x-slot>

    <style>
        /* Clean page background */
        .lot-request-details {
            background: #f8f9ff;
            min-height: 100vh;
        }
        
        /* Enhanced cards */
        .detail-card {
            background: white;
            border: 1px solid #e1e8ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            transition: all 0.2s ease;
            overflow: hidden;
        }
        
        .detail-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        /* Card headers */
        .detail-card .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            color: white !important;
            border: none !important;
            padding: 1rem 1.5rem;
        }
        
        .detail-card .card-header h6 {
            margin: 0;
            font-weight: 600;
        }
        
        /* Table styling */
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .table thead th {
            background: #f8f9ff;
            color: #495057;
            font-weight: 600;
            border: none;
            padding: 0.75rem;
        }
        
        .table tbody tr:hover {
            background: #f8f9ff;
        }
        
        .table tbody td {
            border-top: 1px solid #e1e8ff;
            padding: 0.75rem;
        }
        
        .table tfoot td {
            background: #f8f9ff;
            border-top: 2px solid #667eea;
            padding: 0.75rem;
            font-weight: 600;
        }
        
        /* Enhanced buttons */
        .btn-outline-primary {
            border-color: #667eea;
            color: #667eea;
            transition: all 0.2s ease;
        }
        
        .btn-outline-primary:hover {
            background: #667eea;
            border-color: #667eea;
            transform: translateY(-1px);
        }
        
        .btn-outline-secondary {
            transition: all 0.2s ease;
        }
        
        .btn-outline-secondary:hover {
            transform: translateY(-1px);
        }
        
        /* Status badge styling */
        .status-badge {
            font-size: 1rem;
            padding: 0.5rem 1rem;
            border-radius: 20px;
        }
        
        /* Summary items */
        .summary-item {
            padding: 0.75rem 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .summary-item:last-child {
            border-bottom: none;
        }
        
        /* Fade-in animation */
        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Lot Request #{{ $lotRequest->request_number }}</h4>
                    <p class="text-muted mb-0">Request created on {{ $lotRequest->request_date->format('M d, Y \a\t H:i') }}</p>
                </div>
                <div class="d-flex gap-2">
                    @if(Auth::user()->canManageOrders())
                        <a href="{{ route('lot-requests.edit', $lotRequest) }}" class="btn btn-outline-primary">
                            <i class="fas fa-edit me-2"></i>Update
                        </a>
                    @endif
                    <a href="{{ route('lot-requests.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Lot Requests
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Equipment Items -->
            <div class="detail-card mb-4 fade-in">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-microchip me-2"></i>Equipment Items</h6>
                </div>
                <div class="card-body">
                    @if($lotRequest->lotRequestItems->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>Equipment Number</th>
                                        <th>Equipment Code</th>
                                        <th>Quantity (lots)</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($lotRequest->lotRequestItems as $item)
                                        <tr>
                                            <td>
                                                <div class="fw-medium">{{ $item->equipment_number }}</div>
                                                @if($item->equipment)
                                                    <small class="text-muted">
                                                        Line: {{ $item->equipment->eqp_line ?? 'N/A' }} | 
                                                        Area: {{ $item->equipment->eqp_area ?? 'N/A' }}
                                                    </small>
                                                @endif
                                            </td>
                                            <td>
                                                <span class="badge bg-info">{{ $item->equipment_code ?: 'N/A' }}</span>
                                            </td>
                                            <td>
                                                @php
                                                    $quantityDisplay = $lotRequest->getQuantityDisplayForItem($item);
                                                @endphp
                                                @if($quantityDisplay === 'shared')
                                                    <span class="badge bg-secondary">shared</span>
                                                    <small class="text-muted d-block">{{ $item->quantity }} lots</small>
                                                @else
                                                    <span class="fw-bold text-primary">{{ $item->quantity }}</span>
                                                @endif
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <td colspan="2" class="fw-bold">Sub-total</td>
                                        <td class="fw-bold text-primary">{{ $lotRequest->total_quantity }} lots</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-box-open fa-2x text-muted mb-3"></i>
                            <p class="text-muted">No equipment items found for this lot request.</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Assigned Lots -->
            @if($lotRequest->lotAssignments->count() > 0)
                <div class="detail-card mb-4 fade-in">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-check-circle me-2"></i>Assigned Lots</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>Lot Number</th>
                                        <th>Model</th>
                                        <th>Equipment</th>
                                        <th>Quantity</th>
                                        <th>WIP Status</th>
                                        <th>Assigned Date</th>
                                        <th>Assigned By</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($lotRequest->lotAssignments as $assignment)
                                        @php
                                            $wipDetail = $wipDetails[$assignment->lot_id] ?? null;
                                        @endphp
                                        <tr>
                                            <td>
                                                <div class="fw-medium" style="font-family: 'Courier New', monospace;">
                                                    {{ $assignment->lot_id }}
                                                </div>
                                                @if($wipDetail && $wipDetail->lot_location)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-map-marker-alt me-1"></i>{{ $wipDetail->lot_location }}
                                                    </small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($wipDetail && $wipDetail->model_15)
                                                    <span class="badge bg-info">{{ $wipDetail->model_15 }}</span>
                                                @else
                                                    <span class="text-muted">N/A</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="fw-medium">{{ $assignment->equipment_number }}</div>
                                                <small class="text-muted">{{ $assignment->equipment_code }}</small>
                                            </td>
                                            <td>
                                                <span class="badge bg-success">{{ number_format($assignment->lot_quantity) }}</span>
                                                @if($wipDetail && $wipDetail->stagnant_tat)
                                                    <small class="text-muted d-block">
                                                        <i class="fas fa-clock me-1"></i>{{ number_format($wipDetail->stagnant_tat, 1) }}d TAT
                                                    </small>
                                                @endif
                                            </td>
                                            <td>
                                                @if($wipDetail && $wipDetail->wip_status)
                                                    <span class="badge {{ $wipDetail->wip_status === 'Newlot Standby' ? 'bg-primary' : 'bg-info' }}">
                                                        {{ $wipDetail->wip_status }}
                                                    </span>
                                                    @if($wipDetail->auto_yn === 'Y' || $wipDetail->lipas_yn === 'Y')
                                                        <div class="mt-1">
                                                            @if($wipDetail->auto_yn === 'Y')
                                                                <span class="badge bg-success" style="font-size: 0.65rem;">Auto</span>
                                                            @endif
                                                            @if($wipDetail->lipas_yn === 'Y')
                                                                <span class="badge bg-warning text-dark" style="font-size: 0.65rem;">Lipas</span>
                                                            @endif
                                                        </div>
                                                    @endif
                                                @else
                                                    <span class="text-muted">N/A</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="fw-medium">{{ $assignment->assigned_date->format('M d, Y') }}</div>
                                                <small class="text-muted">{{ $assignment->assigned_date->format('H:i') }}</small>
                                            </td>
                                            <td>
                                                <div class="fw-medium">{{ $assignment->assignedBy->emp_name ?? 'Unknown' }}</div>
                                                <small class="text-muted">{{ $assignment->assignedBy->emp_no ?? 'N/A' }}</small>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                                <tfoot class="table-light">
                                    <tr>
                                        <td colspan="3" class="fw-bold">Total Assigned Lots:</td>
                                        <td class="fw-bold text-success">{{ $lotRequest->lotAssignments->sum('lot_quantity') ? number_format($lotRequest->lotAssignments->sum('lot_quantity')) : $lotRequest->lotAssignments->count() }}</td>
                                        <td colspan="3" class="fw-bold text-muted">{{ $lotRequest->lotAssignments->count() }} assignment(s)</td>
                                    </tr>
                                </tfoot>
                            </table>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Notes -->
            @if($lotRequest->notes)
                <div class="detail-card fade-in">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-sticky-note me-2"></i>Notes</h6>
                    </div>
                    <div class="card-body">
                        <p class="mb-0">{{ $lotRequest->notes }}</p>
                    </div>
                </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Lot Request Summary -->
            <div class="detail-card mb-4 fade-in">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar me-2"></i>Lot Request Summary</h6>
                </div>
                <div class="card-body">
                    <div class="summary-item d-flex justify-content-between">
                        <span>Request Number:</span>
                        <span class="fw-medium text-primary">{{ $lotRequest->request_number }}</span>
                    </div>
                    <div class="summary-item d-flex justify-content-between">
                        <span>Total Equipment Items:</span>
                        <span class="badge bg-secondary">{{ $lotRequest->lotRequestItems->count() }}</span>
                    </div>
                    <div class="summary-item d-flex justify-content-between">
                        <span>Total Lots:</span>
                        <span class="fw-bold text-primary fs-5">{{ $lotRequest->total_quantity }}</span>
                    </div>
                    <div class="summary-item d-flex justify-content-between">
                        <span>Status:</span>
                        <span class="badge {{ $lotRequest->getStatusBadgeClass() }} status-badge">
                            {{ $lotRequest->formatted_status }}
                        </span>
                    </div>
                    <div class="summary-item d-flex justify-content-between">
                        <span>Request Date:</span>
                        <span class="fw-medium">{{ $lotRequest->request_date->format('M d, Y') }}</span>
                    </div>
                </div>
            </div>

            <!-- Customer Information -->
            <div class="detail-card fade-in">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-user me-2"></i>Requestor Information</h6>
                </div>
                <div class="card-body">
                    <div class="summary-item d-flex justify-content-between">
                        <span>Name:</span>
                        <span class="fw-medium">{{ $lotRequest->user->emp_name }}</span>
                    </div>
                    <div class="summary-item d-flex justify-content-between">
                        <span>Employee ID:</span>
                        <span class="fw-medium">{{ $lotRequest->user->emp_no }}</span>
                    </div>
                    <div class="summary-item d-flex justify-content-between">
                        <span>Area Station:</span>
                        <span class="text-break">{{ $lotRequest->area_stations }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
