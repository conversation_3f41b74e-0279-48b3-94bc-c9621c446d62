<?php

namespace App\Http\Controllers;

use App\Models\UpdateWip;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UpdateWipController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // Only admin middleware for create, store, downloadTemplate
        // index and getWipGroupDetails are accessible to all authenticated users
    }

    /**
     * Display a listing of WIP data with filtering.
     */
    public function index(Request $request)
    {
        // Get filter parameters with defaults (showing all data initially)
        $filters = [
            'hold' => $request->get('hold', 'all'),
            'lot_size' => $request->get('lot_size', 'all'),
            'qty_class' => $request->get('qty_class', 'all'),
            'work_type' => $request->get('work_type', 'all'),
            'wip_status' => $request->get('wip_status', 'all'),
            'lipas_yn' => $request->get('lipas_yn', 'all'),
            'auto_yn' => $request->get('auto_yn', 'all'),
            'wildcard_search' => $request->get('wildcard_search', ''),
        ];
        
        // Build WIP summary query with filters
        $wipQuery = UpdateWip::select(
            'lot_size',
            'lot_code', 
            'eqp_class',
            'eqp_type',
            'work_type',
            DB::raw('COUNT(*) as lot_count'),
            DB::raw('SUM(lot_qty) as total_quantity'),
            DB::raw('AVG(lot_qty) as average_quantity'),
            DB::raw('AVG(CAST(stagnant_tat AS DECIMAL(10,2))) as average_tat')
        );
        
        // Apply filters
        if ($filters['hold'] !== 'all') {
            $wipQuery->where('hold', $filters['hold']);
        }
        if ($filters['lot_size'] !== 'all') {
            $wipQuery->where('lot_size', $filters['lot_size']);
        }
        if ($filters['qty_class'] !== 'all') {
            $wipQuery->where('qty_class', $filters['qty_class']);
        }
        if ($filters['work_type'] !== 'all') {
            $wipQuery->where('work_type', $filters['work_type']);
        }
        if ($filters['wip_status'] !== 'all') {
            $wipQuery->where('wip_status', 'like', '%' . $filters['wip_status'] . '%');
        }
        if ($filters['lipas_yn'] !== 'all') {
            $wipQuery->where('lipas_yn', $filters['lipas_yn']);
        }
        if ($filters['auto_yn'] !== 'all') {
            $wipQuery->where('auto_yn', $filters['auto_yn']);
        }
        
        // Apply wildcard search if provided
        if (!empty($filters['wildcard_search'])) {
            $searchTerm = $filters['wildcard_search'];
            // Convert wildcards: * to % (multiple chars), ? to _ (single char)
            $likeTerm = str_replace(['*', '?'], ['%', '_'], $searchTerm);
            
            $wipQuery->where(function($query) use ($likeTerm) {
                $query->where('lot_id', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('model_15', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('lot_code', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('lot_location', 'LIKE', '%' . $likeTerm . '%');
            });
        }
        
        $wipSummary = $wipQuery
            ->groupBy('lot_size', 'lot_code', 'eqp_class', 'eqp_type', 'work_type')
            ->orderBy('lot_size')
            ->orderBy('lot_code')
            ->get();
        
        // Get filter options from database
        $filterOptions = [
            'lot_sizes' => UpdateWip::distinct()->pluck('lot_size')->filter()->sort()->values(),
            'qty_classes' => UpdateWip::distinct()->pluck('qty_class')->filter()->sort()->values(),
            'work_types' => UpdateWip::distinct()->pluck('work_type')->filter()->sort()->values(),
            'wip_statuses' => UpdateWip::distinct()->pluck('wip_status')->filter()->sort()->values(),
        ];
        
        return view('updatewip.index', compact('wipSummary', 'filters', 'filterOptions'));
    }

    /**
     * Get detailed lot records for a specific WIP group
     */
    public function getWipGroupDetails(Request $request)
    {
        $request->validate([
            'lot_size' => 'required|string',
            'lot_code' => 'required|string',
            'eqp_class' => 'required|string',
            'eqp_type' => 'required|string',
            'work_type' => 'required|string',
            'hold' => 'nullable|string',
            'qty_class' => 'nullable|string',
            'wip_status' => 'nullable|string',
            'lipas_yn' => 'nullable|string',
            'auto_yn' => 'nullable|string',
            'wildcard_search' => 'nullable|string',
        ]);

        $lotDetailsQuery = UpdateWip::select(
            'lot_id',
            'model_15',
            'lot_qty',
            'stagnant_tat',
            'lot_location',
            'lot_code as code'
        )
        ->where('lot_size', $request->lot_size)
        ->where('lot_code', $request->lot_code)
        ->where('eqp_class', $request->eqp_class)
        ->where('eqp_type', $request->eqp_type)
        ->where('work_type', $request->work_type);
        
        // Apply additional filters if provided
        if ($request->has('hold') && $request->hold !== 'all') {
            $lotDetailsQuery->where('hold', $request->hold);
        }
        if ($request->has('qty_class') && $request->qty_class !== 'all') {
            $lotDetailsQuery->where('qty_class', $request->qty_class);
        }
        if ($request->has('wip_status') && $request->wip_status !== 'all') {
            $lotDetailsQuery->where('wip_status', 'like', '%' . $request->wip_status . '%');
        }
        if ($request->has('lipas_yn') && $request->lipas_yn !== 'all') {
            $lotDetailsQuery->where('lipas_yn', $request->lipas_yn);
        }
        if ($request->has('auto_yn') && $request->auto_yn !== 'all') {
            $lotDetailsQuery->where('auto_yn', $request->auto_yn);
        }
        
        // Apply wildcard search if provided
        if ($request->has('wildcard_search') && !empty($request->wildcard_search)) {
            $searchTerm = $request->wildcard_search;
            // Convert wildcards: * to % (multiple chars), ? to _ (single char)
            $likeTerm = str_replace(['*', '?'], ['%', '_'], $searchTerm);
            
            $lotDetailsQuery->where(function($query) use ($likeTerm) {
                $query->where('lot_id', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('model_15', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('lot_code', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('lot_location', 'LIKE', '%' . $likeTerm . '%');
            });
        }
        
        $lotDetails = $lotDetailsQuery->orderBy('lot_id')->get();

        return response()->json([
            'success' => true,
            'data' => $lotDetails,
            'group_info' => [
                'lot_size' => $request->lot_size,
                'lot_code' => $request->lot_code,
                'eqp_class' => $request->eqp_class,
                'eqp_type' => $request->eqp_type,
                'work_type' => $request->work_type,
                'total_lots' => $lotDetails->count(),
                'total_quantity' => $lotDetails->sum('lot_qty'),
                'average_tat' => $lotDetails->avg('stagnant_tat')
            ]
        ]);
    }

    /**
     * Show the form for updating WIP data.
     * Users can create/update, Managers and Admins get additional permissions.
     */
    public function create()
    {
        // Check permission - all authenticated users can access
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            abort(403, 'You do not have permission to update WIP data.');
        }
        
        \Illuminate\Support\Facades\Log::info('UpdateWipController@create called', [
            'user_id' => auth()->id(),
            'user_role' => auth()->user()?->role,
            'user_emp_no' => auth()->user()?->emp_no,
            'timestamp' => now()
        ]);
        
        $currentCount = UpdateWip::count();
        $lastUpdate = UpdateWip::latest('updated_at')->first();
        $lastUpdateTime = $lastUpdate ? $lastUpdate->updated_at : null;
        
        return view('updatewip.create', compact('currentCount', 'lastUpdateTime'));
    }

    /**
     * Process the pasted Excel data and update the updatewip table.
     * Users can create/update, Managers and Admins get additional permissions.
     */
    public function store(Request $request)
    {
        // Check permission - all authenticated users can create/update
        if (!auth()->user()->isUser() && !auth()->user()->isManager() && !auth()->user()->isAdmin()) {
            abort(403, 'You do not have permission to update WIP data.');
        }
        $request->validate([
            'raw_data' => 'required|string',
        ]);

        try {
            // Clear the table first as requested and reset auto-increment (outside transaction)
            DB::table('updatewip')->delete();
            DB::statement('ALTER TABLE updatewip AUTO_INCREMENT = 1');
            
            DB::beginTransaction();
            
            $rawData = $request->input('raw_data');
            
            // Normalize line endings and split into lines
            $rawData = str_replace(["\r\n", "\r"], "\n", $rawData);
            $lines = explode("\n", $rawData);
            $lines = array_filter($lines, function($line) {
                return trim($line) !== '';
            });
            
            if (empty($lines)) {
                throw new \Exception('No data found in the pasted content.');
            }
            
            // Parse header from first line
            $headerLine = array_shift($lines);
            $header = str_getcsv($headerLine, "\t"); // Using tab delimiter for Excel paste
            
            // Normalize header keys
            $header = array_map(function ($h) {
                return trim(preg_replace('/^\xEF\xBB\xBF/', '', (string) $h));
            }, $header);
            
            $count = 0;
            foreach ($lines as $line) {
                $line = trim($line);
                if (empty($line)) {
                    continue;
                }
                
                // Parse data from line using tab delimiter
                $row = str_getcsv($line, "\t");
                
                // Map row to associative array by header
                $data = [];
                foreach ($header as $idx => $key) {
                    $val = $row[$idx] ?? null;
                    // Clean up values
                    if (is_string($val)) {
                        $val = trim($val);
                        // Remove surrounding quotes if present
                        if (strlen($val) >= 2 && $val[0] === '"' && $val[-1] === '"') {
                            $val = substr($val, 1, -1);
                        }
                    }
                    $data[$key] = $val === '' ? null : $val;
                }
                
                // Build payload matching our updatewip table columns
                $payload = [
                    'lot_id'         => $data['lot_id'] ?? null,
                    'model_15'       => $data['model_15'] ?? null,
                    'lot_size'       => $data['lot_size'] ?? null,
                    'lot_qty'        => isset($data['lot_qty']) ? (int)str_replace(',', '', trim((string)$data['lot_qty'])) : null,
                    'stagnant_tat'   => isset($data['stagnant_tat']) ? trim((string)$data['stagnant_tat']) : null,
                    'qty_class'      => $data['qty_class'] ?? null,
                    'work_type'      => $data['work_type'] ?? null,
                    'wip_status'     => $data['wip_status'] ?? null,
                    'lot_status'     => $data['lot_status'] ?? null,
                    'hold'           => $data['hold'] ?? null,
                    'auto_yn'        => $data['auto_yn'] ?? null,
                    'lipas_yn'       => $data['lipas_yn'] ?? null,
                    'eqp_type'       => $data['eqp_type'] ?? null,
                    'eqp_class'      => $data['eqp_class'] ?? null,
                    'lot_location'   => $data['lot_location'] ?? null,
                    'lot_code'       => $data['lot_code'] ?? null,
                    'modified_by'    => Auth::user()->emp_name ?? Auth::user()->emp_no,
                ];
                
                // Skip if no lot_id
                if (empty($payload['lot_id'])) {
                    continue;
                }
                
                UpdateWip::create($payload);
                $count++;
            }
            
            DB::commit();
            
            return redirect()->route('updatewip.create')
                ->with('success', "Successfully updated WIP data with {$count} records.");
                
        } catch (\Exception $e) {
            DB::rollback();
            
            return redirect()->back()
                ->withInput()
                ->with('error', 'Error processing data: ' . $e->getMessage());
        }
    }
    
    /**
     * Download the updatewip template Excel file.
     */
    public function downloadTemplate()
    {
        $filePath = public_path('storage/documents/updatewip.xlsx');
        
        if (!file_exists($filePath)) {
            return redirect()->back()->with('error', 'Template file not found.');
        }
        
        return response()->download($filePath, 'updatewip.xlsx');
    }
}
