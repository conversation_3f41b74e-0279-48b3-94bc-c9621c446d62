<x-app-layout>
    <x-slot name="header">
        Inventory Reports
    </x-slot>

    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Inventory Management</h4>
                    <p class="text-muted mb-0">Monitor stock levels and product performance</p>
                </div>
                <div class="d-flex gap-2">
                    <a href="{{ route('reports.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Reports
                    </a>
                    <a href="{{ route('reports.export.inventory') }}" class="btn btn-success">
                        <i class="fas fa-download me-2"></i>Export CSV
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-primary mb-2">
                        <i class="fas fa-boxes fa-2x"></i>
                    </div>
                    <h4 class="text-primary mb-1">{{ number_format($inventorySummary['total_products']) }}</h4>
                    <small class="text-muted">Total Products</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-success mb-2">
                        <i class="fas fa-check-circle fa-2x"></i>
                    </div>
                    <h4 class="text-success mb-1">{{ number_format($inventorySummary['active_products']) }}</h4>
                    <small class="text-muted">Active</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-danger mb-2">
                        <i class="fas fa-times-circle fa-2x"></i>
                    </div>
                    <h4 class="text-danger mb-1">{{ number_format($inventorySummary['out_of_stock']) }}</h4>
                    <small class="text-muted">Out of Stock</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-warning mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h4 class="text-warning mb-1">{{ number_format($inventorySummary['low_stock']) }}</h4>
                    <small class="text-muted">Low Stock</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-info mb-2">
                        <i class="fas fa-tags fa-2x"></i>
                    </div>
                    <h4 class="text-info mb-1">{{ number_format($inventorySummary['categories_count']) }}</h4>
                    <small class="text-muted">Categories</small>
                </div>
            </div>
        </div>
        <div class="col-xl-2 col-md-4">
            <div class="card border-0 shadow-sm text-center">
                <div class="card-body">
                    <div class="text-secondary mb-2">
                        <i class="fas fa-dollar-sign fa-2x"></i>
                    </div>
                    <h4 class="text-secondary mb-1">${{ number_format($inventorySummary['total_inventory_value'], 0) }}</h4>
                    <small class="text-muted">Total Value</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Inventory Filters</h6>
                </div>
                <div class="card-body">
                    <form method="GET" action="{{ route('reports.inventory') }}" class="row g-3">
                        <div class="col-md-3">
                            <label for="category" class="form-label">Category</label>
                            <select class="form-select" id="category" name="category">
                                <option value="">All Categories</option>
                                @foreach($categories as $cat)
                                    <option value="{{ $cat }}" {{ $category === $cat ? 'selected' : '' }}>{{ $cat }}</option>
                                @endforeach
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="">All Status</option>
                                <option value="active" {{ $status === 'active' ? 'selected' : '' }}>Active</option>
                                <option value="inactive" {{ $status === 'inactive' ? 'selected' : '' }}>Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="low_stock" name="low_stock" value="1" {{ $lowStock ? 'checked' : '' }}>
                                <label class="form-check-label" for="low_stock">
                                    Show only low stock items (< 10)
                                </label>
                            </div>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>Apply Filters
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Inventory Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <div class="d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">Product Inventory</h6>
                        <small class="text-muted">{{ $products->total() }} products found</small>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Product</th>
                                    <th>Category</th>
                                    <th>Price</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Value</th>
                                    <th>Created By</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($products as $product)
                                    <tr class="{{ $product->stock == 0 ? 'table-danger' : ($product->stock < 10 ? 'table-warning' : '') }}">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                @if($product->image)
                                                    <img src="{{ asset('storage/' . $product->image) }}" 
                                                         alt="{{ $product->name }}" 
                                                         class="rounded me-3" 
                                                         style="width: 40px; height: 40px; object-fit: cover;">
                                                @else
                                                    <div class="bg-light rounded me-3 d-flex align-items-center justify-content-center" 
                                                         style="width: 40px; height: 40px;">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                @endif
                                                <div>
                                                    <div class="fw-bold">{{ $product->name }}</div>
                                                    @if($product->description)
                                                        <small class="text-muted">{{ Str::limit($product->description, 50) }}</small>
                                                    @endif
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            @if($product->category)
                                                <span class="badge bg-secondary">{{ $product->category }}</span>
                                            @else
                                                <span class="text-muted">-</span>
                                            @endif
                                        </td>
                                        <td class="text-success fw-bold">${{ number_format($product->price, 2) }}</td>
                                        <td>
                                            @if($product->stock == 0)
                                                <span class="badge bg-danger">Out of Stock</span>
                                            @elseif($product->stock < 10)
                                                <span class="badge bg-warning">{{ $product->stock }} (Low)</span>
                                            @else
                                                <span class="badge bg-success">{{ $product->stock }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            <span class="badge {{ $product->isActive() ? 'bg-success' : 'bg-secondary' }}">
                                                {{ ucfirst($product->status) }}
                                            </span>
                                        </td>
                                        <td class="text-primary fw-bold">${{ number_format($product->price * $product->stock, 2) }}</td>
                                        <td>{{ $product->creator->name }}</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('products.show', $product) }}" class="btn btn-outline-primary">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('products.edit', $product) }}" class="btn btn-outline-warning">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="8" class="text-center text-muted py-4">
                                            <i class="fas fa-boxes fa-2x mb-3"></i>
                                            <p>No products found matching the selected criteria.</p>
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                    
                    @if($products->hasPages())
                        <div class="d-flex justify-content-center mt-4">
                            {{ $products->withQueryString()->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Alerts -->
    @if($inventorySummary['low_stock'] > 0 || $inventorySummary['out_of_stock'] > 0)
        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-warning border-0 shadow-sm">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-exclamation-triangle fa-2x me-3"></i>
                        <div>
                            <h6 class="alert-heading mb-1">Inventory Alerts</h6>
                            <p class="mb-0">
                                @if($inventorySummary['out_of_stock'] > 0)
                                    <strong>{{ $inventorySummary['out_of_stock'] }}</strong> products are out of stock.
                                @endif
                                @if($inventorySummary['low_stock'] > 0)
                                    <strong>{{ $inventorySummary['low_stock'] }}</strong> products have low stock levels.
                                @endif
                                Consider restocking these items soon.
                            </p>
                        </div>
                        <div class="ms-auto">
                            <a href="{{ route('reports.inventory') }}?low_stock=1" class="btn btn-warning">
                                View Low Stock Items
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif
</x-app-layout>