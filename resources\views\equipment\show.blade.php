<x-app-layout>
    <x-slot name="header">
        Equipment Details
    </x-slot>

    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-cogs me-2"></i>Equipment Details - {{ $equipment->eqp_no }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <strong>Equipment Number:</strong>
                            <p class="mb-0">{{ $equipment->eqp_no }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Equipment Line:</strong>
                            <p class="mb-0">{{ $equipment->eqp_line }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Equipment Area:</strong>
                            <p class="mb-0">{{ $equipment->eqp_area ?? 'N/A' }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Equipment Type:</strong>
                            <p class="mb-0">{{ $equipment->eqp_type }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Equipment Class:</strong>
                            <p class="mb-0">{{ $equipment->eqp_class ?? 'N/A' }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Equipment Maker:</strong>
                            <p class="mb-0">{{ $equipment->eqp_maker }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Feeder Type:</strong>
                            <p class="mb-0">{{ $equipment->feeder_type ?? 'N/A' }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Lot Size:</strong>
                            <p class="mb-0">{{ $equipment->lot_size }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Work Type:</strong>
                            <p class="mb-0">{{ $equipment->work_type }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Lot Type:</strong>
                            <p class="mb-0">{{ $equipment->lot_type }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Lot Quantity Allocation:</strong>
                            <p class="mb-0">{{ number_format($equipment->lotqty_alloc) }}</p>
                        </div>
                        
                        <div class="col-md-4">
                            <strong>Equipment OEE:</strong>
                            <p class="mb-0">{{ number_format($equipment->eqp_oee, 4) }}</p>
                        </div>
                        
                        <div class="col-md-4">
                            <strong>Equipment Speed:</strong>
                            <p class="mb-0">{{ number_format($equipment->eqp_speed, 2) }}</p>
                        </div>
                        
                        <div class="col-md-4">
                            <strong>Operation Time:</strong>
                            <p class="mb-0">{{ number_format($equipment->operation_time, 2) }} hours</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Equipment Code:</strong>
                            <p class="mb-0">{{ $equipment->eqp_code }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Ongoing Lot:</strong>
                            <p class="mb-0">{{ $equipment->ongoing_lot ?? 'N/A' }}</p>
                        </div>
                        
                        <div class="col-12">
                            <strong>Calculated OEE:</strong>
                            <p class="mb-0">
                                <span class="badge bg-success">
                                    {{ number_format($equipment->eqp_oee * $equipment->eqp_speed * $equipment->operation_time, 4) }}
                                </span>
                                <small class="text-muted ms-2">(OEE × Speed × Operation Time)</small>
                            </p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Modified By:</strong>
                            <p class="mb-0">{{ $equipment->modified_by ?? 'System' }}</p>
                        </div>
                        
                        <div class="col-md-6">
                            <strong>Last Updated:</strong>
                            <p class="mb-0">{{ $equipment->updated_at ? $equipment->updated_at->format('Y-m-d H:i:s') : 'N/A' }}</p>
                        </div>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2">
                                @if(Auth::user()->canManageOrders())
                                    <a href="{{ route('equipment.edit', $equipment) }}" class="btn btn-warning">
                                        <i class="fas fa-edit me-1"></i>Edit Equipment
                                    </a>
                                    <form method="POST" action="{{ route('equipment.destroy', $equipment) }}" class="d-inline"
                                          onsubmit="return confirm('Are you sure you want to delete this equipment?');">
                                        @csrf
                                        @method('DELETE')
                                        <button type="submit" class="btn btn-danger">
                                            <i class="fas fa-trash me-1"></i>Delete Equipment
                                        </button>
                                    </form>
                                @endif
                                <a href="{{ route('equipment.index') }}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-1"></i>Back to List
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
