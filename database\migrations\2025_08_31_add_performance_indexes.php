<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('equipment', function (Blueprint $table) {
            // Indexes for equipment filtering and searching
            $table->index('eqp_code', 'idx_equipment_code');
            $table->index(['eqp_line', 'eqp_code'], 'idx_equipment_line_code');
            $table->index(['eqp_type', 'eqp_class'], 'idx_equipment_type_class');
            $table->index(['eqp_maker', 'eqp_type'], 'idx_equipment_maker_type');
            $table->index('lot_size', 'idx_equipment_lot_size');
            $table->index('work_type', 'idx_equipment_work_type');
        });

        Schema::table('updatewip', function (Blueprint $table) {
            // Indexes for WIP filtering and lot assignment
            $table->index('lot_code', 'idx_updatewip_lot_code');
            $table->index('wip_status', 'idx_updatewip_status');
            $table->index(['lot_code', 'wip_status'], 'idx_updatewip_lot_code_status');
            $table->index(['lot_size', 'qty_class', 'work_type'], 'idx_updatewip_filters');
            $table->index('lot_location', 'idx_updatewip_location');
            $table->index('model_15', 'idx_updatewip_model');
        });

        Schema::table('orders', function (Blueprint $table) {
            // Indexes for order analytics and filtering
            $table->index('status', 'idx_orders_status');
            $table->index(['created_at', 'status'], 'idx_orders_date_status');
            $table->index(['user_id', 'created_at'], 'idx_orders_user_date');
            $table->index(['status', 'total_amount'], 'idx_orders_status_amount');
        });

        Schema::table('lot_requests', function (Blueprint $table) {
            // Indexes for lot request management
            $table->index(['user_id', 'status'], 'idx_lot_requests_user_status');
            $table->index('request_date', 'idx_lot_requests_date');
            $table->index(['status', 'created_at'], 'idx_lot_requests_status_date');
        });

        Schema::table('lot_request_items', function (Blueprint $table) {
            // Indexes for lot request item queries
            $table->index('equipment_number', 'idx_lot_items_equipment');
            $table->index('equipment_code', 'idx_lot_items_code');
            $table->index(['lot_request_id', 'equipment_code'], 'idx_lot_items_request_code');
        });

        Schema::table('lot_assignments', function (Blueprint $table) {
            // Indexes for lot assignment queries
            $table->index('lot_id', 'idx_lot_assignments_lot_id');
            $table->index('lot_code', 'idx_lot_assignments_code');
            $table->index(['lot_request_id', 'lot_code'], 'idx_lot_assignments_request_code');
            $table->index('assigned_date', 'idx_lot_assignments_date');
        });

        Schema::table('products', function (Blueprint $table) {
            // Indexes for product management
            $table->index(['status', 'stock'], 'idx_products_status_stock');
            $table->index('category', 'idx_products_category');
            $table->index('created_by', 'idx_products_creator');
        });

        Schema::table('order_items', function (Blueprint $table) {
            // Indexes for order item analytics
            $table->index(['product_id', 'created_at'], 'idx_order_items_product_date');
            $table->index(['order_id', 'product_id'], 'idx_order_items_order_product');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('equipment', function (Blueprint $table) {
            $table->dropIndex('idx_equipment_code');
            $table->dropIndex('idx_equipment_line_code');
            $table->dropIndex('idx_equipment_type_class');
            $table->dropIndex('idx_equipment_maker_type');
            $table->dropIndex('idx_equipment_lot_size');
            $table->dropIndex('idx_equipment_work_type');
        });

        Schema::table('updatewip', function (Blueprint $table) {
            $table->dropIndex('idx_updatewip_lot_code');
            $table->dropIndex('idx_updatewip_status');
            $table->dropIndex('idx_updatewip_lot_code_status');
            $table->dropIndex('idx_updatewip_filters');
            $table->dropIndex('idx_updatewip_location');
            $table->dropIndex('idx_updatewip_model');
        });

        Schema::table('orders', function (Blueprint $table) {
            $table->dropIndex('idx_orders_status');
            $table->dropIndex('idx_orders_date_status');
            $table->dropIndex('idx_orders_user_date');
            $table->dropIndex('idx_orders_status_amount');
        });

        Schema::table('lot_requests', function (Blueprint $table) {
            $table->dropIndex('idx_lot_requests_user_status');
            $table->dropIndex('idx_lot_requests_date');
            $table->dropIndex('idx_lot_requests_status_date');
        });

        Schema::table('lot_request_items', function (Blueprint $table) {
            $table->dropIndex('idx_lot_items_equipment');
            $table->dropIndex('idx_lot_items_code');
            $table->dropIndex('idx_lot_items_request_code');
        });

        Schema::table('lot_assignments', function (Blueprint $table) {
            $table->dropIndex('idx_lot_assignments_lot_id');
            $table->dropIndex('idx_lot_assignments_code');
            $table->dropIndex('idx_lot_assignments_request_code');
            $table->dropIndex('idx_lot_assignments_date');
        });

        Schema::table('products', function (Blueprint $table) {
            $table->dropIndex('idx_products_status_stock');
            $table->dropIndex('idx_products_category');
            $table->dropIndex('idx_products_creator');
        });

        Schema::table('order_items', function (Blueprint $table) {
            $table->dropIndex('idx_order_items_product_date');
            $table->dropIndex('idx_order_items_order_product');
        });
    }
};
