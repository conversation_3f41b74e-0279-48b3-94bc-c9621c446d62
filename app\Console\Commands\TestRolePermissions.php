<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\User;
use App\Http\Middleware\PermissionMiddleware;

class TestRolePermissions extends Command
{
    protected $signature = 'test:role-permissions';
    protected $description = 'Test role-based permission system';

    public function handle()
    {
        $this->info('Testing Role-Based Permission System');
        $this->info('=====================================');

        // Test permission middleware
        $middleware = new PermissionMiddleware();
        
        // Test scenarios
        $testScenarios = [
            ['role' => 'USER', 'route' => 'dashboard', 'action' => 'view', 'expected' => true],
            ['role' => 'USER', 'route' => 'endtime', 'action' => 'create', 'expected' => true],
            ['role' => 'USER', 'route' => 'endtime', 'action' => 'delete', 'expected' => false],
            ['role' => 'USER', 'route' => 'mc-alloc-rpt', 'action' => 'view', 'expected' => false],
            ['role' => 'MANAGER', 'route' => 'mc-alloc-rpt', 'action' => 'view', 'expected' => true],
            ['role' => 'MANAGER', 'route' => 'endtime', 'action' => 'delete', 'expected' => true],
            ['role' => 'MANAGER', 'route' => 'management/users', 'action' => 'view', 'expected' => false],
            ['role' => 'ADMIN', 'route' => 'management/users', 'action' => 'view', 'expected' => true],
            ['role' => 'ADMIN', 'route' => 'dashboard', 'action' => 'delete', 'expected' => true],
        ];

        foreach ($testScenarios as $scenario) {
            $this->testPermission($scenario);
        }

        $this->info("\nRole-based permission testing completed!");
    }

    private function testPermission($scenario)
    {
        // Create a mock user with the specified role
        $user = new User(['role' => $scenario['role']]);
        
        // Test permission logic manually (since we can't mock middleware easily in console)
        $hasPermission = $this->checkPermission($user, $scenario['route'], $scenario['action']);
        
        $status = $hasPermission === $scenario['expected'] ? '✅ PASS' : '❌ FAIL';
        
        $this->line(sprintf(
            '%s %s role accessing %s:%s (expected: %s, got: %s)',
            $status,
            $scenario['role'],
            $scenario['route'],
            $scenario['action'],
            $scenario['expected'] ? 'allow' : 'deny',
            $hasPermission ? 'allow' : 'deny'
        ));
    }

    private function checkPermission($user, string $route, string $action): bool
    {
        $role = $user->role;

        // Define permissions matrix (same as in PermissionMiddleware)
        $permissions = [
            'USER' => [
                'dashboard' => ['view'],
                'endtime' => ['view', 'create', 'update'],
                'requests-rpt' => ['view'],
                'lot-requests' => ['view'],
                'lot-requests/create' => ['view', 'create'],
                'process-wip-rpt' => ['view'],
                'updatewip' => ['view'],
                'updatewip/create' => ['view', 'create', 'update'],
                'endline-rpt' => ['view'],
                'endline-wip' => ['view', 'create', 'update'],
            ],
            'MANAGER' => [
                'dashboard' => ['view'],
                'endtime' => ['view', 'create', 'update', 'delete'],
                'requests-rpt' => ['view'],
                'lot-requests' => ['view', 'create', 'update', 'delete'],
                'lot-requests/create' => ['view', 'create', 'update', 'delete'],
                'process-wip-rpt' => ['view'],
                'updatewip' => ['view'],
                'updatewip/create' => ['view', 'create', 'update'],
                'mc-alloc-rpt' => ['view'],
                'equipment' => ['view', 'create', 'update', 'delete'],
                'endline-rpt' => ['view'],
                'endline-wip' => ['view', 'create', 'update', 'delete'],
            ],
            'ADMIN' => [
                'dashboard' => ['view', 'create', 'update', 'delete'],
                'endtime' => ['view', 'create', 'update', 'delete'],
                'requests-rpt' => ['view', 'create', 'update', 'delete'],
                'lot-requests' => ['view', 'create', 'update', 'delete'],
                'lot-requests/create' => ['view', 'create', 'update', 'delete'],
                'process-wip-rpt' => ['view', 'create', 'update', 'delete'],
                'updatewip' => ['view', 'create', 'update', 'delete'],
                'updatewip/create' => ['view', 'create', 'update', 'delete'],
                'mc-alloc-rpt' => ['view', 'create', 'update', 'delete'],
                'equipment' => ['view', 'create', 'update', 'delete'],
                'endline-rpt' => ['view', 'create', 'update', 'delete'],
                'endline-wip' => ['view', 'create', 'update', 'delete'],
                'management/settings' => ['view', 'create', 'update', 'delete'],
                'management/data' => ['view', 'create', 'update', 'delete'],
                'management/users' => ['view', 'create', 'update', 'delete'],
                'profile' => ['view', 'update'],
            ],
        ];

        // Check if role exists
        if (!isset($permissions[$role])) {
            return false;
        }

        // Check if route permission exists for this role
        if (!isset($permissions[$role][$route])) {
            return false;
        }

        // Check if specific action is allowed
        return in_array($action, $permissions[$role][$route]);
    }
}
