<x-app-layout>
    <x-slot name="header">
        Sales Analytics
    </x-slot>

    <!-- Sales Overview Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success bg-opacity-10 text-success me-3">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($salesData['daily_sales']->sum('revenue'), 2) }}</h3>
                            <p class="text-muted mb-0">Last 30 Days Revenue</p>
                            <small class="text-success">
                                <i class="fas fa-arrow-up"></i>
                                {{ $salesData['daily_sales']->count() }} days tracked
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-primary bg-opacity-10 text-primary me-3">
                            <i class="fas fa-shopping-cart"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($salesData['daily_sales']->sum('orders')) }}</h3>
                            <p class="text-muted mb-0">Total Orders</p>
                            <small class="text-primary">Last 30 days</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info bg-opacity-10 text-info me-3">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($salesData['daily_sales']->avg('revenue'), 2) }}</h3>
                            <p class="text-muted mb-0">Daily Average</p>
                            <small class="text-info">Revenue per day</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning bg-opacity-10 text-warning me-3">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($salesData['conversion_metrics']['conversion_rate'], 1) }}%</h3>
                            <p class="text-muted mb-0">Conversion Rate</p>
                            <small class="text-warning">Customer conversion</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <!-- Daily Sales Chart -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Daily Sales Trend</h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px;">
                        <canvas id="dailySalesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sales by Category -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Sales by Category</h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px;">
                        <canvas id="salesByCategoryChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Monthly Comparison and Top Products -->
    <div class="row g-4 mb-4">
        <!-- Monthly Comparison -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Monthly Comparison</h5>
                </div>
                <div class="card-body">
                    <div style="height: 300px;">
                        <canvas id="monthlyComparisonChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Selling Products -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Top Selling Products</h5>
                </div>
                <div class="card-body">
                    @if($salesData['top_selling_products']->count() > 0)
                        @foreach($salesData['top_selling_products']->take(8) as $product)
                        <div class="d-flex justify-content-between align-items-center mb-3 {{ !$loop->last ? 'border-bottom pb-3' : '' }}">
                            <div>
                                <h6 class="mb-0">{{ $product->name }}</h6>
                                <small class="text-muted">{{ $product->total_sold }} units sold</small>
                            </div>
                            <div class="text-end">
                                <strong>${{ number_format($product->total_revenue, 2) }}</strong>
                            </div>
                        </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-box fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No sales data available</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Back Navigation -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <a href="{{ route('management.analytics.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Analytics Overview
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
    </style>

    @php
        $chartData = [
            'dailySalesData' => $salesData['daily_sales']->pluck('revenue'),
            'dailySalesLabels' => $salesData['daily_sales']->pluck('date'),
            'dailyOrdersData' => $salesData['daily_sales']->pluck('orders'),
            'categoryData' => $salesData['sales_by_category']->pluck('total_revenue'),
            'categoryLabels' => $salesData['sales_by_category']->pluck('category'),
            'monthlyData' => $salesData['monthly_comparison']->pluck('revenue'),
            'monthlyLabels' => $salesData['monthly_comparison']->map(function($item) {
                return $item->year . '-' . str_pad($item->month, 2, '0', STR_PAD_LEFT);
            })
        ];
    @endphp

    <div id="chart-data" 
         data-daily-sales="{{ htmlspecialchars(json_encode($chartData['dailySalesData']), ENT_QUOTES, 'UTF-8') }}"
         data-daily-labels="{{ htmlspecialchars(json_encode($chartData['dailySalesLabels']), ENT_QUOTES, 'UTF-8') }}"
         data-daily-orders="{{ htmlspecialchars(json_encode($chartData['dailyOrdersData']), ENT_QUOTES, 'UTF-8') }}"
         data-category-data="{{ htmlspecialchars(json_encode($chartData['categoryData']), ENT_QUOTES, 'UTF-8') }}"
         data-category-labels="{{ htmlspecialchars(json_encode($chartData['categoryLabels']), ENT_QUOTES, 'UTF-8') }}"
         data-monthly-data="{{ htmlspecialchars(json_encode($chartData['monthlyData']), ENT_QUOTES, 'UTF-8') }}"
         data-monthly-labels="{{ htmlspecialchars(json_encode($chartData['monthlyLabels']), ENT_QUOTES, 'UTF-8') }}"
         style="display: none;"></div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded');
                return;
            }

            // Get data from HTML attributes
            const chartDataElement = document.getElementById('chart-data');
            const chartData = {
                dailySalesData: JSON.parse(chartDataElement.getAttribute('data-daily-sales')),
                dailySalesLabels: JSON.parse(chartDataElement.getAttribute('data-daily-labels')),
                dailyOrdersData: JSON.parse(chartDataElement.getAttribute('data-daily-orders')),
                categoryData: JSON.parse(chartDataElement.getAttribute('data-category-data')),
                categoryLabels: JSON.parse(chartDataElement.getAttribute('data-category-labels')),
                monthlyData: JSON.parse(chartDataElement.getAttribute('data-monthly-data')),
                monthlyLabels: JSON.parse(chartDataElement.getAttribute('data-monthly-labels'))
            };

            // Daily Sales Chart
            const dailySalesCtx = document.getElementById('dailySalesChart');
            if (dailySalesCtx) {
                const dailySalesData = chartData.dailySalesData;
                const dailySalesLabels = chartData.dailySalesLabels;
                const dailyOrdersData = chartData.dailyOrdersData;
                
                new Chart(dailySalesCtx, {
                    type: 'line',
                    data: {
                        labels: dailySalesLabels,
                        datasets: [{
                            label: 'Revenue ($)',
                            data: dailySalesData,
                            borderColor: 'rgb(99, 102, 241)',
                            backgroundColor: 'rgba(99, 102, 241, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.4,
                            yAxisID: 'y'
                        }, {
                            label: 'Orders',
                            data: dailyOrdersData,
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 2,
                            fill: false,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false,
                                },
                                ticks: {
                                    callback: function(value) {
                                        return value + ' orders';
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Sales by Category Chart
            const categoryCtx = document.getElementById('salesByCategoryChart');
            if (categoryCtx) {
                const categoryData = chartData.categoryData;
                const categoryLabels = chartData.categoryLabels;
                
                new Chart(categoryCtx, {
                    type: 'doughnut',
                    data: {
                        labels: categoryLabels,
                        datasets: [{
                            data: categoryData,
                            backgroundColor: [
                                '#6366f1',
                                '#22c55e',
                                '#f59e0b',
                                '#ef4444',
                                '#8b5cf6',
                                '#06b6d4'
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }

            // Monthly Comparison Chart
            const monthlyCtx = document.getElementById('monthlyComparisonChart');
            if (monthlyCtx) {
                const monthlyData = chartData.monthlyData;
                const monthlyLabels = chartData.monthlyLabels;
                
                new Chart(monthlyCtx, {
                    type: 'bar',
                    data: {
                        labels: monthlyLabels,
                        datasets: [{
                            label: 'Monthly Revenue',
                            data: monthlyData,
                            backgroundColor: 'rgba(99, 102, 241, 0.8)',
                            borderColor: 'rgb(99, 102, 241)',
                            borderWidth: 1,
                            borderRadius: 8,
                            borderSkipped: false
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                display: false
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</x-app-layout>