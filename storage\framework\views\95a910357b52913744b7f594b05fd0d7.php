<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        ENDTIME | CREATE NEW LOT
     <?php $__env->endSlot(); ?>

    <!-- Enhanced CSS for endtime functionality -->
    <link href="<?php echo e(asset('css/endtime-enhancements.css')); ?>" rel="stylesheet">

    <style>
        /* Equipment dropdown styling - ENHANCED */
        .equipment-dropdown {
            position: fixed !important;
            background: white !important;
            border: 2px solid #007bff !important;
            border-radius: 0.375rem;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.4) !important;
            max-height: 300px;
            overflow-y: auto;
            z-index: 999999 !important; /* Even higher z-index */
            min-width: 300px;
            transform: none !important; /* Prevent any transform interference */
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: auto !important;
        }

        .equipment-dropdown:not(.d-none) {
            display: block !important;
        }

        .equipment-dropdown .dropdown-item {
            border: none;
            padding: 8px 12px;
            cursor: pointer;
            transition: background-color 0.15s ease-in-out;
            border-bottom: 1px solid #f0f0f0;
            background-color: white !important;
        }

        .equipment-dropdown .dropdown-item:hover {
            background-color: #e3f2fd !important;
        }

        .equipment-dropdown .dropdown-item:last-child {
            border-bottom: none;
        }

        /* CRITICAL: Override ALL container restrictions */
        .equipment-table-container,
        .table-responsive,
        .card,
        .card-body,
        body,
        html {
            overflow: visible !important;
        }

        /* Ensure position relative containers don't clip */
        .position-relative,
        .equipment-section,
        .equipment-section-header,
        td,
        tr,
        table,
        tbody {
            overflow: visible !important;
            position: static !important; /* Remove relative positioning that might interfere */
        }

        /* Force table to not clip content */
        table.table {
            table-layout: auto !important;
            overflow: visible !important;
        }

        /* Equipment search input validation styling */
        .equipment-search.is-valid {
            border-color: #198754;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='m2.3 6.73.49.49c.**********.58.06L8 4.93c.19-.14.23-.42.09-.61L6.91 3.15c-.14-.19-.42-.23-.61-.09L4.84 4.22c-.19.14-.23.41-.09.6l1.17 1.56c.***********.6.09z'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        .equipment-search.is-invalid {
            border-color: #dc3545;
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 2.4 2.4m0-2.4-2.4 2.4'/%3e%3c/svg%3e");
            background-repeat: no-repeat;
            background-position: right calc(0.375em + 0.1875rem) center;
            background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
        }

        /* Bootstrap d-none important override */
        .d-none {
            display: none !important;
        }

        /* Additional positioning helper */
        .equipment-dropdown-portal {
            position: fixed;
            top: 0;
            left: 0;
            z-index: 999999;
            pointer-events: none;
        }

        .equipment-dropdown-portal .equipment-dropdown {
            pointer-events: auto;
        }
    </style>

    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><strong>Validation Errors:</strong>
            <ul class="mb-0 mt-2">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Endtime Forecast Form -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-clock me-2"></i>New Lot Entry & Endtime Forecast
                </h5>
                <div class="d-flex gap-2">
                    <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-warning btn-sm">
                        <i class="fas fa-home me-1"></i>Dashboard
                    </a>
                    <a href="<?php echo e(route('endtime.index')); ?>" class="btn btn-light btn-sm">
                        <i class="fas fa-list me-1"></i>Back to List
                    </a>
                    <a href="<?php echo e(route('endtime.submit.show')); ?>" class="btn btn-success btn-sm">
                        <i class="fas fa-check-circle me-1"></i>ADD SUBMITTED Lot
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body p-3">
                <form id="newLotForm" action="<?php echo e(route('endtime.store')); ?>" method="POST">
                    <?php echo csrf_field(); ?>

                    <!-- Enhanced Lot Information Section -->
                    <div class="lot-info-card mb-4">
                        <div class="lot-info-header">
                            <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>Lot Information</h6>
                        </div>
                        <div class="lot-info-body">
                            <!-- Top Row: Lot ID, Lot Qty, Lot Type -->
                            <div class="lot-main-controls">
                                <!-- Lot ID Input -->
                                <div class="lot-id-group">
                                    <label class="form-label fw-bold">Lot ID <span class="text-danger">*</span></label>
                                    <div class="position-relative">
                                        <input type="text" class="form-control lot-id-input" id="lot_id" name="lot_id"
                                               required maxlength="10" placeholder="Enter Lot ID">
                                        <div class="lot-quantity-badge" id="lotQuantityDisplay" style="display: none;">
                                            <span class="lot-qty-value"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Lot Quantity Display/Input -->
                                <div class="lot-qty-group">
                                    <label class="form-label fw-bold">Lot Qty</label>
                                    <!-- Default Quantity Display -->
                                    <div class="lot-qty-display" id="defaultQtyDisplay">
                                        <div class="qty-value" id="lotQtyValue">--</div>
                                        <small class="text-muted">pieces</small>
                                    </div>
                                    <!-- RL/LY Quantity Input -->
                                    <div class="rl-ly-quantity-input" id="rlLyQuantityInput" style="display: none;">
                                        <input type="number" class="form-control text-center"
                                               id="rl_ly_quantity" name="rl_ly_quantity"
                                               placeholder="Enter qty" min="1" step="1">
                                        <small class="text-muted mt-1">Override quantity</small>
                                    </div>
                                </div>

                                <!-- Lot Type Controls -->
                                <div class="lot-type-group">
                                    <label class="form-label fw-bold">Lot Type</label>
                                    <div class="lot-type-controls">
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="lot_type_override" id="lotTypeMain" value="MAIN" checked>
                                            <label class="form-check-label" for="lotTypeMain">
                                                <span class="badge bg-info">MAIN</span>
                                            </label>
                                        </div>
                                        <div class="form-check form-check-inline">
                                            <input class="form-check-input" type="radio" name="lot_type_override" id="lotTypeRlLy" value="RL/LY">
                                            <label class="form-check-label" for="lotTypeRlLy">
                                                <span class="badge bg-warning text-dark">RL/LY</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Lot Details Field -->
                                <div class="lot-details-group">
                                    <label class="form-label fw-bold">Lot Details</label>
                                    <textarea class="form-control" id="lotDetailsTextArea" rows="3" readonly
                                              placeholder="Lot details will appear here after entering Lot ID..."
                                              style="resize: none; background-color: var(--bg-color); border: 1px solid var(--border-color); color: var(--text-color);"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                        <!-- Hidden form fields -->
                        <input type="hidden" id="model_15" name="model_15">
                        <input type="hidden" id="lot_size" name="lot_size" value="10">
                        <input type="hidden" id="lot_qty" name="lot_qty">
                        <input type="hidden" id="work_type" name="work_type" value="NOR">
                        <input type="hidden" id="lot_type" name="lot_type" value="MAIN">
                        <input type="hidden" id="lipas_yn" name="lipas_yn" value="N">
                    </div>

                    <!-- Enhanced Equipment Assignment Section -->
                    <div class="equipment-section mb-4">
                        <div class="equipment-section-header">
                            <div class="d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>Equipment Assignment & Loading Times
                                    <span class="badge bg-primary ms-2" id="equipmentCount">1 Mc's</span>
                                </h6>
                                <div class="equipment-controls">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="addEquipmentBtn">
                                        <i class="fas fa-plus me-1"></i>Add Equipment
                                    </button>
                                </div>
                            </div>
                            <small class="text-muted mt-1 d-block">Configure equipment and loading schedules for endtime calculation</small>
                        </div>

                        <!-- Equipment Table Container -->
                        <div id="equipmentAssignments" class="equipment-table-container">
                            <div class="table-responsive">
                                <table class="table table-sm mb-0" id="equipmentTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 40px;">#</th>
                                            <th style="width: 140px;">Equipment <span class="text-danger">*</span></th>
                                            <th style="width: 80px;">NG %</th>
                                            <th style="width: 180px;">Start Time <span class="text-danger">*</span></th>
                                            <th style="width: 120px;">Capacity</th>
                                            <th style="width: 100px;">Est. End</th>
                                            <th style="width: 40px;"></th>
                                        </tr>
                                    </thead>
                                    <tbody id="equipmentTableBody">
                                        <!-- Equipment Row 1 -->
                                        <tr class="equipment-row" data-index="1">
                                            <td class="text-center fw-bold text-primary">1</td>
                                            <td>
                                                <div class="position-relative">
                                                    <input type="text" class="form-control form-control-sm equipment-search"
                                                           id="equipment_1" name="equipment[1][eqp_no]"
                                                           placeholder="Search..."
                                                           autocomplete="off" required>
                                                    <div class="equipment-dropdown d-none" id="equipmentDropdown_1">
                                                        <!-- Dynamic equipment options -->
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group input-group-sm">
                                                    <input type="number" class="form-control text-center"
                                                           id="ng_percent_1" name="equipment[1][ng_percent]"
                                                           value="0" min="0" max="100" step="0.1">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="input-group input-group-sm">
                                                    <input type="datetime-local" class="form-control"
                                                           id="start_time_1" name="equipment[1][start_time]" step="60" required>
                                                    <button type="button" class="btn btn-outline-secondary time-now-btn"
                                                            title="Set to current time" data-equipment-index="1">
                                                        <i class="fas fa-clock"></i>
                                                    </button>
                                                </div>
                                            </td>
                                            <td class="equipment-capacity" id="equipmentCapacity_1">
                                                <small class="text-muted">--</small>
                                            </td>
                                            <td class="equipment-endtime" id="equipmentEndTime_1">
                                                <small class="text-muted">--</small>
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-outline-danger equipment-remove-btn"
                                                        style="display: none;" data-equipment-index="1" title="Remove">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Calculation Results -->
                    <div class="results-card mb-4" id="calculationResults" style="display: none;">
                        <div class="results-section-header">
                            <h6 class="mb-0"><i class="fas fa-calculator me-2"></i>Calculation Results</h6>
                        </div>
                        <div class="results-grid">
                            <div class="result-item primary-result">
                                <label class="result-label">Estimated End Time</label>
                                <div class="result-value" id="estimatedEndtime">--</div>
                                <small class="result-subtitle" id="estimatedEndtimeRelative">--</small>
                            </div>
                            <div class="result-stats">
                                <div class="stat-item">
                                    <i class="fas fa-cogs me-1"></i>
                                    <span class="stat-label">Equipment:</span>
                                    <span class="stat-value" id="totalEquipment">0</span>
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-tachometer-alt me-1"></i>
                                    <span class="stat-label">Capacity:</span>
                                    <span class="stat-value" id="combinedCapacity">0</span> pcs/day
                                </div>
                                <div class="stat-item">
                                    <i class="fas fa-clock me-1"></i>
                                    <span class="stat-label">Duration:</span>
                                    <span class="stat-value" id="estimatedDuration">0</span> hrs
                                </div>
                            </div>
                        </div>
                        <div id="equipmentBreakdown" class="equipment-breakdown"></div>
                    </div>

                    <!-- Form Actions -->
                    <div class="form-actions d-flex justify-content-end gap-2">
                        <a href="<?php echo e(route('endtime.index')); ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitForecastBtn" disabled>
                            <i class="fas fa-save me-2"></i>Save Lot Entry
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>


    <!-- JavaScript Integration -->
    <script>
        // Inject available equipment data for JavaScript consumption
        window.availableEquipment = <?php echo json_encode($availableEquipment ?? [], 15, 512) ?>;

        console.log('🔧 Equipment data loaded:', window.availableEquipment?.length || 0, 'items');
        console.log('📋 Equipment data sample:', window.availableEquipment?.slice(0, 3));

        // Debug helper function
        window.debugEquipmentDropdown = function(index = 1) {
            console.log('🔍 DEBUG: Equipment dropdown debug for index', index);
            const input = document.getElementById(`equipment_${index}`);
            const dropdown = document.getElementById(`equipmentDropdown_${index}`);

            console.log('Input element:', input);
            console.log('Dropdown element:', dropdown);
            console.log('Input value:', input?.value);
            console.log('Dropdown classes:', dropdown?.className);
            console.log('Dropdown innerHTML length:', dropdown?.innerHTML.length);
            console.log('Available equipment count:', window.availableEquipment?.length || 0);

            if (input && dropdown && window.availableEquipment?.length > 0) {
                console.log('✅ All elements ready, forcing search...');
                searchEquipment(index);
            } else {
                console.log('❌ Missing elements or data:', {
                    hasInput: !!input,
                    hasDropdown: !!dropdown,
                    hasData: !!(window.availableEquipment?.length)
                });
            }
        };

        // Global variables
        let equipmentRowCount = 1;
        const maxEquipmentRows = 10;

        // Initialize page when DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 Initializing Endtime Page...');

            try {
                // Set current time for initial row
                setCurrentTimeToInput('start_time_1');

                // Initialize lot ID lookup
                setupLotIdLookup();

                // Initialize equipment search for existing row
                console.log('🔎 Setting up equipment search...');
                setupEquipmentSearch();

                // Initialize add equipment button
                setupAddEquipmentButton();

                // Initialize time buttons
                setupTimeButtons();

                // Initialize lot type toggles
                setupLotTypeToggles();

                // Add change listeners to existing inputs
                addChangeListeners();

                // Debug initial setup
                console.log('📋 Initial equipment input check:');
                const initialInput = document.getElementById('equipment_1');
                const initialDropdown = document.getElementById('equipmentDropdown_1');
                console.log('Initial input found:', !!initialInput);
                console.log('Initial dropdown found:', !!initialDropdown);

                // Add test functions to window for manual testing
                window.testEquipmentSearch = function() {
                    console.log('🧪 Testing equipment search manually...');
                    const input = document.getElementById('equipment_1');
                    if (input) {
                        input.value = 'v';
                        input.focus();
                        input.dispatchEvent(new Event('input', { bubbles: true }));

                        setTimeout(() => {
                            console.log('🔍 Dropdown classes after search:', document.getElementById('equipmentDropdown_1')?.className);
                        }, 100);
                    } else {
                        console.error('❌ Input equipment_1 not found');
                    }
                };

                window.forceShowDropdown = function(index = 1) {
                    console.log('💪 Forcing dropdown to show for index:', index);
                    const dropdown = document.getElementById(`equipmentDropdown_${index}`);
                    const input = document.getElementById(`equipment_${index}`);
                    if (dropdown && input) {
                        dropdown.innerHTML = `
                            <div class="p-2 text-success border-bottom">
                                <strong>🧪 Test Dropdown for Index ${index}</strong>
                            </div>
                            <div class="dropdown-item" onclick="console.log('Test item clicked!')">
                                <strong>TEST-EQUIPMENT-${index}</strong><br>
                                <small class="text-muted">Test Line - Test Area | 10,000 pcs/day</small>
                            </div>
                            <div class="dropdown-item" onclick="console.log('Another test item clicked!')">
                                <strong>DEMO-UNIT-${index}</strong><br>
                                <small class="text-muted">Demo Line - Demo Area | 15,000 pcs/day</small>
                            </div>
                        `;
                        dropdown.classList.remove('d-none');
                        showDropdown(dropdown, input);
                        console.log('✅ Dropdown forced to show with test content for index:', index);
                    } else {
                        console.error('❌ Elements not found for index:', index, { dropdown: !!dropdown, input: !!input });
                    }
                };

                window.forceShowAllDropdowns = function() {
                    console.log('🎆 Forcing all dropdowns to show...');
                    const inputs = document.querySelectorAll('.equipment-search');
                    inputs.forEach((input, idx) => {
                        const index = input.id.split('_')[1];
                        setTimeout(() => forceShowDropdown(index), idx * 100);
                    });
                };

                console.log('✅ Endtime page initialized successfully');
                console.log('💡 Debug helpers available:');
                console.log('   - debugEquipmentDropdown(index) - Debug dropdown for specific index');
                console.log('   - testEquipmentSearch() - Test search functionality');
                console.log('   - forceShowDropdown(index) - Force dropdown to appear with test content');
                console.log('   - forceShowAllDropdowns() - Force all equipment dropdowns to appear');

                // Additional initialization delay for complex DOM
                setTimeout(() => {
                    console.log('🔄 Running delayed setup verification...');
                    const input = document.getElementById('equipment_1');
                    const dropdown = document.getElementById('equipmentDropdown_1');
                    console.log('Delayed check - Input:', !!input, 'Dropdown:', !!dropdown);

                    if (input && dropdown) {
                        console.log('✅ Equipment elements ready after delay');
                    } else {
                        console.error('❌ Equipment elements still missing after delay');

                        // Try to find all elements with equipment in the ID
                        const allElements = document.querySelectorAll('[id*="equipment"]');
                        console.log('🔍 All elements with "equipment" in ID:', Array.from(allElements).map(el => el.id));
                    }
                }, 500);

            } catch (error) {
                console.error('❌ Error initializing page:', error);
                console.error('Error details:', error.stack);
            }
        });

        // Helper function to set current time to input
        function setCurrentTimeToInput(inputId) {
            const input = document.getElementById(inputId);
            if (input) {
                const now = new Date();
                const currentDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
                input.value = currentDateTime;
                console.log('⏰ Set current time to', inputId);
            }
        }

        // Setup lot ID lookup
        function setupLotIdLookup() {
            const lotIdInput = document.getElementById('lot_id');
            if (lotIdInput) {
                console.log('🔍 Setting up lot ID lookup');
                lotIdInput.addEventListener('blur', handleLotLookup);
                lotIdInput.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        handleLotLookup();
                    }
                });
            }
        }

        // Handle lot lookup
        async function handleLotLookup() {
            const lotIdInput = document.getElementById('lot_id');
            if (!lotIdInput) return;

            const lotId = lotIdInput.value.trim().toUpperCase();
            console.log('Looking up lot:', lotId);

            if (!lotId || lotId.length < 3) {
                clearLotData();
                return;
            }

            // Show loading
            const lotDetailsTextArea = document.getElementById('lotDetailsTextArea');
            if (lotDetailsTextArea) {
                lotDetailsTextArea.value = 'Loading lot details...';
            }

            try {
                const response = await fetch(`/endtime/lookup/${lotId}`, {
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.lot) {
                        populateLotData(data.lot);
                        lotIdInput.classList.add('is-valid');
                        lotIdInput.classList.remove('is-invalid');
                        console.log('✅ Lot data loaded successfully');
                    } else {
                        clearLotData();
                        lotIdInput.classList.add('is-invalid');
                        alert(data.message || 'Lot not found');
                    }
                } else {
                    throw new Error('Network error');
                }
            } catch (error) {
                console.error('❌ Lot lookup error:', error);
                clearLotData();
                lotIdInput.classList.add('is-invalid');
                alert('Error looking up lot: ' + error.message);
            }
        }

        // Populate lot data
        function populateLotData(lotData) {
            console.log('📝 Populating lot data:', lotData);

            // Update hidden fields
            const fields = {
                'model_15': lotData.model_15 || '',
                'lot_size': lotData.lot_size || '10',
                'lot_qty': lotData.lot_qty || '',
                'work_type': lotData.work_type || 'NOR',
                'lot_type': lotData.lot_type || 'MAIN',
                'lipas_yn': lotData.lipas_yn || 'N'
            };

            Object.keys(fields).forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) field.value = fields[fieldId];
            });

            // Update lot quantity display
            const lotQtyValue = document.getElementById('lotQtyValue');
            if (lotQtyValue && lotData.lot_qty) {
                lotQtyValue.textContent = parseInt(lotData.lot_qty).toLocaleString();
            }

            // Update lot details textarea
            const lotDetailsTextArea = document.getElementById('lotDetailsTextArea');
            if (lotDetailsTextArea) {
                const details = [
                    `Lot ID: ${lotData.lot_id}`,
                    `Model: ${lotData.model_15 || 'N/A'}`,
                    `Size: ${lotData.lot_size || 'N/A'}`,
                    `Quantity: ${lotData.lot_qty ? parseInt(lotData.lot_qty).toLocaleString() : 'N/A'} pcs`,
                    `Work Type: ${lotData.work_type || 'N/A'}`,
                    `Lot Type: ${lotData.lot_type || 'N/A'}`,
                    `LIPAS: ${lotData.lipas_yn || 'N'}`
                ];
                lotDetailsTextArea.value = details.join('\n');
            }

            updateSubmitButtonState();
        }

        // Clear lot data
        function clearLotData() {
            const fields = ['model_15', 'lot_size', 'lot_qty', 'work_type', 'lot_type', 'lipas_yn'];
            fields.forEach(fieldId => {
                const field = document.getElementById(fieldId);
                if (field) field.value = fieldId === 'lot_size' ? '10' : (fieldId === 'work_type' ? 'NOR' : (fieldId === 'lot_type' ? 'MAIN' : (fieldId === 'lipas_yn' ? 'N' : '')));
            });

            const lotQtyValue = document.getElementById('lotQtyValue');
            if (lotQtyValue) lotQtyValue.textContent = '--';

            const lotDetailsTextArea = document.getElementById('lotDetailsTextArea');
            if (lotDetailsTextArea) {
                lotDetailsTextArea.value = 'Lot details will appear here after entering Lot ID...';
            }

            updateSubmitButtonState();
        }

        // Setup equipment search for existing rows
        function setupEquipmentSearch() {
            console.log('⚙️ Setting up equipment search');
            const equipmentInputs = document.querySelectorAll('.equipment-search');

            console.log('📋 Found', equipmentInputs.length, 'equipment inputs');

            equipmentInputs.forEach((input, inputIndex) => {
                const index = input.id.split('_')[1];
                console.log(`🔧 Setting up equipment search for index: ${index} (element ${inputIndex + 1}/${equipmentInputs.length})`);
                console.log('Input ID:', input.id);
                console.log('Input placeholder:', input.placeholder);

                // Remove any existing listeners
                const newInput = input.cloneNode(true);
                input.parentNode.replaceChild(newInput, input);

                console.log('🔄 Replaced input element for index:', index);

                // Add event listeners with debugging
                newInput.addEventListener('input', function() {
                    console.log('⌨️ Input event triggered for index:', index, 'value:', this.value);
                    searchEquipment(index);
                });

                newInput.addEventListener('focus', function() {
                    console.log('🎯 Focus event triggered for index:', index, 'value:', this.value);
                    if (this.value.length > 0) {
                        console.log('🔍 Triggering search on focus for index:', index);
                        searchEquipment(index);
                    }
                });

                newInput.addEventListener('blur', function() {
                    console.log('🔲 Blur event triggered for index:', index);
                    setTimeout(() => {
                        hideEquipmentDropdown(index);
                        // Only validate if there's no selected value to preserve
                        if (!this.dataset.selectedValue || this.value !== this.dataset.selectedValue) {
                            console.log('🔍 Validating equipment selection for index:', index);
                            validateEquipmentSelection(index);
                        } else {
                            console.log('🔒 Preserving selected value for index:', index, this.dataset.selectedValue);
                        }
                    }, 200);
                });

                // Prevent input from changing when equipment is already selected
                newInput.addEventListener('keydown', function(e) {
                    if (this.dataset.selectedValue && this.value === this.dataset.selectedValue) {
                        console.log('⌨️ Keydown on selected equipment:', e.key, 'for index:', index);
                        // Allow backspace/delete to clear selection
                        if (e.key === 'Backspace' || e.key === 'Delete') {
                            console.log('🗑️ Clearing selected equipment for index:', index);
                            delete this.dataset.selectedValue;
                            delete this.dataset.equipmentData;
                            this.classList.remove('is-valid');
                        }
                    }
                });

                // Test the input after setup
                console.log('📋 Event listeners added for index:', index);
            });

            console.log('✅ Equipment search setup completed for', equipmentInputs.length, 'inputs');
        }

        // Search equipment function
        function searchEquipment(index) {
            console.log('🔍 searchEquipment called for index:', index);

            const input = document.getElementById(`equipment_${index}`);
            const dropdown = document.getElementById(`equipmentDropdown_${index}`);

            console.log('📋 Elements check:', {
                input: !!input,
                dropdown: !!dropdown,
                inputId: input?.id,
                dropdownId: dropdown?.id
            });

            if (!input || !dropdown) {
                console.error('❌ Equipment input or dropdown not found for index:', index, {
                    inputExists: !!input,
                    dropdownExists: !!dropdown
                });
                return;
            }

            const searchTerm = input.value.toLowerCase().trim();
            console.log('🔎 Searching equipment:', {
                searchTerm: searchTerm,
                index: index,
                inputValue: input.value,
                searchLength: searchTerm.length
            });

            if (searchTerm.length < 1) {
                console.log('🚫 Search term too short, hiding dropdown');
                dropdown.classList.add('d-none');
                return;
            }

            console.log('📋 Equipment data check:', {
                hasData: !!window.availableEquipment,
                dataLength: window.availableEquipment?.length || 0,
                dataType: typeof window.availableEquipment
            });

            if (!window.availableEquipment || window.availableEquipment.length === 0) {
                console.warn('⚠️ Equipment data not available');
                dropdown.innerHTML = '<div class="p-2 text-warning">Equipment data not available</div>';
                showDropdown(dropdown, input);
                return;
            }

            // Filter equipment
            const matches = window.availableEquipment.filter(eqp => {
                if (!eqp) return false;
                const eqpNo = (eqp.eqp_no || '').toLowerCase();
                const eqpLine = (eqp.eqp_line || '').toLowerCase();
                const eqpArea = (eqp.eqp_area || '').toLowerCase();
                return eqpNo.includes(searchTerm) || eqpLine.includes(searchTerm) || eqpArea.includes(searchTerm);
            });

            console.log('Found', matches.length, 'equipment matches');

            // Populate dropdown
            dropdown.innerHTML = '';

            if (matches.length === 0) {
                dropdown.innerHTML = `<div class="p-2 text-muted">No equipment found for "${searchTerm}"</div>`;
            } else {
                matches.slice(0, 10).forEach(eqp => {
                    const option = document.createElement('div');
                    option.className = 'dropdown-item';
                    option.style.cursor = 'pointer';
                    option.style.padding = '8px 12px';
                    option.style.borderBottom = '1px solid #f0f0f0';

                    const capacity = calculateEquipmentCapacity(eqp);
                    const capacityText = capacity > 0 ? capacity.toLocaleString() + ' pcs/day' : 'N/A';

                    option.innerHTML = `
                        <div>
                            <strong style="color: #007bff;">${eqp.eqp_no}</strong><br>
                            <small class="text-muted">${eqp.eqp_line} - ${eqp.eqp_area} | ${capacityText}</small>
                        </div>
                    `;

                    // Store equipment data on the option for easy access
                    option.dataset.equipmentData = JSON.stringify(eqp);

                    option.addEventListener('mousedown', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        console.log('💆 Equipment option clicked:', eqp.eqp_no);
                        selectEquipment(index, eqp);
                    });

                    option.addEventListener('click', function(e) {
                        e.preventDefault();
                        e.stopPropagation();
                        selectEquipment(index, eqp);
                    });

                    option.addEventListener('mouseenter', function() {
                        this.style.backgroundColor = '#f8f9fa';
                    });

                    option.addEventListener('mouseleave', function() {
                        this.style.backgroundColor = 'white';
                    });

                    dropdown.appendChild(option);
                });
            }

            showDropdown(dropdown, input);
        }

        // Create dropdown portal to avoid clipping issues
        function createDropdownPortal() {
            let portal = document.getElementById('equipment-dropdown-portal');
            if (!portal) {
                portal = document.createElement('div');
                portal.id = 'equipment-dropdown-portal';
                portal.className = 'equipment-dropdown-portal';
                document.body.appendChild(portal);
                console.log('🌌 Created dropdown portal');
            }
            return portal;
        }

        // Show dropdown with enhanced portal positioning
        function showDropdown(dropdown, input) {
            if (!dropdown || !input) {
                console.error('❌ showDropdown: Missing elements', { dropdown: !!dropdown, input: !!input });
                return;
            }

            console.log('📍 showDropdown called for:', input.id);

            // Create portal if it doesn't exist
            const portal = createDropdownPortal();

            // Move dropdown to portal (outside table structure)
            if (dropdown.parentElement !== portal) {
                portal.appendChild(dropdown);
                console.log('🚚 Moved dropdown to portal');
            }

            // Remove d-none class and force show
            dropdown.classList.remove('d-none');
            dropdown.style.display = 'block';

            // Get input position
            const inputRect = input.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const dropdownHeight = 300; // max height from CSS

            // Calculate optimal position
            let topPosition = inputRect.bottom + 2;
            if (topPosition + dropdownHeight > viewportHeight) {
                topPosition = Math.max(10, inputRect.top - dropdownHeight - 2);
                console.log('🔄 Dropdown positioned above input to stay in viewport');
            }

            // Apply enhanced styling
            Object.assign(dropdown.style, {
                position: 'fixed',
                left: inputRect.left + 'px',
                top: topPosition + 'px',
                width: Math.max(300, inputRect.width) + 'px',
                zIndex: '999999',
                maxHeight: '300px',
                overflow: 'auto',
                border: '2px solid #007bff',
                backgroundColor: 'white',
                boxShadow: '0 8px 16px rgba(0,0,0,0.2)',
                borderRadius: '0.375rem',
                opacity: '1',
                visibility: 'visible',
                pointerEvents: 'auto',
                transform: 'none'
            });

            console.log('📍 Positioned dropdown at:', {
                left: inputRect.left,
                top: topPosition,
                width: Math.max(300, inputRect.width),
                viewport: viewportHeight,
                inputBottom: inputRect.bottom,
                dropdownId: dropdown.id
            });

            // Immediate visibility verification
            const computedStyle = window.getComputedStyle(dropdown);
            console.log('🔍 Immediate dropdown styles:', {
                display: computedStyle.display,
                visibility: computedStyle.visibility,
                opacity: computedStyle.opacity,
                zIndex: computedStyle.zIndex,
                position: computedStyle.position,
                top: computedStyle.top,
                left: computedStyle.left
            });

            // Force reflow to ensure visibility
            dropdown.offsetHeight;

            // Additional check after brief delay
            setTimeout(() => {
                const finalStyle = window.getComputedStyle(dropdown);
                console.log('🔍 Final dropdown check:', {
                    display: finalStyle.display,
                    visible: finalStyle.visibility !== 'hidden' && finalStyle.display !== 'none',
                    hasContent: dropdown.innerHTML.length > 0
                });
            }, 50);
        }

        // Select equipment from dropdown
        function selectEquipment(index, equipment) {
            console.log('🎯 Selecting equipment:', equipment.eqp_no, 'for index:', index);

            const input = document.getElementById(`equipment_${index}`);
            const dropdown = document.getElementById(`equipmentDropdown_${index}`);

            if (input && equipment && equipment.eqp_no) {
                // Set the input value to the full equipment number
                input.value = equipment.eqp_no;

                // Store the equipment data
                input.dataset.equipmentData = JSON.stringify(equipment);
                input.dataset.selectedValue = equipment.eqp_no; // Store selected value to prevent blur events from changing it

                // Update validation classes
                input.classList.add('is-valid');
                input.classList.remove('is-invalid');

                console.log('✅ Equipment input updated:', {
                    value: input.value,
                    equipment: equipment.eqp_no
                });
            }

            // Hide dropdown
            if (dropdown) {
                dropdown.classList.add('d-none');
                console.log('🔽 Dropdown hidden for index:', index);
            }

            // Update capacity display
            updateCapacityDisplay(index, equipment);

            // Update submit button
            updateSubmitButtonState();

            // Calculate endtime
            calculateEstimatedEndTime();

            // Remove focus from input to prevent further changes
            setTimeout(() => {
                if (input) {
                    input.blur();
                }
            }, 50);
        }

        // Hide equipment dropdown
        function hideEquipmentDropdown(index) {
            const dropdown = document.getElementById(`equipmentDropdown_${index}`);
            if (dropdown) {
                dropdown.classList.add('d-none');
            }
        }

        // Validate equipment selection
        function validateEquipmentSelection(index) {
            const input = document.getElementById(`equipment_${index}`);
            if (!input) return;

            const inputValue = input.value.trim();

            if (!inputValue) {
                input.classList.remove('is-valid', 'is-invalid');
                return;
            }

            // Check if equipment exists
            const equipment = window.availableEquipment?.find(eqp =>
                eqp.eqp_no && eqp.eqp_no.toLowerCase() === inputValue.toLowerCase()
            );

            if (equipment) {
                selectEquipment(index, equipment);
            } else {
                input.classList.remove('is-valid');
                input.classList.add('is-invalid');
                delete input.dataset.equipmentData;
                clearCapacityDisplay(index);
            }
        }

        // Calculate equipment capacity
        function calculateEquipmentCapacity(equipment) {
            if (!equipment) return 0;

            if (equipment.daily_capa && typeof equipment.daily_capa === 'number') {
                return equipment.daily_capa;
            }

            // Fallback calculation
            let oee = parseFloat(equipment.eqp_oee) || 0;
            if (oee > 1) oee = oee / 100;

            const speed = parseFloat(String(equipment.eqp_speed).replace(/[^0-9.]/g, '')) || 0;
            const operationTime = parseFloat(String(equipment.operation_time).replace(/[^0-9.]/g, '')) || 0;

            return Math.round(oee * speed * operationTime);
        }

        // Update capacity display
        function updateCapacityDisplay(index, equipment) {
            const capacityElement = document.getElementById(`equipmentCapacity_${index}`);
            if (capacityElement) {
                const capacity = calculateEquipmentCapacity(equipment);
                if (capacity > 0) {
                    capacityElement.innerHTML = `<strong>${capacity.toLocaleString()}</strong><br><small class="text-muted">pcs/day</small>`;
                } else {
                    capacityElement.innerHTML = '<small class="text-muted">N/A</small>';
                }
            }
        }

        // Clear capacity display
        function clearCapacityDisplay(index) {
            const capacityElement = document.getElementById(`equipmentCapacity_${index}`);
            if (capacityElement) {
                capacityElement.innerHTML = '<small class="text-muted">--</small>';
            }
        }

        // Setup add equipment button
        function setupAddEquipmentButton() {
            const addBtn = document.getElementById('addEquipmentBtn');
            if (addBtn) {
                console.log('➕ Setting up add equipment button');
                addBtn.addEventListener('click', addEquipmentRow);
            }
        }

        // Add equipment row
        function addEquipmentRow() {
            if (equipmentRowCount >= maxEquipmentRows) {
                alert(`Maximum ${maxEquipmentRows} equipment allowed`);
                return;
            }

            equipmentRowCount++;
            console.log('➕ Adding equipment row:', equipmentRowCount);

            const tableBody = document.getElementById('equipmentTableBody');
            if (!tableBody) {
                console.error('Equipment table body not found');
                return;
            }

            const now = new Date();
            const currentDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);

            const newRow = document.createElement('tr');
            newRow.className = 'equipment-row';
            newRow.setAttribute('data-index', equipmentRowCount);

            newRow.innerHTML = `
                <td class="text-center fw-bold text-primary">${equipmentRowCount}</td>
                <td>
                    <div class="position-relative">
                        <input type="text" class="form-control form-control-sm equipment-search"
                               id="equipment_${equipmentRowCount}" name="equipment[${equipmentRowCount}][eqp_no]"
                               placeholder="Search..." autocomplete="off" required>
                        <div class="equipment-dropdown d-none" id="equipmentDropdown_${equipmentRowCount}">
                        </div>
                    </div>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="number" class="form-control text-center"
                               id="ng_percent_${equipmentRowCount}" name="equipment[${equipmentRowCount}][ng_percent]"
                               value="0" min="0" max="100" step="0.1">
                        <span class="input-group-text">%</span>
                    </div>
                </td>
                <td>
                    <div class="input-group input-group-sm">
                        <input type="datetime-local" class="form-control"
                               id="start_time_${equipmentRowCount}" name="equipment[${equipmentRowCount}][start_time]"
                               step="60" value="${currentDateTime}" required>
                        <button type="button" class="btn btn-outline-secondary time-now-btn"
                                title="Set to current time" data-equipment-index="${equipmentRowCount}">
                            <i class="fas fa-clock"></i>
                        </button>
                    </div>
                </td>
                <td class="equipment-capacity" id="equipmentCapacity_${equipmentRowCount}">
                    <small class="text-muted">--</small>
                </td>
                <td class="equipment-endtime" id="equipmentEndTime_${equipmentRowCount}">
                    <small class="text-muted">--</small>
                </td>
                <td>
                    <button type="button" class="btn btn-sm btn-outline-danger equipment-remove-btn"
                            data-equipment-index="${equipmentRowCount}" title="Remove">
                        <i class="fas fa-times"></i>
                    </button>
                </td>
            `;

            tableBody.appendChild(newRow);

            // Initialize the new row
            setTimeout(() => {
                setupEquipmentSearch();
                setupTimeButtons();
                updateEquipmentCounter();
                updateRemoveButtonVisibility();

                // Add change listeners for calculation
                const newStartTimeInput = document.getElementById(`start_time_${equipmentRowCount}`);
                if (newStartTimeInput) {
                    newStartTimeInput.addEventListener('change', calculateEstimatedEndTime);
                }

                const newNgInput = document.getElementById(`ng_percent_${equipmentRowCount}`);
                if (newNgInput) {
                    newNgInput.addEventListener('change', calculateEstimatedEndTime);
                }
            }, 100);
        }

        // Setup time buttons
        function setupTimeButtons() {
            console.log('⏰ Setting up time buttons');

            // Time now buttons
            document.querySelectorAll('.time-now-btn').forEach(btn => {
                const newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);

                newBtn.addEventListener('click', function() {
                    const index = this.getAttribute('data-equipment-index');
                    setCurrentTimeToInput(`start_time_${index}`);
                    calculateEstimatedEndTime();
                });
            });

            // Remove buttons
            document.querySelectorAll('.equipment-remove-btn').forEach(btn => {
                const newBtn = btn.cloneNode(true);
                btn.parentNode.replaceChild(newBtn, btn);

                newBtn.addEventListener('click', function() {
                    const index = this.getAttribute('data-equipment-index');
                    removeEquipmentRow(index);
                });
            });
        }

        // Remove equipment row
        function removeEquipmentRow(index) {
            const row = document.querySelector(`[data-index="${index}"]`);
            if (row) {
                row.remove();
                console.log('❌ Removed equipment row:', index);
                updateEquipmentCounter();
                updateRemoveButtonVisibility();
                updateSubmitButtonState();
                calculateEstimatedEndTime();
            }
        }

        // Update equipment counter
        function updateEquipmentCounter() {
            const rows = document.querySelectorAll('.equipment-row');
            const counter = document.getElementById('equipmentCount');
            if (counter) {
                const count = rows.length;
                counter.textContent = `${count} Unit${count !== 1 ? 's' : ''}`;
            }
        }

        // Update remove button visibility
        function updateRemoveButtonVisibility() {
            const rows = document.querySelectorAll('.equipment-row');
            const removeButtons = document.querySelectorAll('.equipment-remove-btn');

            removeButtons.forEach(btn => {
                btn.style.display = rows.length > 1 ? 'inline-block' : 'none';
            });
        }

        // Setup lot type toggles
        function setupLotTypeToggles() {
            console.log('♾️ Setting up lot type toggles');

            const lotTypeMain = document.getElementById('lotTypeMain');
            const lotTypeRlLy = document.getElementById('lotTypeRlLy');
            const defaultQtyDisplay = document.getElementById('defaultQtyDisplay');
            const rlLyQuantityInput = document.getElementById('rlLyQuantityInput');

            if (lotTypeMain) {
                lotTypeMain.addEventListener('change', function() {
                    if (this.checked && defaultQtyDisplay && rlLyQuantityInput) {
                        defaultQtyDisplay.style.display = 'flex';
                        rlLyQuantityInput.style.display = 'none';
                    }
                });
            }

            if (lotTypeRlLy) {
                lotTypeRlLy.addEventListener('change', function() {
                    if (this.checked && defaultQtyDisplay && rlLyQuantityInput) {
                        defaultQtyDisplay.style.display = 'none';
                        rlLyQuantityInput.style.display = 'flex';
                    }
                });
            }
        }

        // Update submit button state
        function updateSubmitButtonState() {
            const submitBtn = document.getElementById('submitForecastBtn');
            if (!submitBtn) return;

            const lotId = document.getElementById('lot_id')?.value?.trim();
            const lotQty = document.getElementById('lot_qty')?.value;

            let hasValidEquipment = false;
            document.querySelectorAll('.equipment-search').forEach(input => {
                if (input.value.trim() && input.dataset.equipmentData) {
                    hasValidEquipment = true;
                }
            });

            const isValid = lotId && lotQty && parseInt(lotQty) > 0 && hasValidEquipment;
            submitBtn.disabled = !isValid;

            console.log('Submit button state:', { lotId, lotQty, hasValidEquipment, isValid });
        }

        // Add change listeners to existing inputs
        function addChangeListeners() {
            console.log('🔊 Adding change listeners to inputs');

            // Start time inputs
            document.querySelectorAll('input[type="datetime-local"]').forEach(input => {
                input.addEventListener('change', calculateEstimatedEndTime);
            });

            // NG percentage inputs
            document.querySelectorAll('input[name*="ng_percent"]').forEach(input => {
                input.addEventListener('change', calculateEstimatedEndTime);
            });
        }

        // Simple endtime calculation
        function calculateEstimatedEndTime() {
            console.log('📊 Calculating estimated end time...');
            // This is a placeholder - you can implement the full calculation later
            // For now, just show current time + 2 hours as example

            const equipmentRows = document.querySelectorAll('.equipment-row');
            equipmentRows.forEach(row => {
                const index = row.dataset.index;
                const endtimeCell = document.getElementById(`equipmentEndTime_${index}`);
                const startTimeInput = document.getElementById(`start_time_${index}`);

                if (endtimeCell && startTimeInput && startTimeInput.value) {
                    const startTime = new Date(startTimeInput.value);
                    const endTime = new Date(startTime.getTime() + (2 * 60 * 60 * 1000)); // +2 hours

                    const formattedTime = endTime.toLocaleString('en-US', {
                        month: 'short',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        hour12: false
                    });

                    endtimeCell.innerHTML = `
                        <div class="text-center">
                            <strong class="text-primary">${formattedTime}</strong><br>
                            <small class="text-muted">+2h (est.)</small>
                        </div>
                    `;
                }
            });
        }

    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Project\process-dashboard\resources\views/endtime/create.blade.php ENDPATH**/ ?>