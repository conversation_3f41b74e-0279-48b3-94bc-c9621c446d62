<x-app-layout>
    <x-slot name="header">
        Security Settings
    </x-slot>

    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('management.settings.index') }}">Settings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Security</li>
                </ol>
            </nav>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-shield-alt me-2"></i>Security Configuration
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.settings.update.security') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row g-4">
                            <!-- Password Requirements -->
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">Password Requirements</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="password_min_length" class="form-label">Minimum Length <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <input type="number" 
                                           class="form-control @error('password_min_length') is-invalid @enderror" 
                                           id="password_min_length" 
                                           name="password_min_length" 
                                           value="{{ old('password_min_length', $settings['password_min_length']) }}" 
                                           min="6" 
                                           max="32" 
                                           required>
                                    <span class="input-group-text">characters</span>
                                </div>
                                <div class="form-text">Minimum 6, maximum 32 characters</div>
                                @error('password_min_length')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label class="form-label">Password Complexity</label>
                                <div class="password-requirements">
                                    <div class="form-check">
                                        <input class="form-check-input @error('password_require_uppercase') is-invalid @enderror" 
                                               type="checkbox" 
                                               id="password_require_uppercase" 
                                               name="password_require_uppercase" 
                                               value="1"
                                               {{ old('password_require_uppercase', $settings['password_require_uppercase']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_uppercase">
                                            Require uppercase letters (A-Z)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input @error('password_require_lowercase') is-invalid @enderror" 
                                               type="checkbox" 
                                               id="password_require_lowercase" 
                                               name="password_require_lowercase" 
                                               value="1"
                                               {{ old('password_require_lowercase', $settings['password_require_lowercase']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_lowercase">
                                            Require lowercase letters (a-z)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input @error('password_require_numbers') is-invalid @enderror" 
                                               type="checkbox" 
                                               id="password_require_numbers" 
                                               name="password_require_numbers" 
                                               value="1"
                                               {{ old('password_require_numbers', $settings['password_require_numbers']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_numbers">
                                            Require numbers (0-9)
                                        </label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input @error('password_require_symbols') is-invalid @enderror" 
                                               type="checkbox" 
                                               id="password_require_symbols" 
                                               name="password_require_symbols" 
                                               value="1"
                                               {{ old('password_require_symbols', $settings['password_require_symbols']) ? 'checked' : '' }}>
                                        <label class="form-check-label" for="password_require_symbols">
                                            Require symbols (!@#$%^&*)
                                        </label>
                                    </div>
                                </div>
                                @error('password_require_uppercase')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                @error('password_require_lowercase')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                @error('password_require_numbers')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                                @error('password_require_symbols')
                                    <div class="invalid-feedback d-block">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Login Security -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Login Security</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="login_attempts_limit" class="form-label">Max Login Attempts <span class="text-danger">*</span></label>
                                <select class="form-select @error('login_attempts_limit') is-invalid @enderror" 
                                        id="login_attempts_limit" 
                                        name="login_attempts_limit" 
                                        required>
                                    <option value="">Select Max Attempts</option>
                                    @for($i = 3; $i <= 10; $i++)
                                        <option value="{{ $i }}" {{ old('login_attempts_limit', $settings['login_attempts_limit']) == $i ? 'selected' : '' }}>
                                            {{ $i }} attempts
                                        </option>
                                    @endfor
                                </select>
                                <div class="form-text">Number of failed attempts before lockout</div>
                                @error('login_attempts_limit')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="lockout_duration" class="form-label">Lockout Duration <span class="text-danger">*</span></label>
                                <div class="input-group">
                                    <select class="form-select @error('lockout_duration') is-invalid @enderror" 
                                            id="lockout_duration" 
                                            name="lockout_duration" 
                                            required>
                                        <option value="">Select Duration</option>
                                        @php
                                            $durations = [
                                                1 => '1 minute',
                                                5 => '5 minutes',
                                                10 => '10 minutes',
                                                15 => '15 minutes',
                                                30 => '30 minutes',
                                                60 => '1 hour',
                                            ];
                                        @endphp
                                        @foreach($durations as $value => $label)
                                            <option value="{{ $value }}" {{ old('lockout_duration', $settings['lockout_duration']) == $value ? 'selected' : '' }}>
                                                {{ $label }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="form-text">How long accounts remain locked</div>
                                @error('lockout_duration')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Two-Factor Authentication -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Two-Factor Authentication</h6>
                            </div>

                            <div class="col-12">
                                <div class="form-check form-switch">
                                    <input class="form-check-input @error('two_factor_enabled') is-invalid @enderror" 
                                           type="checkbox" 
                                           id="two_factor_enabled" 
                                           name="two_factor_enabled" 
                                           value="1"
                                           {{ old('two_factor_enabled', $settings['two_factor_enabled']) ? 'checked' : '' }}>
                                    <label class="form-check-label" for="two_factor_enabled">
                                        <strong>Enable Two-Factor Authentication</strong>
                                    </label>
                                    <div class="form-text">Require users to verify their identity with a second factor</div>
                                    @error('two_factor_enabled')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                @if($settings['two_factor_enabled'])
                                <div class="alert alert-info mt-3">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>Two-Factor Authentication is currently enabled.</strong>
                                    Users will be prompted to set up 2FA on their next login.
                                </div>
                                @else
                                <div class="alert alert-warning mt-3">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <strong>Two-Factor Authentication is currently disabled.</strong>
                                    Consider enabling it for enhanced security.
                                </div>
                                @endif
                            </div>

                            <!-- Security Recommendations -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Security Status</h6>
                            </div>

                            <div class="col-12">
                                <div class="security-checklist">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['password_min_length'] >= 8 ? 'check-circle text-success' : 'times-circle text-danger' }} me-2"></i>
                                                <span>Password length {{ $settings['password_min_length'] >= 8 ? 'adequate' : 'too short' }}</span>
                                            </div>
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ ($settings['password_require_uppercase'] && $settings['password_require_lowercase']) ? 'check-circle text-success' : 'times-circle text-danger' }} me-2"></i>
                                                <span>Mixed case {{ ($settings['password_require_uppercase'] && $settings['password_require_lowercase']) ? 'required' : 'not required' }}</span>
                                            </div>
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['password_require_numbers'] ? 'check-circle text-success' : 'times-circle text-danger' }} me-2"></i>
                                                <span>Numbers {{ $settings['password_require_numbers'] ? 'required' : 'not required' }}</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['password_require_symbols'] ? 'check-circle text-success' : 'times-circle text-danger' }} me-2"></i>
                                                <span>Symbols {{ $settings['password_require_symbols'] ? 'required' : 'not required' }}</span>
                                            </div>
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['login_attempts_limit'] <= 5 ? 'check-circle text-success' : 'times-circle text-warning' }} me-2"></i>
                                                <span>Login attempts {{ $settings['login_attempts_limit'] <= 5 ? 'appropriately limited' : 'could be stricter' }}</span>
                                            </div>
                                            <div class="security-item d-flex align-items-center mb-2">
                                                <i class="fas fa-{{ $settings['two_factor_enabled'] ? 'check-circle text-success' : 'times-circle text-warning' }} me-2"></i>
                                                <span>Two-factor auth {{ $settings['two_factor_enabled'] ? 'enabled' : 'disabled' }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12 mt-4">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('management.settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Current Security Settings</h6>
                </div>
                <div class="card-body">
                    <div class="security-overview">
                        <div class="mb-3">
                            <label class="form-label text-muted small">Password Length</label>
                            <p class="mb-0">{{ $settings['password_min_length'] }} characters minimum</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Password Requirements</label>
                            <ul class="list-unstyled small">
                                <li>{{ $settings['password_require_uppercase'] ? '✓' : '✗' }} Uppercase letters</li>
                                <li>{{ $settings['password_require_lowercase'] ? '✓' : '✗' }} Lowercase letters</li>
                                <li>{{ $settings['password_require_numbers'] ? '✓' : '✗' }} Numbers</li>
                                <li>{{ $settings['password_require_symbols'] ? '✓' : '✗' }} Symbols</li>
                            </ul>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Login Security</label>
                            <p class="mb-0">{{ $settings['login_attempts_limit'] }} attempts, {{ $settings['lockout_duration'] }} min lockout</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted small">Two-Factor Auth</label>
                            <p class="mb-0">
                                <span class="badge {{ $settings['two_factor_enabled'] ? 'bg-success' : 'bg-warning' }}">
                                    {{ $settings['two_factor_enabled'] ? 'Enabled' : 'Disabled' }}
                                </span>
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Security Recommendations</h6>
                </div>
                <div class="card-body">
                    <div class="security-tips">
                        <div class="alert alert-info">
                            <i class="fas fa-lightbulb me-2"></i>
                            <strong>Tip:</strong> Enable all password requirements for maximum security.
                        </div>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-shield text-primary me-2"></i>Use at least 8 character passwords</li>
                            <li><i class="fas fa-shield text-primary me-2"></i>Require mixed case letters</li>
                            <li><i class="fas fa-shield text-primary me-2"></i>Include numbers and symbols</li>
                            <li><i class="fas fa-shield text-primary me-2"></i>Enable two-factor authentication</li>
                            <li><i class="fas fa-shield text-primary me-2"></i>Limit login attempts to 5 or fewer</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Security Score</h6>
                </div>
                <div class="card-body text-center">
                    @php
                        $score = 0;
                        if ($settings['password_min_length'] >= 8) $score += 20;
                        if ($settings['password_require_uppercase'] && $settings['password_require_lowercase']) $score += 20;
                        if ($settings['password_require_numbers']) $score += 15;
                        if ($settings['password_require_symbols']) $score += 15;
                        if ($settings['login_attempts_limit'] <= 5) $score += 15;
                        if ($settings['two_factor_enabled']) $score += 15;
                        
                        $scoreColor = $score >= 80 ? 'success' : ($score >= 60 ? 'warning' : 'danger');
                    @endphp
                    <div class="security-score">
                        <div class="display-4 text-{{ $scoreColor }}">{{ $score }}%</div>
                        <p class="text-muted">Security Level</p>
                        <div class="progress">
                            <div class="progress-bar bg-{{ $scoreColor }}" 
                                 role="progressbar" 
                                 style="--progress-width: {{ $score }}%; width: var(--progress-width);" 
                                 aria-valuenow="{{ $score }}" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>