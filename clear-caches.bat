@echo off
echo Clearing Laravel caches...
echo.

echo [1/6] Clearing view cache...
php artisan view:clear

echo [2/6] Clearing application cache...
php artisan cache:clear

echo [3/6] Clearing configuration cache...
php artisan config:clear

echo [4/6] Clearing route cache...
php artisan route:clear

echo [5/6] Manually clearing compiled views...
if exist "storage\framework\views\*" del "storage\framework\views\*" /f /q

echo [6/6] Optimizing application...
php artisan optimize:clear

echo.
echo All caches cleared successfully!
echo You can now access your application without cache issues.
pause