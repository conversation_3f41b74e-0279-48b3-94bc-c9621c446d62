<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('equipment', function (Blueprint $table) {
            // Add daily_capa column before eqp_oee
            $table->integer('daily_capa')->nullable()->after('lotqty_alloc')->comment('Daily capacity of the equipment (eqp_oee * eqp_speed * operation_time)');
        });
        
        // Calculate and populate daily_capa for existing records
        DB::statement("
            UPDATE equipment 
            SET daily_capa = CASE 
                WHEN eqp_oee IS NOT NULL AND eqp_speed IS NOT NULL AND operation_time IS NOT NULL 
                THEN ROUND(eqp_oee * eqp_speed * operation_time)
                ELSE NULL 
            END
        ");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('equipment', function (Blueprint $table) {
            // Remove daily_capa column
            $table->dropColumn('daily_capa');
        });
    }
};
