<x-app-layout>
    <x-slot name="header">
        Product Analytics
    </x-slot>

    <!-- Product Overview Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-success bg-opacity-10 text-success me-3">
                            <i class="fas fa-boxes"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($productData['inventory_analysis']['total_products']) }}</h3>
                            <p class="text-muted mb-0">Total Products</p>
                            <small class="text-success">In catalog</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-warning bg-opacity-10 text-warning me-3">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($productData['inventory_analysis']['low_stock']) }}</h3>
                            <p class="text-muted mb-0">Low Stock Items</p>
                            <small class="text-warning">< 10 units</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-danger bg-opacity-10 text-danger me-3">
                            <i class="fas fa-times-circle"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">{{ number_format($productData['inventory_analysis']['out_of_stock']) }}</h3>
                            <p class="text-muted mb-0">Out of Stock</p>
                            <small class="text-danger">Need restock</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon bg-info bg-opacity-10 text-info me-3">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <div>
                            <h3 class="mb-0">${{ number_format($productData['inventory_analysis']['average_stock_value'], 2) }}</h3>
                            <p class="text-muted mb-0">Avg Stock Value</p>
                            <small class="text-info">Per product</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row g-4 mb-4">
        <!-- Category Performance -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Category Performance</h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px;">
                        <canvas id="categoryPerformanceChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stock Status Distribution -->
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Stock Status</h5>
                </div>
                <div class="card-body">
                    <div style="height: 350px;">
                        <canvas id="stockStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Data Tables -->
    <div class="row g-4 mb-4">
        <!-- Most Profitable Products -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Most Profitable Products</h5>
                </div>
                <div class="card-body">
                    @if($productData['product_profitability']->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Product</th>
                                        <th>Units Sold</th>
                                        <th>Revenue</th>
                                        <th>Est. Profit</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($productData['product_profitability']->take(10) as $product)
                                    <tr>
                                        <td>
                                            <div>
                                                <strong>{{ $product->name }}</strong>
                                                <small class="d-block text-muted">${{ number_format($product->price, 2) }}</small>
                                            </div>
                                        </td>
                                        <td>{{ number_format($product->units_sold ?? 0) }}</td>
                                        <td><strong>${{ number_format($product->total_revenue ?? 0, 2) }}</strong></td>
                                        <td class="text-success"><strong>${{ number_format($product->estimated_profit ?? 0, 2) }}</strong></td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-chart-line fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No profit data available</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Stock Alerts -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Stock Alerts</h5>
                </div>
                <div class="card-body">
                    <!-- Critical Stock (Out of Stock) -->
                    @if($productData['stock_alerts']['critical']->count() > 0)
                        <h6 class="text-danger mb-2">
                            <i class="fas fa-exclamation-circle me-1"></i>
                            Critical ({{ $productData['stock_alerts']['critical']->count() }})
                        </h6>
                        @foreach($productData['stock_alerts']['critical']->take(3) as $product)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ $product->name }}</span>
                                <span class="badge bg-danger">Out of Stock</span>
                            </div>
                        @endforeach
                        <hr>
                    @endif

                    <!-- Low Stock -->
                    @if($productData['stock_alerts']['low']->count() > 0)
                        <h6 class="text-warning mb-2">
                            <i class="fas fa-exclamation-triangle me-1"></i>
                            Low Stock ({{ $productData['stock_alerts']['low']->count() }})
                        </h6>
                        @foreach($productData['stock_alerts']['low']->take(3) as $product)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ $product->name }}</span>
                                <span class="badge bg-warning">{{ $product->stock }} left</span>
                            </div>
                        @endforeach
                        <hr>
                    @endif

                    <!-- Warning Stock -->
                    @if($productData['stock_alerts']['warning']->count() > 0)
                        <h6 class="text-info mb-2">
                            <i class="fas fa-info-circle me-1"></i>
                            Warning ({{ $productData['stock_alerts']['warning']->count() }})
                        </h6>
                        @foreach($productData['stock_alerts']['warning']->take(3) as $product)
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <span>{{ $product->name }}</span>
                                <span class="badge bg-info">{{ $product->stock }} left</span>
                            </div>
                        @endforeach
                    @endif

                    @if($productData['stock_alerts']['critical']->count() == 0 && 
                        $productData['stock_alerts']['low']->count() == 0 && 
                        $productData['stock_alerts']['warning']->count() == 0)
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
                            <p class="text-muted">All products are well stocked!</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Category Details -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">Category Analysis</h5>
                </div>
                <div class="card-body">
                    @if($productData['category_performance']->count() > 0)
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>Category</th>
                                        <th>Products</th>
                                        <th>Total Stock</th>
                                        <th>Avg Price</th>
                                        <th>Units Sold</th>
                                        <th>Total Revenue</th>
                                        <th>Performance</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($productData['category_performance'] as $category)
                                        @php
                                            $performance = $category->total_revenue > 1000 ? 'excellent' : 
                                                         ($category->total_revenue > 500 ? 'good' : 
                                                         ($category->total_revenue > 100 ? 'average' : 'poor'));
                                            $performanceColor = [
                                                'excellent' => 'success',
                                                'good' => 'primary', 
                                                'average' => 'warning',
                                                'poor' => 'danger'
                                            ][$performance];
                                        @endphp
                                        <tr>
                                            <td><strong>{{ $category->category ?: 'Uncategorized' }}</strong></td>
                                            <td>{{ $category->product_count }}</td>
                                            <td>{{ number_format($category->total_stock) }}</td>
                                            <td>${{ number_format($category->avg_price, 2) }}</td>
                                            <td>{{ number_format($category->total_sold) }}</td>
                                            <td><strong>${{ number_format($category->total_revenue, 2) }}</strong></td>
                                            <td>
                                                <span class="badge bg-{{ $performanceColor }}-transparent text-{{ $performanceColor }}">
                                                    {{ ucfirst($performance) }}
                                                </span>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-tags fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No category data available</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Back Navigation -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <a href="{{ route('management.analytics.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>
                        Back to Analytics Overview
                    </a>
                </div>
            </div>
        </div>
    </div>

    <style>
        .stats-icon {
            width: 50px;
            height: 50px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
        }
    </style>

    <script>
        // Prepare data for charts
        const chartData = {
            categoryData: <?php echo json_encode($productData['category_performance']->pluck('total_revenue')); ?>,
            categoryLabels: <?php echo json_encode($productData['category_performance']->pluck('category')); ?>,
            productCounts: <?php echo json_encode($productData['category_performance']->pluck('product_count')); ?>,
            stockData: [
                <?php echo ($productData['inventory_analysis']['total_products'] - $productData['inventory_analysis']['low_stock'] - $productData['inventory_analysis']['out_of_stock']); ?>,
                <?php echo $productData['inventory_analysis']['low_stock']; ?>,
                <?php echo $productData['inventory_analysis']['out_of_stock']; ?>
            ]
        };

        document.addEventListener('DOMContentLoaded', function() {
            if (typeof Chart === 'undefined') {
                console.warn('Chart.js not loaded');
                return;
            }

            // Category Performance Chart
            const categoryCtx = document.getElementById('categoryPerformanceChart');
            if (categoryCtx) {
                new Chart(categoryCtx, {
                    type: 'bar',
                    data: {
                        labels: chartData.categoryLabels,
                        datasets: [{
                            label: 'Revenue ($)',
                            data: chartData.categoryData,
                            backgroundColor: 'rgba(99, 102, 241, 0.8)',
                            borderColor: 'rgb(99, 102, 241)',
                            borderWidth: 1,
                            borderRadius: 8,
                            yAxisID: 'y'
                        }, {
                            label: 'Product Count',
                            data: chartData.productCounts,
                            type: 'line',
                            borderColor: 'rgb(34, 197, 94)',
                            backgroundColor: 'rgba(34, 197, 94, 0.1)',
                            borderWidth: 3,
                            tension: 0.4,
                            yAxisID: 'y1'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        interaction: {
                            mode: 'index',
                            intersect: false,
                        },
                        scales: {
                            y: {
                                type: 'linear',
                                display: true,
                                position: 'left',
                                beginAtZero: true,
                                ticks: {
                                    callback: function(value) {
                                        return '$' + value.toLocaleString();
                                    }
                                }
                            },
                            y1: {
                                type: 'linear',
                                display: true,
                                position: 'right',
                                beginAtZero: true,
                                grid: {
                                    drawOnChartArea: false,
                                },
                                ticks: {
                                    callback: function(value) {
                                        return value + ' products';
                                    }
                                }
                            },
                            x: {
                                grid: {
                                    display: false
                                }
                            }
                        }
                    }
                });
            }

            // Stock Status Chart
            const stockCtx = document.getElementById('stockStatusChart');
            if (stockCtx) {
                new Chart(stockCtx, {
                    type: 'doughnut',
                    data: {
                        labels: ['Well Stocked', 'Low Stock', 'Out of Stock'],
                        datasets: [{
                            data: chartData.stockData,
                            backgroundColor: [
                                '#22c55e', // Green for well stocked
                                '#f59e0b', // Orange for low stock
                                '#ef4444'  // Red for out of stock
                            ],
                            borderWidth: 2,
                            borderColor: '#ffffff'
                        }]
                    },
                    options: {
                        responsive: true,
                        maintainAspectRatio: false,
                        plugins: {
                            legend: {
                                position: 'bottom',
                                labels: {
                                    padding: 20,
                                    usePointStyle: true
                                }
                            }
                        }
                    }
                });
            }
        });
    </script>
</x-app-layout>