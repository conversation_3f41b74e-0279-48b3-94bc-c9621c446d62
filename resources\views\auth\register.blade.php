<x-guest-layout>
    <div class="text-center mb-4">
        <h4 class="fw-bold mb-2">Create your account</h4>
        <p class="text-muted">Please enter valid credentials</p>
    </div>

    <form method="POST" action="{{ route('register') }}">
        @csrf

        <!-- Employee Number -->
        <div class="mb-3">
            <label for="emp_no" class="form-label fw-medium">Employee Number</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-id-card text-muted"></i>
                </span>
                <input id="emp_no" 
                       type="text" 
                       class="form-control border-start-0 @error('emp_no') is-invalid @enderror" 
                       name="emp_no" 
                       value="{{ old('emp_no') }}" 
                       required 
                       autofocus 
                       autocomplete="username"
                       placeholder="Enter your employee number">
            </div>
            @error('emp_no')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Employee Name -->
        <div class="mb-3">
            <label for="emp_name" class="form-label fw-medium">Employee Name</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-user text-muted"></i>
                </span>
                <input id="emp_name" 
                       type="text" 
                       class="form-control border-start-0 @error('emp_name') is-invalid @enderror" 
                       name="emp_name" 
                       value="{{ old('emp_name') }}" 
                       required 
                       autocomplete="name"
                       placeholder="Enter your full name">
            </div>
            @error('emp_name')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Position -->
        <div class="mb-3">
            <label for="position" class="form-label fw-medium">Position</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-briefcase text-muted"></i>
                </span>
                <input id="position" 
                       type="text" 
                       class="form-control border-start-0 @error('position') is-invalid @enderror" 
                       name="position" 
                       value="{{ old('position') }}" 
                       placeholder="Enter your position">
            </div>
            @error('position')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Job Assigned -->
        <div class="mb-3">
            <label for="job_assigned" class="form-label fw-medium">Job Assigned</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-tasks text-muted"></i>
                </span>
                <input id="job_assigned" 
                       type="text" 
                       class="form-control border-start-0 @error('job_assigned') is-invalid @enderror" 
                       name="job_assigned" 
                       value="{{ old('job_assigned') }}" 
                       placeholder="Enter your job assignment">
            </div>
            @error('job_assigned')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Password -->
        <div class="mb-3">
            <label for="password" class="form-label fw-medium">Password</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-lock text-muted"></i>
                </span>
                <input id="password" 
                       type="password" 
                       class="form-control border-start-0 @error('password') is-invalid @enderror" 
                       name="password" 
                       required 
                       autocomplete="new-password"
                       placeholder="Create a password">
            </div>
            @error('password')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Confirm Password -->
        <div class="mb-4">
            <label for="password_confirmation" class="form-label fw-medium">{{ __('Confirm Password') }}</label>
            <div class="input-group">
                <span class="input-group-text bg-light border-end-0">
                    <i class="fas fa-lock text-muted"></i>
                </span>
                <input id="password_confirmation" 
                       type="password" 
                       class="form-control border-start-0 @error('password_confirmation') is-invalid @enderror" 
                       name="password_confirmation" 
                       required 
                       autocomplete="new-password"
                       placeholder="Confirm your password">
            </div>
            @error('password_confirmation')
                <div class="invalid-feedback d-block">
                    {{ $message }}
                </div>
            @enderror
        </div>

        <!-- Register Button -->
        <div class="d-grid mb-4">
            <button type="submit" class="btn btn-primary btn-lg py-2">
                <i class="fas fa-user-plus me-2"></i>
                Sign Up
            </button>
        </div>
    </form>

    <!-- Login Link -->
    <div class="text-center">
        <p class="mb-0 text-muted">Already have an account? 
            <a href="{{ route('login') }}" class="text-decoration-none text-primary fw-medium">
                Login Here
            </a>
        </p>
    </div>
</x-guest-layout>
