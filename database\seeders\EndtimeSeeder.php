<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Endtime;
use Carbon\Carbon;

class EndtimeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $csvFile = base_path('endtime.csv');
        
        if (!file_exists($csvFile)) {
            $this->command->error('endtime.csv file not found in project root');
            return;
        }
        
        $csvData = array_map('str_getcsv', file($csvFile));
        $header = array_shift($csvData); // Remove header row
        
        foreach ($csvData as $row) {
            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }
            
            // Map CSV columns to array keys
            $data = array_combine($header, $row);
            
            // Skip if lot_id is empty
            if (empty($data['lot_id'])) {
                continue;
            }
            
            // Parse and format the data
            $estEndtime = null;
            if (!empty($data['est_endtime'])) {
                try {
                    $estEndtime = Carbon::createFromFormat('n/j/Y G:i', $data['est_endtime']);
                } catch (Exception $e) {
                    // If parsing fails, set to a future time
                    $estEndtime = Carbon::now()->addHours(rand(1, 24));
                }
            }
            
            // Handle actual_submitted_at (if exists in CSV)
            $actualSubmittedAt = null;
            if (!empty($data['actual_submitted_at'])) {
                try {
                    $actualSubmittedAt = Carbon::createFromFormat('n/j/Y G:i', $data['actual_submitted_at']);
                } catch (Exception $e) {
                    $actualSubmittedAt = null;
                }
            }
            
            // Handle remarks and submission notes
            $remarks = null;
            $submissionNotes = null;
            
            // If we have actual submission time and estimated end time, calculate remarks
            if ($actualSubmittedAt && $estEndtime) {
                $timeDifferenceMinutes = $actualSubmittedAt->diffInMinutes($estEndtime, false);
                
                if ($timeDifferenceMinutes > 30) {
                    $remarks = 'Early';
                } elseif ($timeDifferenceMinutes < -30) {
                    $remarks = 'Delayed';
                    // For delayed lots, try to get notes if available
                    $submissionNotes = $data['submission_notes'] ?? 'Imported from CSV - reason not specified';
                } else {
                    $remarks = 'OK';
                }
            }
            
            Endtime::create([
                'lot_id' => $data['lot_id'],
                'model_15' => $data['model_15'] ?? null,
                'lot_size' => !empty($data['lot_size']) ? (int)$data['lot_size'] : null,
                'lot_qty' => !empty($data['lot_qty']) ? (int)$data['lot_qty'] : null,
                'work_type' => $data['work_type'] ?? null,
                'lot_type' => $data['lot_type'] ?? null,
                'lipas_yn' => $data['lipas_yn'] ?? 'N',
                'eqp_1' => $data['eqp_1'] ?? null,
                'eqp_2' => !empty($data['eqp_2']) ? $data['eqp_2'] : null,
                'eqp_3' => !empty($data['eqp_3']) ? $data['eqp_3'] : null,
                'eqp_4' => !empty($data['eqp_4']) ? $data['eqp_4'] : null,
                'eqp_5' => !empty($data['eqp_5']) ? $data['eqp_5'] : null,
                'eqp_6' => !empty($data['eqp_6']) ? $data['eqp_6'] : null,
                'eqp_7' => !empty($data['eqp_7']) ? $data['eqp_7'] : null,
                'eqp_8' => !empty($data['eqp_8']) ? $data['eqp_8'] : null,
                'eqp_9' => !empty($data['eqp_9']) ? $data['eqp_9'] : null,
                'eqp_10' => !empty($data['eqp_10']) ? $data['eqp_10'] : null,
                'eqp_line' => $data['eqp_line'] ?? null,
                'eqp_area' => $data['eqp_area'] ?? null,
                'status' => $data['status'] ?? 'Ongoing',
                'est_endtime' => $estEndtime,
                'actual_submitted_at' => $actualSubmittedAt,
                'modified_by' => $data['modified_by'] ?? 'CSV_IMPORT',
                'remarks' => $remarks,
                'submission_notes' => $submissionNotes,
            ]);
        }
        
        $this->command->info('Imported ' . (count($csvData) - count(array_filter($csvData, function($row) { return empty(array_filter($row)); }))) . ' records from endtime.csv');
    }
}
