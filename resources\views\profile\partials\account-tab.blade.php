<!-- Account Tab -->
<div class="tab-pane fade show active" id="account" role="tabpanel">
    <form method="post" action="{{ route('profile.update') }}">
        @csrf
        @method('patch')
        
        <div class="row g-4">
            <div class="col-md-6">
                <label for="emp_no" class="form-label fw-semibold">Employee Number <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="emp_no" name="emp_no" value="{{ old('emp_no', Auth::user()->emp_no) }}" required>
                @error('emp_no')
                    <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="emp_name" class="form-label fw-semibold">Employee Name <span class="text-danger">*</span></label>
                <input type="text" class="form-control" id="emp_name" name="emp_name" value="{{ old('emp_name', Auth::user()->emp_name) }}" required>
                @error('emp_name')
                    <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="position" class="form-label fw-semibold">Position</label>
                <input type="text" class="form-control" id="position" name="position" value="{{ old('position', Auth::user()->position ?? '') }}">
                @error('position')
                    <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="title_class" class="form-label fw-semibold">Title Class</label>
                <input type="text" class="form-control" id="title_class" name="title_class" value="{{ old('title_class', Auth::user()->title_class ?? '') }}">
                @error('title_class')
                    <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="rank" class="form-label fw-semibold">Rank</label>
                <input type="text" class="form-control" id="rank" name="rank" value="{{ old('rank', Auth::user()->rank ?? '') }}">
                @error('rank')
                    <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                @enderror
            </div>
            <div class="col-md-6">
                <label for="hr_job_name" class="form-label fw-semibold">HR Job Name</label>
                <input type="text" class="form-control" id="hr_job_name" name="hr_job_name" value="{{ old('hr_job_name', Auth::user()->hr_job_name ?? '') }}">
                @error('hr_job_name')
                    <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                @enderror
            </div>
            <div class="col-12">
                <label for="job_assigned" class="form-label fw-semibold">Job Assigned</label>
                <input type="text" class="form-control" id="job_assigned" name="job_assigned" value="{{ old('job_assigned', Auth::user()->job_assigned ?? '') }}">
                @error('job_assigned')
                    <div class="text-danger mt-1"><small>{{ $message }}</small></div>
                @enderror
            </div>
        </div>
        
        <div class="mt-4">
            <button type="submit" class="btn btn-primary me-2">
                <i class="fas fa-save me-2"></i>Save Changes
            </button>
            <button type="reset" class="btn btn-outline-secondary">
                <i class="fas fa-undo me-2"></i>Reset
            </button>
        </div>
    </form>
</div>