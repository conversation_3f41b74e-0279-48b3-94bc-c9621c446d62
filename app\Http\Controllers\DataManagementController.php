<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Product;
use App\Models\Order;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Response;
use Illuminate\Support\Str;

class DataManagementController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:ADMIN');
    }

    /**
     * Display data management dashboard.
     */
    public function index()
    {
        $stats = [
            'total_users' => User::count(),
            'total_products' => Product::count(),
            'total_orders' => Order::count(),
            'storage_used' => $this->getStorageUsage(),
            'last_backup' => $this->getLastBackupDate(),
        ];

        return view('management.data.index', compact('stats'));
    }

    /**
     * Export users data.
     */
    public function exportUsers()
    {
        $users = User::with(['orders', 'products'])->get();
        
        $csvData = "ID,Employee No,Employee Name,Role,Position,Title Class,Rank,HR Job Name,Job Assigned,Verified,Orders Count,Products Count,Created At\n";
        
        foreach ($users as $user) {
            $csvData .= sprintf(
                "%d,%s,%s,%s,%s,%s,%s,%s,%s,%s,%d,%d,%s\n",
                $user->id,
                $this->escapeCsv($user->emp_no),
                $this->escapeCsv($user->emp_name),
                $user->role,
                $this->escapeCsv($user->position ?? ''),
                $this->escapeCsv($user->title_class ?? ''),
                $this->escapeCsv($user->rank ?? ''),
                $this->escapeCsv($user->hr_job_name ?? ''),
                $this->escapeCsv($user->job_assigned ?? ''),
                $user->emp_verified_at ? 'Yes' : 'No',
                $user->orders->count(),
                $user->products->count(),
                $user->created_at->format('Y-m-d H:i:s')
            );
        }

        return Response::make($csvData, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=users_export_' . date('Y-m-d_H-i-s') . '.csv',
        ]);
    }

    /**
     * Export products data.
     */
    public function exportProducts()
    {
        $products = Product::with('creator')->get();
        
        $csvData = "ID,Name,Category,Price,Stock,Status,Created By,Created At\n";
        
        foreach ($products as $product) {
            $csvData .= sprintf(
                "%d,%s,%s,%.2f,%d,%s,%s,%s\n",
                $product->id,
                $this->escapeCsv($product->name),
                $this->escapeCsv($product->category ?? ''),
                $product->price,
                $product->stock,
                $product->status,
                $this->escapeCsv($product->creator->emp_name ?? $product->creator->emp_no),
                $product->created_at->format('Y-m-d H:i:s')
            );
        }

        return Response::make($csvData, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=products_export_' . date('Y-m-d_H-i-s') . '.csv',
        ]);
    }

    /**
     * Export orders data.
     */
    public function exportOrders()
    {
        $orders = Order::with(['user', 'orderItems.product'])->get();
        
        $csvData = "ID,Order Number,Customer,Total Amount,Total Items,Status,Order Date,Created At\n";
        
        foreach ($orders as $order) {
            $csvData .= sprintf(
                "%d,%s,%s,%.2f,%d,%s,%s,%s\n",
                $order->id,
                $this->escapeCsv($order->order_number),
                $this->escapeCsv($order->user->emp_name ?? $order->user->emp_no),
                $order->total_amount,
                $order->total_items,
                $order->status,
                $order->order_date->format('Y-m-d H:i:s'),
                $order->created_at->format('Y-m-d H:i:s')
            );
        }

        return Response::make($csvData, 200, [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename=orders_export_' . date('Y-m-d_H-i-s') . '.csv',
        ]);
    }

    /**
     * Import products from CSV.
     */
    public function importProducts(Request $request)
    {
        $request->validate([
            'csv_file' => 'required|file|mimes:csv,txt|max:10240', // 10MB max
        ]);

        $file = $request->file('csv_file');
        $csvData = file_get_contents($file->getPathname());
        $lines = explode("\n", $csvData);
        
        // Skip header row
        array_shift($lines);
        
        $imported = 0;
        $errors = [];
        
        foreach ($lines as $index => $line) {
            $line = trim($line);
            if (empty($line)) continue;
            
            $data = str_getcsv($line);
            
            if (count($data) < 5) {
                $errors[] = "Line " . ($index + 2) . ": Invalid data format";
                continue;
            }
            
            try {
                Product::create([
                    'name' => $data[0],
                    'category' => $data[1] ?? null,
                    'description' => $data[2] ?? null,
                    'price' => floatval($data[3]),
                    'stock' => intval($data[4]),
                    'status' => $data[5] ?? 'active',
                    'created_by' => auth()->id(),
                ]);
                $imported++;
            } catch (\Exception $e) {
                $errors[] = "Line " . ($index + 2) . ": " . $e->getMessage();
            }
        }
        
        $message = "Imported {$imported} products successfully.";
        if (!empty($errors)) {
            $message .= " " . count($errors) . " errors occurred.";
        }
        
        return redirect()->route('management.data.index')
                        ->with($imported > 0 ? 'success' : 'error', $message)
                        ->with('import_errors', $errors);
    }

    /**
     * Create database backup.
     */
    public function createBackup()
    {
        try {
            $timestamp = date('Y-m-d_H-i-s');
            $backupData = [
                'created_at' => now(),
                'users' => User::all(),
                'products' => Product::all(),
                'orders' => Order::with('orderItems')->get(),
            ];
            
            $backupJson = json_encode($backupData, JSON_PRETTY_PRINT);
            $filename = "backup_{$timestamp}.json";
            
            Storage::disk('local')->put("backups/{$filename}", $backupJson);
            
            return redirect()->route('management.data.index')
                           ->with('success', "Backup created successfully: {$filename}");
        } catch (\Exception $e) {
            return redirect()->route('management.data.index')
                           ->with('error', 'Failed to create backup: ' . $e->getMessage());
        }
    }

    /**
     * Download backup file.
     */
    public function downloadBackup($filename)
    {
        if (!Storage::disk('local')->exists("backups/{$filename}")) {
            abort(404);
        }
        
        return Storage::disk('local')->download("backups/{$filename}");
    }

    /**
     * List available backups.
     */
    public function listBackups()
    {
        $backups = [];
        $files = Storage::disk('local')->files('backups');
        
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'json') {
                $backups[] = [
                    'filename' => basename($file),
                    'size' => Storage::disk('local')->size($file),
                    'created_at' => Storage::disk('local')->lastModified($file),
                ];
            }
        }
        
        // Sort by creation time, newest first
        usort($backups, function($a, $b) {
            return $b['created_at'] - $a['created_at'];
        });
        
        return view('management.data.backups', compact('backups'));
    }

    /**
     * Delete backup file.
     */
    public function deleteBackup($filename)
    {
        if (Storage::disk('local')->exists("backups/{$filename}")) {
            Storage::disk('local')->delete("backups/{$filename}");
            return redirect()->route('management.data.backups')
                           ->with('success', 'Backup deleted successfully');
        }
        
        return redirect()->route('management.data.backups')
                       ->with('error', 'Backup file not found');
    }

    /**
     * Get storage usage in MB.
     */
    private function getStorageUsage()
    {
        $size = 0;
        $files = Storage::disk('local')->allFiles();
        
        foreach ($files as $file) {
            $size += Storage::disk('local')->size($file);
        }
        
        return round($size / 1024 / 1024, 2); // Convert to MB
    }

    /**
     * Get last backup date.
     */
    private function getLastBackupDate()
    {
        $files = Storage::disk('local')->files('backups');
        $lastModified = 0;
        
        foreach ($files as $file) {
            if (pathinfo($file, PATHINFO_EXTENSION) === 'json') {
                $modified = Storage::disk('local')->lastModified($file);
                if ($modified > $lastModified) {
                    $lastModified = $modified;
                }
            }
        }
        
        return $lastModified ? date('Y-m-d H:i:s', $lastModified) : 'Never';
    }

    /**
     * Escape CSV values.
     */
    private function escapeCsv($value)
    {
        if (strpos($value, ',') !== false || strpos($value, '"') !== false || strpos($value, "\n") !== false) {
            return '"' . str_replace('"', '""', $value) . '"';
        }
        return $value;
    }
}