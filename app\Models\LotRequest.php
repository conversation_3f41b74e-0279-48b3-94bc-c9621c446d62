<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class LotRequest extends Model
{
    use HasFactory;

    protected $fillable = [
        'request_number',
        'user_id',
        'notes',
        'status',
        'request_date',
        'responded_at',
        'completed_at'
    ];

    protected $casts = [
        'request_date' => 'datetime',
        'responded_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the user who created this lot request
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the lot request items for this request
     */
    public function lotRequestItems()
    {
        return $this->hasMany(LotRequestItem::class);
    }
    
    /**
     * Get the lot assignments for this request
     */
    public function lotAssignments()
    {
        return $this->hasMany(LotAssignment::class);
    }

    /**
     * Generate request number
     */
    public static function generateRequestNumber()
    {
        return 'LTR-' . strtoupper(Str::random(8));
    }

    /**
     * Get status badge class
     */
    public function getStatusBadgeClass()
    {
        return match($this->status) {
            'pending' => 'bg-warning',
            'in_process' => 'bg-info',
            'completed' => 'bg-success',
            'cancelled' => 'bg-danger',
            default => 'bg-secondary'
        };
    }

    /**
     * Get formatted status for display
     */
    public function getFormattedStatusAttribute()
    {
        return match($this->status) {
            'in_process' => 'Getting Lot',
            default => ucfirst(str_replace('_', ' ', $this->status))
        };
    }

    /**
     * Get total quantity (count of unique lot quantities)
     * Equipment with the same quantity are grouped together and count as one lot
     */
    public function getTotalQuantityAttribute()
    {
        return $this->lotRequestItems->pluck('quantity')->unique()->sum();
    }

    /**
     * Get quantity display for individual items
     * If quantity appears multiple times, show 'shared' instead of number
     */
    public function getQuantityDisplayForItem($item)
    {
        $allQuantities = $this->lotRequestItems->pluck('quantity')->toArray();
        $countOfThisQuantity = array_count_values($allQuantities)[$item->quantity] ?? 0;
        
        if ($countOfThisQuantity > 1) {
            return 'shared';
        }
        
        return $item->quantity;
    }

    /**
     * Get items grouped by quantity for display
     */
    public function getGroupedItemsAttribute()
    {
        return $this->lotRequestItems->groupBy('quantity')->map(function ($items, $quantity) {
            return [
                'quantity' => $quantity,
                'items' => $items,
                'is_shared' => $items->count() > 1,
                'item_count' => $items->count()
            ];
        });
    }

    /**
     * Get unique area stations from equipment items
     */
    public function getAreaStationsAttribute()
    {
        return $this->lotRequestItems
            ->map(function ($item) {
                return $item->equipment ? $item->equipment->eqp_area : null;
            })
            ->filter() // Remove null values
            ->unique()
            ->values()
            ->implode(', ');
    }
}
