<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Artisan;

class SettingsController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:ADMIN');
    }

    /**
     * Display settings dashboard.
     */
    public function index()
    {
        $settings = $this->getApplicationSettings();
        
        return view('management.settings.index', compact('settings'));
    }

    /**
     * Show general settings form.
     */
    public function general()
    {
        $settings = $this->getApplicationSettings();
        
        return view('management.settings.general', compact('settings'));
    }

    /**
     * Update general settings.
     */
    public function updateGeneral(Request $request)
    {
        $request->validate([
            'app_name' => 'required|string|max:255',
            'app_description' => 'nullable|string|max:500',
            'contact_email' => 'required|email',
            'timezone' => 'required|string',
            'date_format' => 'required|string',
            'currency' => 'required|string|size:3',
            'items_per_page' => 'required|integer|min:5|max:100',
        ]);

        $this->updateSettings([
            'app_name' => $request->app_name,
            'app_description' => $request->app_description,
            'contact_email' => $request->contact_email,
            'timezone' => $request->timezone,
            'date_format' => $request->date_format,
            'currency' => $request->currency,
            'items_per_page' => $request->items_per_page,
        ]);

        return redirect()->route('management.settings.general')
                        ->with('success', 'General settings updated successfully!');
    }

    /**
     * Show email settings form.
     */
    public function email()
    {
        $settings = $this->getApplicationSettings();
        
        return view('management.settings.email', compact('settings'));
    }

    /**
     * Update email settings.
     */
    public function updateEmail(Request $request)
    {
        $request->validate([
            'mail_from_address' => 'required|email',
            'mail_from_name' => 'required|string|max:255',
            'smtp_host' => 'nullable|string',
            'smtp_port' => 'nullable|integer',
            'smtp_username' => 'nullable|string',
            'smtp_password' => 'nullable|string',
            'smtp_encryption' => 'nullable|string',
        ]);

        $this->updateSettings([
            'mail_from_address' => $request->mail_from_address,
            'mail_from_name' => $request->mail_from_name,
            'smtp_host' => $request->smtp_host,
            'smtp_port' => $request->smtp_port,
            'smtp_username' => $request->smtp_username,
            'smtp_password' => $request->smtp_password,
            'smtp_encryption' => $request->smtp_encryption,
        ]);

        return redirect()->route('management.settings.email')
                        ->with('success', 'Email settings updated successfully!');
    }

    /**
     * Show system settings form.
     */
    public function system()
    {
        $settings = $this->getApplicationSettings();
        $systemInfo = $this->getSystemInfo();
        
        return view('management.settings.system', compact('settings', 'systemInfo'));
    }

    /**
     * Update system settings.
     */
    public function updateSystem(Request $request)
    {
        $request->validate([
            'maintenance_mode' => 'boolean',
            'debug_mode' => 'boolean',
            'cache_enabled' => 'boolean',
            'log_level' => 'required|in:emergency,alert,critical,error,warning,notice,info,debug',
            'session_lifetime' => 'required|integer|min:5|max:1440',
            'max_upload_size' => 'required|integer|min:1|max:100',
        ]);

        $this->updateSettings([
            'maintenance_mode' => $request->boolean('maintenance_mode'),
            'debug_mode' => $request->boolean('debug_mode'),
            'cache_enabled' => $request->boolean('cache_enabled'),
            'log_level' => $request->log_level,
            'session_lifetime' => $request->session_lifetime,
            'max_upload_size' => $request->max_upload_size,
        ]);

        return redirect()->route('management.settings.system')
                        ->with('success', 'System settings updated successfully!');
    }

    /**
     * Show security settings form.
     */
    public function security()
    {
        $settings = $this->getApplicationSettings();
        
        return view('management.settings.security', compact('settings'));
    }

    /**
     * Update security settings.
     */
    public function updateSecurity(Request $request)
    {
        $request->validate([
            'password_min_length' => 'required|integer|min:6|max:32',
            'password_require_uppercase' => 'boolean',
            'password_require_lowercase' => 'boolean',
            'password_require_numbers' => 'boolean',
            'password_require_symbols' => 'boolean',
            'login_attempts_limit' => 'required|integer|min:3|max:10',
            'lockout_duration' => 'required|integer|min:1|max:60',
            'two_factor_enabled' => 'boolean',
        ]);

        $this->updateSettings([
            'password_min_length' => $request->password_min_length,
            'password_require_uppercase' => $request->boolean('password_require_uppercase'),
            'password_require_lowercase' => $request->boolean('password_require_lowercase'),
            'password_require_numbers' => $request->boolean('password_require_numbers'),
            'password_require_symbols' => $request->boolean('password_require_symbols'),
            'login_attempts_limit' => $request->login_attempts_limit,
            'lockout_duration' => $request->lockout_duration,
            'two_factor_enabled' => $request->boolean('two_factor_enabled'),
        ]);

        return redirect()->route('management.settings.security')
                        ->with('success', 'Security settings updated successfully!');
    }

    /**
     * Clear application cache.
     */
    public function clearCache()
    {
        try {
            Artisan::call('cache:clear');
            Artisan::call('config:clear');
            Artisan::call('view:clear');
            Artisan::call('route:clear');

            return redirect()->back()
                           ->with('success', 'Application cache cleared successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Failed to clear cache: ' . $e->getMessage());
        }
    }

    /**
     * Optimize application.
     */
    public function optimize()
    {
        try {
            Artisan::call('config:cache');
            Artisan::call('route:cache');
            Artisan::call('view:cache');

            return redirect()->back()
                           ->with('success', 'Application optimized successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                           ->with('error', 'Failed to optimize application: ' . $e->getMessage());
        }
    }

    /**
     * Download application logs.
     */
    public function downloadLogs()
    {
        $logPath = storage_path('logs/laravel.log');
        
        if (!file_exists($logPath)) {
            return redirect()->back()
                           ->with('error', 'Log file not found.');
        }

        return response()->download($logPath, 'application_logs_' . date('Y-m-d') . '.log');
    }

    /**
     * Clear application logs.
     */
    public function clearLogs()
    {
        $logPath = storage_path('logs/laravel.log');
        
        if (file_exists($logPath)) {
            file_put_contents($logPath, '');
            return redirect()->back()
                           ->with('success', 'Application logs cleared successfully!');
        }

        return redirect()->back()
                       ->with('error', 'Log file not found.');
    }

    /**
     * Get application settings.
     */
    private function getApplicationSettings()
    {
        return [
            // General Settings
            'app_name' => config('app.name', 'Process Dashboard'),
            'app_description' => $this->getSetting('app_description', 'Business Process Management Dashboard'),
            'contact_email' => $this->getSetting('contact_email', '<EMAIL>'),
            'timezone' => config('app.timezone', 'UTC'),
            'date_format' => $this->getSetting('date_format', 'M d, Y'),
            'currency' => $this->getSetting('currency', 'USD'),
            'items_per_page' => $this->getSetting('items_per_page', 15),

            // Email Settings
            'mail_from_address' => config('mail.from.address', '<EMAIL>'),
            'mail_from_name' => config('mail.from.name', 'Process Dashboard'),
            'smtp_host' => config('mail.mailers.smtp.host', ''),
            'smtp_port' => config('mail.mailers.smtp.port', 587),
            'smtp_username' => config('mail.mailers.smtp.username', ''),
            'smtp_password' => config('mail.mailers.smtp.password', ''),
            'smtp_encryption' => config('mail.mailers.smtp.encryption', 'tls'),

            // System Settings
            'maintenance_mode' => $this->getSetting('maintenance_mode', false),
            'debug_mode' => config('app.debug', false),
            'cache_enabled' => $this->getSetting('cache_enabled', true),
            'log_level' => config('logging.channels.single.level', 'info'),
            'session_lifetime' => config('session.lifetime', 120),
            'max_upload_size' => $this->getSetting('max_upload_size', 2),

            // Security Settings
            'password_min_length' => $this->getSetting('password_min_length', 8),
            'password_require_uppercase' => $this->getSetting('password_require_uppercase', false),
            'password_require_lowercase' => $this->getSetting('password_require_lowercase', false),
            'password_require_numbers' => $this->getSetting('password_require_numbers', false),
            'password_require_symbols' => $this->getSetting('password_require_symbols', false),
            'login_attempts_limit' => $this->getSetting('login_attempts_limit', 5),
            'lockout_duration' => $this->getSetting('lockout_duration', 5),
            'two_factor_enabled' => $this->getSetting('two_factor_enabled', false),
        ];
    }

    /**
     * Get system information.
     */
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'disk_space' => $this->getDiskSpace(),
            'log_file_size' => $this->getLogFileSize(),
            'cache_size' => $this->getCacheSize(),
        ];
    }

    /**
     * Get a setting value.
     */
    private function getSetting($key, $default = null)
    {
        $settings = Cache::get('app_settings', []);
        return $settings[$key] ?? $default;
    }

    /**
     * Update settings.
     */
    private function updateSettings($newSettings)
    {
        $settings = Cache::get('app_settings', []);
        $settings = array_merge($settings, $newSettings);
        Cache::put('app_settings', $settings, now()->addDays(30));
        
        // Also save to file for persistence
        Storage::disk('local')->put('settings.json', json_encode($settings, JSON_PRETTY_PRINT));
    }

    /**
     * Get disk space information.
     */
    private function getDiskSpace()
    {
        $bytes = disk_free_space('/');
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get log file size.
     */
    private function getLogFileSize()
    {
        $logPath = storage_path('logs/laravel.log');
        
        if (!file_exists($logPath)) {
            return '0 B';
        }
        
        $bytes = filesize($logPath);
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get cache size.
     */
    private function getCacheSize()
    {
        try {
            $size = 0;
            $files = Storage::disk('local')->allFiles('framework/cache');
            
            foreach ($files as $file) {
                $size += Storage::disk('local')->size($file);
            }
            
            $units = ['B', 'KB', 'MB', 'GB'];
            
            for ($i = 0; $size > 1024; $i++) {
                $size /= 1024;
            }
            
            return round($size, 2) . ' ' . $units[$i];
        } catch (\Exception $e) {
            return 'Unknown';
        }
    }
}