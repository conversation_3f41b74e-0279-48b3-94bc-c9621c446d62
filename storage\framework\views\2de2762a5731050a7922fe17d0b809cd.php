<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        Update Process WIP
     <?php $__env->endSlot(); ?>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Update Process WIP</h4>
                <a href="<?php echo e(route('updatewip.index')); ?>" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Process WIP
                </a>
            </div>
        </div>
    </div>

    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-10 col-md-12 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-database me-2"></i>WIP Data Import
                        <span class="badge bg-light text-primary ms-2">Current Records: <?php echo e($currentCount); ?></span>
                        <?php if($lastUpdateTime): ?>
                            <span class="badge bg-light text-primary ms-2">Last Updated: <?php echo e($lastUpdateTime->format('M d, Y H:i')); ?></span>
                        <?php endif; ?>
                    </h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Instructions:</strong>
                        <ol class="mb-0 mt-2">
                            <li>Copy the WIP data from Excel (including headers)</li>
                            <li>Paste the data into the text area below</li>
                            <li>Click "Update WIP Data" to process</li>
                            <li><strong>Warning:</strong> This will clear all existing WIP data before importing new data</li>
                        </ol>
                    </div>

                    <form action="<?php echo e(route('updatewip.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>
                        
                        <div class="mb-4">
                            <label for="raw_data" class="form-label">
                                <strong>Excel Data <span class="text-danger">*</span></strong>
                                <small class="text-muted">(Paste copied Excel data here)</small>
                            </label>
                            <textarea class="form-control <?php $__errorArgs = ['raw_data'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?> font-monospace" 
                                      id="raw_data" 
                                      name="raw_data" 
                                      rows="15" 
                                      style="height: 300px; resize: vertical;"
                                      placeholder="Paste your Excel data here..."
                                      required><?php echo e(session('success') ? '' : old('raw_data')); ?></textarea>
                            <?php $__errorArgs = ['raw_data'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            <!-- <div class="form-text">
                                <i class="fas fa-exclamation-triangle text-warning me-1"></i>
                                Expected columns: lot_id, model_15, lot_size, lot_qty, stagnant_tat, qty_class, work_type, wip_status, lot_status, hold, auto_yn, lipas_yn, eqp_type, eqp_class, lot_location, lot_code, modified_by, create_at, updated_at
                            </div> -->
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-trash text-danger fa-2x mb-2"></i>
                                        <h6 class="text-danger">Data Replacement</h6>
                                        <p class="small text-muted mb-0">All existing WIP records will be deleted before importing new data</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <a href="<?php echo e(route('updatewip.download.template')); ?>" class="text-decoration-none">
                                    <div class="card bg-light border-success" style="transition: transform 0.2s; cursor: pointer;" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                        <div class="card-body text-center">
                                            <i class="fas fa-file-excel text-success fa-2x mb-2"></i>
                                            <h6 class="text-success">Excel Format</h6>
                                            <!-- <p class="small text-muted mb-0">Paste data directly from Excel with tab-separated columns</p> -->
                                            <div class="mt-2">
                                                <i class="fas fa-download me-1"></i>
                                                <small class="text-success">Click to download template</small>
                                            </div>
                                        </div>
                                    </div>
                                </a>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="<?php echo e(route('updatewip.index')); ?>" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>Cancel
                            </a>
                            <button type="submit" class="btn btn-danger" onclick="return confirm('This will delete all existing WIP data. Are you sure?')">
                                <i class="fas fa-sync-alt me-2"></i>Update WIP Data
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Helper Scripts -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const textarea = document.getElementById('raw_data');
            
            // Show preview of data structure
            textarea.addEventListener('input', function() {
                const lines = this.value.split('\n').filter(line => line.trim() !== '');
                const lineCount = lines.length - 1; // Subtract 1 for header
                
                let statusText = '';
                if (lines.length > 0) {
                    statusText = `${lineCount} data rows detected`;
                    if (lines.length > 1) {
                        const firstDataLine = lines[1].split('\t');
                        statusText += ` (${firstDataLine.length} columns)`;
                    }
                }
                
                // Update or create status display
                let statusDiv = document.getElementById('data-status');
                if (!statusDiv) {
                    statusDiv = document.createElement('div');
                    statusDiv.id = 'data-status';
                    statusDiv.className = 'form-text text-info mt-1';
                    textarea.parentNode.appendChild(statusDiv);
                }
                statusDiv.innerHTML = statusText ? `<i class="fas fa-info-circle me-1"></i>${statusText}` : '';
            });
        });
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\inetpub\wwwroot\process-dashboard\resources\views/updatewip/create.blade.php ENDPATH**/ ?>