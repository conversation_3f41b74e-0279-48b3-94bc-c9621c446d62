<x-app-layout>
    <x-slot name="header">
        Create Lot Request
    </x-slot>

    <style>
        /* Enhanced styling for lot request page */
        .lot-request-form {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .form-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border-radius: 20px;
            overflow: hidden;
        }
        
        .form-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 1.5rem;
            border-radius: 20px 20px 0 0;
        }
        
        .equipment-item {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
            border: 2px solid #e1e8ff;
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .equipment-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 4px;
            height: 100%;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        
        .equipment-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.15);
            border-color: #667eea;
        }
        
        .equipment-tag {
            display: inline-block;
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-size: 0.85rem;
            margin: 0.2rem;
            position: relative;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .equipment-tag:hover {
            transform: scale(1.05);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .equipment-tag .remove-tag {
            margin-left: 0.5rem;
            cursor: pointer;
            opacity: 0.8;
        }
        
        .equipment-tag .remove-tag:hover {
            opacity: 1;
        }
        
        .equipment-search-container {
            position: relative;
        }
        
        .equipment-dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            background: white;
            border: 1px solid #e1e8ff;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            margin-top: 0.25rem;
        }
        
        .equipment-dropdown-item {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 1px solid #f0f4ff;
            transition: background-color 0.2s ease;
        }
        
        .equipment-dropdown-item:hover {
            background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
        }
        
        .equipment-dropdown-item:last-child {
            border-bottom: none;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 10px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline-primary {
            border: 2px solid #667eea;
            color: #667eea;
            border-radius: 10px;
            transition: all 0.3s ease;
        }
        
        .btn-outline-primary:hover {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border-color: transparent;
            transform: translateY(-1px);
        }
        
        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.2);
        }
        
        .summary-card .summary-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .summary-card .summary-item:last-child {
            border-bottom: none;
        }
        
        .page-header {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 2rem;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
        }
        
        .fade-in {
            animation: fadeIn 0.6s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .equipment-input-container {
            background: white;
            border: 2px solid #e1e8ff;
            border-radius: 10px;
            padding: 0.75rem;
            min-height: 80px;
            cursor: text;
            transition: all 0.2s ease;
        }
        
        .equipment-input-container:focus-within {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        .equipment-search-input {
            border: none;
            outline: none;
            padding: 0.25rem;
            font-size: 0.9rem;
            background: transparent;
            min-width: 200px;
        }
        
        /* Requestor dropdown styling */
        .requestor-dropdown-item {
            cursor: pointer !important;
            pointer-events: auto !important;
            user-select: none;
            transition: background-color 0.2s ease;
        }
        
        .requestor-dropdown-item:hover {
            background-color: #f8f9fa !important;
            text-decoration: none !important;
        }
        
        .requestor-dropdown-item:active {
            background-color: #e9ecef !important;
        }
    </style>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Create Lot Request</h4>
                    <p class="text-muted mb-0">Add a new lot request to the system</p>
                </div>
                <a href="{{ route('lot-requests.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Lot Requests
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 mx-auto">
            <div class="form-card">
                <div class="form-header">
                    <h6 class="mb-0"><i class="fas fa-clipboard-list me-2"></i>Lot Request Information</h6>
                </div>
                <div class="card-body p-4">
                    <form action="{{ route('lot-requests.store') }}" method="POST" id="lotRequestForm">
                        @csrf
                        
                        <!-- Requestor Selection (Available to all users) -->
                        <div class="mb-4">
                            <label for="requestor_search" class="form-label">Requestor <small class="text-muted">(Optional)</small></label>
                            <div class="position-relative">
                                <input type="text" 
                                       class="form-control @error('user_id') is-invalid @enderror" 
                                       id="requestor_search" 
                                       placeholder="Search by Employee ID or Name... (Leave empty for self)"
                                       autocomplete="off">
                                <input type="hidden" name="user_id" id="selected_user_id" value="{{ old('user_id') }}">
                                <div id="requestor_dropdown" class="position-absolute w-100 bg-white border rounded shadow-sm" style="display: none; max-height: 200px; overflow-y: auto; z-index: 1050; top: 100%; left: 0;"></div>
                            </div>
                            @error('user_id')
                                <div class="invalid-feedback d-block">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Search and select a requestor for this lot request, or leave empty to create for yourself</div>
                            <div id="selected_requestor" class="mt-2" style="display: none;">
                                <small class="text-muted">Selected: <span id="selected_requestor_info"></span></small>
                            </div>
                        </div>

                        <!-- Equipment Selection -->
                        <div class="mb-4">
                            <label class="form-label">Request for Equipment No. <span class="text-danger">*</span></label>
                            <div id="equipmentContainer">
                                <div class="equipment-item fade-in">
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label class="form-label">Equipment Numbers</label>
                                            <div class="equipment-search-container">
                                                <div class="equipment-input-container" data-index="0">
                                                    <div class="selected-equipment-tags" id="selectedEquipmentTags0"></div>
                                                    <input type="text" 
                                                           class="equipment-search-input" 
                                                           placeholder="Type equipment number..."
                                                           data-index="0">
                                                    <input type="hidden" name="equipment_items[0][equipment_numbers]" class="equipment-numbers-input" value="">
                                                </div>
                                                <div class="equipment-dropdown" id="equipmentDropdown0" style="display: none;"></div>
                                            </div>
                                            @error('equipment_items.0.equipment_numbers')
                                                <div class="invalid-feedback d-block">{{ $message }}</div>
                                            @enderror
                                            <div class="form-text">Type equipment numbers to search and select. Multiple equipment can share the same lot quantity.</div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">EQP Codes</label>
                                            <div class="equipment-codes-display border rounded p-2 bg-light" style="min-height: 40px;">
                                                <small class="text-muted">Select equipment to see codes</small>
                                            </div>
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Quantity (lots)</label>
                                            <input type="number" 
                                                   class="form-control quantity-input @error('equipment_items.0.quantity') is-invalid @enderror" 
                                                   name="equipment_items[0][quantity]" 
                                                   min="1" 
                                                   value="{{ old('equipment_items.0.quantity', 1) }}" 
                                                   required>
                                            @error('equipment_items.0.quantity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                        <div class="col-md-1 d-flex align-items-end">
                                            <button type="button" class="btn btn-outline-danger remove-equipment" style="display: none;">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <div class="equipment-info">
                                            <small class="text-muted">Add equipment numbers to see details</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <button type="button" class="btn btn-outline-primary" id="addEquipment">
                                <i class="fas fa-plus me-2"></i>Add Another Equipment Group
                            </button>
                            
                            @error('equipment_items')
                                <div class="text-danger mt-2">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mb-4">
                            <label for="notes" class="form-label">Notes</label>
                            <textarea class="form-control @error('notes') is-invalid @enderror" 
                                      id="notes" 
                                      name="notes" 
                                      rows="3" 
                                      placeholder="Any special instructions or notes for this lot request...">{{ old('notes') }}</textarea>
                            @error('notes')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Lot Request Summary -->
                        <div class="summary-card mb-4">
                            <h6 class="mb-3"><i class="fas fa-chart-bar me-2"></i>Lot Request Summary</h6>
                            <div class="summary-item d-flex justify-content-between">
                                <span><i class="fas fa-cogs me-2"></i>Total Equipment Groups:</span>
                                <span id="totalEquipmentGroups" class="fw-bold">0</span>
                            </div>
                            <div class="summary-item d-flex justify-content-between">
                                <span><i class="fas fa-microchip me-2"></i>Total Equipment Count:</span>
                                <span id="totalEquipmentCount" class="fw-bold">0</span>
                            </div>
                            <div class="summary-item d-flex justify-content-between">
                                <span><i class="fas fa-layer-group me-2"></i>Total Lots:</span>
                                <span id="totalLots" class="fw-bold fs-5">0</span>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('lot-requests.index') }}" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Create Lot Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        let equipmentIndex = 1;
        let debounceTimer = null;
        let equipmentData = @json($equipment); // Pass equipment data to JavaScript
        
        // Store selected equipment for each group
        const selectedEquipmentByGroup = {};

        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, initializing...');
            console.log('Equipment data available:', equipmentData ? equipmentData.length : 0);
            
            updateLotRequestSummary();
            
            // Add equipment button
            document.getElementById('addEquipment').addEventListener('click', function() {
                addEquipmentGroup();
            });

            // Initialize equipment search for the first group with a small delay
            setTimeout(() => {
                setupEquipmentSearch(0);
            }, 100);
            
            // Requestor search functionality
            setupRequestorSearch();
            
            // Initialize remove buttons
            updateRemoveButtons();
            
            // Setup event delegation for remove buttons
            setupRemoveButtonHandlers();
            
            // Add test functionality
            setTimeout(() => {
                testEquipmentFunctionality();
            }, 500);
        });
        
        function testEquipmentFunctionality() {
            console.log('Testing equipment functionality...');
            const searchInput = document.querySelector('.equipment-search-input[data-index="0"]');
            if (searchInput) {
                console.log('Search input found:', searchInput);
                searchInput.focus();
                
                // Test with a known equipment number
                if (equipmentData && equipmentData.length > 0) {
                    console.log('Sample equipment:', equipmentData[0]);
                }
            } else {
                console.error('Search input not found!');
            }
        }

        function setupRequestorSearch() {
            console.log('=== REQUESTOR SEARCH SETUP DEBUG ===');
            
            // Wait a bit to ensure DOM is fully loaded
            setTimeout(() => {
                const requestorSearch = document.getElementById('requestor_search');
                const requestorDropdown = document.getElementById('requestor_dropdown');
                
                console.log('requestorSearch element:', requestorSearch);
                console.log('requestorDropdown element:', requestorDropdown);
                
                if (!requestorSearch) {
                    console.error('❌ Requestor search input element not found!');
                    console.log('Available elements with ID:', document.querySelectorAll('[id]'));
                    return;
                }
                
                if (!requestorDropdown) {
                    console.error('❌ Requestor dropdown element not found!');
                    return;
                }
                
                console.log('✅ Setting up requestor search event listener...');
                
                // Clear any existing event listeners and add new ones
                requestorSearch.removeEventListener('input', handleRequestorInput);
                requestorSearch.addEventListener('input', handleRequestorInput);
                
                function handleRequestorInput(e) {
                    const query = this.value.trim();
                    console.log('🔍 FIXED: Requestor search input event fired:', query);
                    console.log('Query length:', query.length);
                    
                    // Clear previous timer
                    if (window.requestorDebounceTimer) {
                        clearTimeout(window.requestorDebounceTimer);
                    }
                    
                    if (query.length < 2) {
                        console.log('Query too short, hiding dropdown');
                        requestorDropdown.style.display = 'none';
                        return;
                    }
                    
                    console.log('⏰ Setting debounce timer...');
                    window.requestorDebounceTimer = setTimeout(() => {
                        console.log('⚡ Debounce timer triggered, calling searchEmployees');
                        searchEmployees(query);
                    }, 300);
                }
                
                // Test the input field immediately
                console.log('🧪 Testing input field focus...');
                requestorSearch.addEventListener('focus', function() {
                    console.log('✅ Requestor search input focused!');
                });
                
                // Hide dropdown when clicking outside
                document.addEventListener('click', function(e) {
                    if (!requestorSearch.contains(e.target) && !requestorDropdown.contains(e.target)) {
                        requestorDropdown.style.display = 'none';
                    }
                });
                
                console.log('✅ Requestor search setup completed!');
                
            }, 500); // Wait 500ms for DOM to be fully ready
        }
        
        function searchEmployees(query) {
            console.log('=== SEARCH EMPLOYEES DEBUG ===');
            console.log('🔍 Searching for employees with query:', query);
            
            const apiUrl = `{{ route('api.search.employees') }}?q=${encodeURIComponent(query)}`;
            console.log('📡 API URL:', apiUrl);
            
            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                             document.querySelector('input[name="_token"]')?.value;
            
            const headers = {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            };
            
            if (csrfToken) {
                headers['X-CSRF-TOKEN'] = csrfToken;
            }
            
            fetch(apiUrl, {
                method: 'GET',
                headers: headers,
                credentials: 'same-origin'
            })
                .then(response => {
                    console.log('📥 API response received');
                    console.log('Status:', response.status);
                    console.log('StatusText:', response.statusText);
                    console.log('Headers:', response.headers);
                    
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    
                    return response.json();
                })
                .then(users => {
                    console.log('✅ API returned users:');
                    console.log('Users array:', users);
                    console.log('Users count:', users.length);
                    
                    const dropdown = document.getElementById('requestor_dropdown');
                    console.log('Dropdown element:', dropdown);
                    
                    if (!dropdown) {
                        console.error('❌ Requestor dropdown element not found!');
                        return;
                    }
                    
                    if (users.length === 0) {
                        console.log('⚠️ No users found, showing no results message');
                        dropdown.innerHTML = '<div class="px-3 py-2 text-muted">No users found</div>';
                    } else {
                        console.log('👥 Building dropdown with', users.length, 'users');
                        const dropdownHtml = users.map(user => {
                            console.log('Processing user:', user);
            return `<a href="#" class="d-block px-3 py-2 text-decoration-none text-dark border-bottom requestor-dropdown-item" 
                                       data-user-id="${user.id}" 
                                       data-user-info="${user.emp_name} (${user.emp_no})"
                                       style="cursor: pointer; pointer-events: auto; user-select: none; -webkit-user-select: none;"
                                       onclick="console.log('ONCLICK: User clicked:', this.dataset.userInfo); return false;"
                                       onmouseover="this.style.backgroundColor='#f8f9fa'" 
                                       onmouseout="this.style.backgroundColor='white'">
                                <strong>${user.emp_name}</strong>
                                <span class="badge bg-secondary ms-2">${user.role}</span><br>
                                <small class="text-muted">ID: ${user.emp_no}</small>
                            </a>`;
                        }).join('');
                        
                        dropdown.innerHTML = dropdownHtml;
                        console.log('✅ Dropdown HTML updated');
                        
                        // Add click listeners with improved selection
                        const dropdownItems = dropdown.querySelectorAll('a[data-user-id]');
                        console.log('🖱️ Adding click listeners to', dropdownItems.length, 'items');
                        
                        dropdownItems.forEach((item, index) => {
                            console.log('Setting up click for item', index, ':', item);
                            
                            // Add multiple event types to ensure it works
                            item.addEventListener('click', handleItemClick);
                            item.addEventListener('mousedown', handleItemClick);
                            
                            function handleItemClick(e) {
                                e.preventDefault();
                                e.stopPropagation();
                                
                                console.log('🎯 Click event triggered on item:', e.target);
                                
                                const userId = item.dataset.userId;
                                const userInfo = item.dataset.userInfo;
                                
                                console.log('🎯 User selected:', userId, userInfo);
                                
                                // Update form fields
                                const selectedUserIdField = document.getElementById('selected_user_id');
                                const requestorSearchField = document.getElementById('requestor_search');
                                const selectedRequestorInfo = document.getElementById('selected_requestor_info');
                                const selectedRequestorDiv = document.getElementById('selected_requestor');
                                
                                if (selectedUserIdField) selectedUserIdField.value = userId;
                                if (requestorSearchField) requestorSearchField.value = userInfo;
                                if (selectedRequestorInfo) selectedRequestorInfo.textContent = userInfo;
                                if (selectedRequestorDiv) selectedRequestorDiv.style.display = 'block';
                                
                                // Hide dropdown
                                dropdown.style.display = 'none';
                                
                                console.log('✅ User selection completed successfully');
                                
                                return false;
                            }
                        });
                    }
                    
                    dropdown.style.display = 'block';
                    console.log('👁️ Dropdown made visible');
                })
                .catch(error => {
                    console.error('❌ Error searching employees:');
                    console.error('Error details:', error);
                    console.error('Error message:', error.message);
                    console.error('Error stack:', error.stack);
                    
                    // Show error in dropdown
                    const dropdown = document.getElementById('requestor_dropdown');
                    if (dropdown) {
                        dropdown.innerHTML = '<div class="px-3 py-2 text-danger">Error loading users. Check console.</div>';
                        dropdown.style.display = 'block';
                    }
                });
        }

        function setupEquipmentSearch(index) {
            const searchInput = document.querySelector(`.equipment-search-input[data-index="${index}"]`);
            const dropdown = document.getElementById(`equipmentDropdown${index}`);
            const container = document.querySelector(`.equipment-input-container[data-index="${index}"]`);
            
            console.log('Setting up equipment search for index:', index);
            console.log('Search input:', searchInput);
            console.log('Dropdown:', dropdown);
            console.log('Container:', container);
            console.log('Equipment data count:', equipmentData ? equipmentData.length : 0);
            
            if (!searchInput || !dropdown || !container) {
                console.error('Missing elements for equipment search setup');
                return;
            }
            
            // Initialize selected equipment for this group
            selectedEquipmentByGroup[index] = [];
            
            searchInput.addEventListener('input', function() {
                const query = this.value.trim();
                console.log('Equipment search input:', query);
                
                if (debounceTimer) {
                    clearTimeout(debounceTimer);
                }
                
                if (query.length < 1) {
                    dropdown.style.display = 'none';
                    return;
                }
                
                debounceTimer = setTimeout(() => {
                    searchEquipment(query, index);
                }, 200);
            });
            
            // Handle clicking on container to focus input
            container.addEventListener('click', function(e) {
                // Only focus if clicking on the container itself, not on tags
                if (e.target === container || e.target.classList.contains('selected-equipment-tags')) {
                    searchInput.focus();
                }
            });
            
            // Handle enter key
            searchInput.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    const query = this.value.trim();
                    if (query) {
                        // Try to find exact match
                        const exactMatch = equipmentData.find(eq => eq.eqp_no.toLowerCase() === query.toLowerCase());
                        if (exactMatch) {
                            addEquipmentTag(exactMatch, index);
                            this.value = '';
                            dropdown.style.display = 'none';
                        } else {
                            console.log('No exact match found for:', query);
                        }
                    }
                }
            });
            
            // Hide dropdown when clicking outside
            document.addEventListener('click', function(e) {
                if (!container.contains(e.target) && !dropdown.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });
        }
        
        function searchEquipment(query, index) {
            const dropdown = document.getElementById(`equipmentDropdown${index}`);
            
            console.log('=== Equipment Search Debug ===');
            console.log('Query:', query);
            console.log('Index:', index);
            console.log('Dropdown element:', dropdown);
            console.log('Equipment data type:', typeof equipmentData);
            console.log('Equipment data length:', equipmentData ? equipmentData.length : 'null');
            console.log('First 3 equipment items:', equipmentData ? equipmentData.slice(0, 3) : 'none');
            
            if (!dropdown) {
                console.error('Dropdown element not found!');
                return;
            }
            
            if (!equipmentData || equipmentData.length === 0) {
                console.warn('No equipment data available');
                dropdown.innerHTML = '<div class="equipment-dropdown-item text-muted">No equipment data available</div>';
                dropdown.style.display = 'block';
                return;
            }
            
            try {
                // Filter equipment based on query
                const filteredEquipment = equipmentData.filter(eq => {
                    if (!eq || typeof eq !== 'object') {
                        console.warn('Invalid equipment object:', eq);
                        return false;
                    }
                    
                    const eqpNo = eq.eqp_no ? String(eq.eqp_no).toLowerCase() : '';
                    const eqpCode = eq.eqp_code ? String(eq.eqp_code).toLowerCase() : '';
                    const queryLower = query.toLowerCase();
                    
                    const matches = eqpNo.includes(queryLower) || eqpCode.includes(queryLower);
                    if (matches) {
                        console.log('Match found:', eq.eqp_no, 'for query:', query);
                    }
                    return matches;
                }).slice(0, 10); // Limit to 10 results
                
                console.log('Filtered equipment count:', filteredEquipment.length);
                
                if (filteredEquipment.length === 0) {
                    dropdown.innerHTML = `<div class="equipment-dropdown-item text-muted">No equipment found for "${query}"</div>`;
                } else {
                    const dropdownHTML = filteredEquipment.map(eq => {
                        // Simple data encoding to avoid JSON issues
                        const eqpNo = eq.eqp_no || '';
                        const eqpCode = eq.eqp_code || '';
                        const eqpLine = eq.eqp_line || 'N/A';
                        const eqpArea = eq.eqp_area || 'N/A';
                        
                        return `<div class="equipment-dropdown-item" 
                                     data-eqp-no="${eqpNo}" 
                                     data-eqp-code="${eqpCode}"
                                     data-eqp-line="${eqpLine}"
                                     data-eqp-area="${eqpArea}">
                            <strong>${eqpNo}</strong>
                            ${eqpCode ? `<span class="badge bg-secondary ms-2">${eqpCode}</span>` : ''}<br>
                            <small class="text-muted">Line: ${eqpLine} | Area: ${eqpArea}</small>
                        </div>`;
                    }).join('');
                    
                    dropdown.innerHTML = dropdownHTML;
                    
                    // Add click listeners with simpler data handling
                    dropdown.querySelectorAll('.equipment-dropdown-item[data-eqp-no]').forEach(item => {
                        item.addEventListener('click', function() {
                            console.log('Equipment item clicked:', this.dataset.eqpNo);
                            
                            const equipment = {
                                eqp_no: this.dataset.eqpNo,
                                eqp_code: this.dataset.eqpCode,
                                eqp_line: this.dataset.eqpLine,
                                eqp_area: this.dataset.eqpArea
                            };
                            
                            addEquipmentTag(equipment, index);
                            
                            // Clear search input
                            const searchInput = document.querySelector(`.equipment-search-input[data-index="${index}"]`);
                            if (searchInput) {
                                searchInput.value = '';
                            }
                            dropdown.style.display = 'none';
                        });
                    });
                }
                
                dropdown.style.display = 'block';
                console.log('Dropdown displayed');
                
            } catch (error) {
                console.error('Error in searchEquipment:', error);
                dropdown.innerHTML = '<div class="equipment-dropdown-item text-danger">Error searching equipment</div>';
                dropdown.style.display = 'block';
            }
        }
        
        function addEquipmentTag(equipment, groupIndex) {
            // Check if equipment is already selected in this group
            if (selectedEquipmentByGroup[groupIndex].some(eq => eq.eqp_no === equipment.eqp_no)) {
                return; // Already selected
            }
            
            selectedEquipmentByGroup[groupIndex].push(equipment);
            
            const tagsContainer = document.getElementById(`selectedEquipmentTags${groupIndex}`);
            const tag = document.createElement('span');
            tag.className = 'equipment-tag';
            tag.innerHTML = `${equipment.eqp_no} <span class="remove-tag" data-eqp-no="${equipment.eqp_no}">&times;</span>`;
            
            // Add remove functionality
            tag.querySelector('.remove-tag').addEventListener('click', function() {
                removeEquipmentTag(equipment.eqp_no, groupIndex);
            });
            
            tagsContainer.appendChild(tag);
            
            // Update hidden input
            updateEquipmentNumbersInput(groupIndex);
            
            // Update equipment codes display
            updateEquipmentCodesDisplay(groupIndex);
            
            // Update summary
            updateLotRequestSummary();
        }
        
        function removeEquipmentTag(equipmentNo, groupIndex) {
            // Remove from selected equipment array
            selectedEquipmentByGroup[groupIndex] = selectedEquipmentByGroup[groupIndex].filter(eq => eq.eqp_no !== equipmentNo);
            
            // Remove tag from DOM
            const tagsContainer = document.getElementById(`selectedEquipmentTags${groupIndex}`);
            const tags = tagsContainer.querySelectorAll('.equipment-tag');
            tags.forEach(tag => {
                if (tag.textContent.includes(equipmentNo)) {
                    tag.remove();
                }
            });
            
            // Update hidden input
            updateEquipmentNumbersInput(groupIndex);
            
            // Update equipment codes display
            updateEquipmentCodesDisplay(groupIndex);
            
            // Update summary
            updateLotRequestSummary();
        }
        
        function updateEquipmentNumbersInput(groupIndex) {
            const input = document.querySelector(`input[name="equipment_items[${groupIndex}][equipment_numbers]"]`);
            const equipmentNumbers = selectedEquipmentByGroup[groupIndex].map(eq => eq.eqp_no);
            input.value = equipmentNumbers.join(',');
        }
        
        function updateEquipmentCodesDisplay(groupIndex) {
            const equipmentItem = document.querySelector(`[data-index="${groupIndex}"]`).closest('.equipment-item');
            const codesDisplay = equipmentItem.querySelector('.equipment-codes-display');
            const equipmentInfo = equipmentItem.querySelector('.equipment-info');
            
            if (selectedEquipmentByGroup[groupIndex].length === 0) {
                codesDisplay.innerHTML = '<small class="text-muted">Select equipment to see codes</small>';
                equipmentInfo.innerHTML = '<small class="text-muted">Add equipment numbers to see details</small>';
                return;
            }
            
            // Display equipment codes
            const codes = selectedEquipmentByGroup[groupIndex].map(eq => 
                `<span class="badge bg-info me-1">${eq.eqp_code || 'N/A'}</span>`
            ).join('');
            codesDisplay.innerHTML = codes;
            
            // Display equipment info
            const infoItems = selectedEquipmentByGroup[groupIndex].map(eq => 
                `<strong>${eq.eqp_no}</strong> (${eq.eqp_line || 'N/A'}/${eq.eqp_area || 'N/A'})`
            );
            equipmentInfo.innerHTML = `<small class="text-primary">${infoItems.join(' | ')}</small>`;
        }

        function addEquipmentGroup() {
            const container = document.getElementById('equipmentContainer');
            const equipmentItem = document.createElement('div');
            equipmentItem.className = 'equipment-item fade-in';
            
            equipmentItem.innerHTML = `
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label">Equipment Numbers</label>
                        <div class="equipment-search-container">
                            <div class="equipment-input-container" data-index="${equipmentIndex}">
                                <div class="selected-equipment-tags" id="selectedEquipmentTags${equipmentIndex}"></div>
                                <input type="text" 
                                       class="equipment-search-input" 
                                       placeholder="Type equipment number..."
                                       data-index="${equipmentIndex}">
                                <input type="hidden" name="equipment_items[${equipmentIndex}][equipment_numbers]" class="equipment-numbers-input" value="">
                            </div>
                            <div class="equipment-dropdown" id="equipmentDropdown${equipmentIndex}" style="display: none;"></div>
                        </div>
                        <div class="form-text">Type equipment numbers to search and select. Multiple equipment can share the same lot quantity.</div>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">EQP Codes</label>
                        <div class="equipment-codes-display border rounded p-2 bg-light" style="min-height: 40px;">
                            <small class="text-muted">Select equipment to see codes</small>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Quantity (lots)</label>
                        <input type="number" class="form-control quantity-input" 
                               name="equipment_items[${equipmentIndex}][quantity]" min="1" value="1" required />
                    </div>
                    <div class="col-md-1 d-flex align-items-end">
                        <button type="button" class="btn btn-outline-danger remove-equipment">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
                <div class="mt-3">
                    <div class="equipment-info">
                        <small class="text-muted">Add equipment numbers to see details</small>
                    </div>
                </div>
            `;
            
            container.appendChild(equipmentItem);
            
            // Setup equipment search for the new group
            setupEquipmentSearch(equipmentIndex);
            
            // Add event listeners for quantity input
            const quantityInput = equipmentItem.querySelector('.quantity-input');
            quantityInput.addEventListener('input', updateLotRequestSummary);
            
            equipmentIndex++;
            updateRemoveButtons();
        }
        
        function handleRemoveEquipmentGroup(groupIndex) {
            const equipmentItem = document.querySelector(`[data-index="${groupIndex}"]`).closest('.equipment-item');
            equipmentItem.remove();
            
            // Clean up selected equipment data
            delete selectedEquipmentByGroup[groupIndex];
            
            updateLotRequestSummary();
            updateRemoveButtons();
        }

        function updateLotRequestSummary() {
            let totalGroups = 0;
            let totalEquipmentCount = 0;
            let totalLots = 0;
            
            document.querySelectorAll('.equipment-item').forEach((item, index) => {
                const container = item.querySelector('.equipment-input-container');
                const quantityInput = item.querySelector('.quantity-input');
                
                if (container && quantityInput && quantityInput.value) {
                    const groupIndex = container.dataset.index;
                    const selectedEquipment = selectedEquipmentByGroup[groupIndex] || [];
                    
                    if (selectedEquipment.length > 0) {
                        totalGroups++;
                        totalEquipmentCount += selectedEquipment.length;
                        totalLots += parseInt(quantityInput.value) || 0;
                    }
                }
            });
            
            document.getElementById('totalEquipmentGroups').textContent = totalGroups;
            document.getElementById('totalEquipmentCount').textContent = totalEquipmentCount;
            document.getElementById('totalLots').textContent = totalLots;
        }

        function setupRemoveButtonHandlers() {
            // Use event delegation to handle remove buttons for both existing and new equipment groups
            const container = document.getElementById('equipmentContainer');
            
            container.addEventListener('click', function(e) {
                if (e.target.closest('.remove-equipment')) {
                    e.preventDefault();
                    const button = e.target.closest('.remove-equipment');
                    const equipmentItem = button.closest('.equipment-item');
                    const inputContainer = equipmentItem.querySelector('.equipment-input-container');
                    const groupIndex = parseInt(inputContainer.dataset.index);
                    
                    console.log('Remove button clicked for group index:', groupIndex);
                    
                    // Remove the equipment group
                    equipmentItem.remove();
                    
                    // Clean up selected equipment data
                    delete selectedEquipmentByGroup[groupIndex];
                    
                    updateLotRequestSummary();
                    updateRemoveButtons();
                }
            });
        }
        
        function updateRemoveButtons() {
            const equipmentItems = document.querySelectorAll('.equipment-item');
            const removeButtons = document.querySelectorAll('.remove-equipment');
            
            removeButtons.forEach((button, index) => {
                button.style.display = equipmentItems.length > 1 ? 'block' : 'none';
            });
        }
        
        // Setup quantity change listeners
        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('quantity-input')) {
                updateLotRequestSummary();
            }
        });
    </script>
</x-app-layout>
