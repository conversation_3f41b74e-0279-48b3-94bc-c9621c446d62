<?php

namespace App\Http\Controllers;

use App\Models\LotRequest;
use App\Models\Equipment;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class LotRequestController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        // All authenticated users can access lot request functions
        // Authorization is handled at the method level for specific actions
    }

    /**
     * Get the authenticated user as a User model instance
     * 
     * @return User
     */
    private function getAuthenticatedUser(): User
    {
        return Auth::user();
    }

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $user = $this->getAuthenticatedUser();
        $query = LotRequest::with(['user', 'lotRequestItems.equipment']);
        
        // Role-based lot request visibility:
        // - USER: Can see their own lot requests 
        // - MANAGER: Can see all lot requests
        // - ADMIN: Can see all lot requests
        if ($user->isUser()) {
            // Users can see their own requests
            $query->where('user_id', $user->id);
        } elseif ($user->isManager() || $user->isAdmin()) {
            // Managers and Admins can see all requests
            // No additional filtering needed
        }
        
        $lotRequests = $query->latest()->paginate(10);
        return view('lot-requests.index', compact('lotRequests'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $equipment = Equipment::all();
        return view('lot-requests.create', compact('equipment'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Validation rules - all users can now select customers
        $rules = [
            'equipment_items' => 'required|array|min:1',
            'equipment_items.*.equipment_numbers' => 'required|string',
            'equipment_items.*.quantity' => 'required|integer|min:1',
            'notes' => 'nullable|string|max:1000',
            'user_id' => 'nullable|exists:users,id', // Allow all users to select customers, but it's optional
        ];
        
        $request->validate($rules);
        
        // Additional validation for equipment numbers
        $validationErrors = [];
        foreach ($request->equipment_items as $index => $item) {
            $equipmentNumbers = array_filter(array_map('trim', explode(',', $item['equipment_numbers'])));
            
            if (empty($equipmentNumbers)) {
                $validationErrors["equipment_items.{$index}.equipment_numbers"] = 'At least one equipment number is required.';
                continue;
            }
            
            foreach ($equipmentNumbers as $equipmentNumber) {
                if (!Equipment::where('eqp_no', $equipmentNumber)->exists()) {
                    $validationErrors["equipment_items.{$index}.equipment_numbers"] = "Equipment number '{$equipmentNumber}' does not exist.";
                    break;
                }
            }
        }
        
        if (!empty($validationErrors)) {
            return back()->withErrors($validationErrors)->withInput();
        }

        // Determine the user_id for the lot request
        // If a customer is selected, use that; otherwise, use the current user
        if ($request->filled('user_id')) {
            $userId = $request->user_id;
        } else {
            $userId = Auth::user()->id;
        }

        DB::beginTransaction();
        
        try {
            // Create lot request
            $lotRequest = LotRequest::create([
                'request_number' => LotRequest::generateRequestNumber(),
                'user_id' => $userId,
                'notes' => $request->notes,
                'request_date' => now(),
            ]);

            // Create lot request items
            foreach ($request->equipment_items as $item) {
                // Parse comma-separated equipment numbers
                $equipmentNumbers = array_filter(array_map('trim', explode(',', $item['equipment_numbers'])));
                
                if (empty($equipmentNumbers)) {
                    continue; // Skip if no equipment numbers
                }
                
                // Create separate lot request items for each equipment number in the group
                foreach ($equipmentNumbers as $equipmentNumber) {
                    $equipment = Equipment::where('eqp_no', $equipmentNumber)->first();
                    
                    if ($equipment) {
                        $lotRequest->lotRequestItems()->create([
                            'equipment_number' => $equipmentNumber,
                            'equipment_code' => $equipment->eqp_code ?? '',
                            'quantity' => $item['quantity'], // Same quantity for all equipment in the group
                        ]);
                    }
                }
            }
            
            DB::commit();
            
            return redirect()->route('lot-requests.show', $lotRequest)
                ->with('success', 'Lot request created successfully!');
                
        } catch (\Exception $e) {
            DB::rollBack();
            
            return back()->withErrors([
                'error' => 'Failed to create lot request. Please try again.'
            ])->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(LotRequest $lotRequest)
    {
        // Allow all authenticated users to view lot requests
        // (Authorization can be added later if needed based on business requirements)
        
        $lotRequest->load(['lotRequestItems', 'user', 'lotAssignments.assignedBy']);
        
        // Get WIP details for assigned lots
        $assignedLotIds = $lotRequest->lotAssignments->pluck('lot_id')->toArray();
        $wipDetails = [];
        
        if (!empty($assignedLotIds)) {
            $wipDetails = \App\Models\UpdateWip::whereIn('lot_id', $assignedLotIds)
                ->get()
                ->keyBy('lot_id');
        }
        
        return view('lot-requests.show', compact('lotRequest', 'wipDetails'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(LotRequest $lotRequest)
    {
        // Allow all authenticated users to edit lot requests
        // (Authorization can be added later if needed based on business requirements)
        
        $equipment = Equipment::all();
        $lotRequest->load(['lotRequestItems']);
        
        // Get available WIP data that matches the equipment codes
        $availableWip = $this->getAvailableWipForRequest($lotRequest);
        
        return view('lot-requests.edit', compact('lotRequest', 'equipment', 'availableWip'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, LotRequest $lotRequest)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            if ($request->expectsJson()) {
                return response()->json(['error' => 'Authentication required'], 401);
            }
            return redirect()->route('login')
                ->with('error', 'You must be logged in to update lot requests.');
        }

        // Allow all authenticated users to update lot requests
        // (Authorization can be added later if needed based on business requirements)
        
        // Role-based validation for status updates
        if (!$this->getAuthenticatedUser()->canManageOrders()) {
            // Regular users have limited status update options
            $request->validate([
                'status' => 'required|in:pending,cancelled',
                'notes' => 'nullable|string|max:1000',
            ]);
        } else {
            // Admins and order managers can set any status
            $request->validate([
                'status' => 'required|in:pending,in_process,completed,cancelled',
                'notes' => 'nullable|string|max:1000',
            ]);
        }

        try {
            $updateData = [
                'status' => $request->status,
                'notes' => $request->notes,
            ];
            
            // Set timestamps based on status changes
            $oldStatus = $lotRequest->status;
            $newStatus = $request->status;
            
            // If moving from pending to in_process, set responded_at
            if ($oldStatus === 'pending' && $newStatus === 'in_process' && !$lotRequest->responded_at) {
                $updateData['responded_at'] = now();
            }
            
            // If moving to completed, set completed_at
            if ($newStatus === 'completed' && !$lotRequest->completed_at) {
                $updateData['completed_at'] = now();
                // Also set responded_at if not already set
                if (!$lotRequest->responded_at) {
                    $updateData['responded_at'] = now();
                }
            }
            
            // If moving back from completed or in_process to pending, clear timestamps
            if ($newStatus === 'pending') {
                $updateData['responded_at'] = null;
                $updateData['completed_at'] = null;
            }
            
            // If moving from completed back to in_process, clear completed_at
            if ($oldStatus === 'completed' && $newStatus === 'in_process') {
                $updateData['completed_at'] = null;
            }
            
            $lotRequest->update($updateData);

            return redirect()->route('lot-requests.show', $lotRequest)
                ->with('success', 'Lot request updated successfully!');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to update lot request. Please try again.')
                ->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(LotRequest $lotRequest)
    {
        $lotRequest->delete();
        
        return redirect()->route('lot-requests.index')
            ->with('success', 'Lot request deleted successfully!');
    }

    /**
     * Search employees by emp_no or emp_name
     */
    public function searchEmployees(Request $request)
    {
        $query = $request->get('q', '');
        
        if (strlen($query) < 2) {
            return response()->json([]);
        }
        
        // Search all users regardless of role - allow all users to be customers
        $users = User::where(function($q) use ($query) {
                $q->where('emp_no', 'LIKE', "%{$query}%")
                  ->orWhere('emp_name', 'LIKE', "%{$query}%");
            })
            ->limit(10)
            ->get(['id', 'emp_no', 'emp_name', 'role']);
            
        return response()->json($users);
    }

    /**
     * Get equipment code by equipment number
     */
    public function getEquipmentCode(Request $request)
    {
        $equipmentNumber = $request->get('equipment_number');
        
        if (!$equipmentNumber) {
            return response()->json(['error' => 'Equipment number is required'], 400);
        }
        
        $equipment = Equipment::where('eqp_no', $equipmentNumber)->first();
        
        if (!$equipment) {
            return response()->json(['error' => 'Equipment not found'], 404);
        }
        
        return response()->json([
            'equipment_code' => $equipment->eqp_code ?? '',
            'equipment_info' => [
                'line' => $equipment->eqp_line,
                'area' => $equipment->eqp_area,
                'type' => $equipment->eqp_type,
                'class' => $equipment->eqp_class
            ]
        ]);
    }
    
    /**
     * Get available WIP data that matches equipment codes in the lot request
     */
    private function getAvailableWipForRequest($lotRequest)
    {
        // Get unique equipment codes from the lot request
        $equipmentCodes = $lotRequest->lotRequestItems
            ->pluck('equipment_code')
            ->filter()
            ->unique()
            ->toArray();
        
        if (empty($equipmentCodes)) {
            return collect();
        }
        
        // Get lot IDs that are already assigned to exclude them
        $assignedLotIds = \App\Models\LotAssignment::pluck('lot_id')->toArray();
        
        // Get WIP data that matches any of the equipment codes and is not already assigned
        $availableWip = \App\Models\UpdateWip::whereIn('lot_code', $equipmentCodes)
            ->whereIn('wip_status', ['Newlot Standby', 'Rework Lot Standby'])
            ->whereNotIn('lot_id', $assignedLotIds) // Exclude already assigned lots
            ->select(
                'lot_id',
                'model_15',
                'lot_size',
                'lot_qty',
                'stagnant_tat',
                'qty_class',
                'work_type',
                'wip_status',
                'lot_status',
                'hold',
                'auto_yn',
                'lipas_yn',
                'eqp_type',
                'eqp_class',
                'lot_location',
                'lot_code'
            )
            ->orderBy('lot_code')
            ->orderBy('lot_id')
            ->get();
            
        return $availableWip;
    }
    
    /**
     * Assign a lot to a lot request
     */
    public function assignLot(Request $request, LotRequest $lotRequest)
    {
        // Check if user is authenticated
        if (!Auth::check()) {
            return response()->json(['error' => 'Authentication required'], 401);
        }
        
        // Check permissions
        if (!$this->getAuthenticatedUser()->canManageOrders() && $lotRequest->user_id !== Auth::user()->id) {
            return response()->json(['error' => 'Unauthorized access'], 403);
        }
        
        // Validate request
        $request->validate([
            'lot_id' => 'required|string',
            'lot_code' => 'required|string'
        ]);
        
        DB::beginTransaction();
        
        try {
            // Check if lot is already assigned
            $existingAssignment = \App\Models\LotAssignment::where('lot_id', $request->lot_id)->first();
            if ($existingAssignment) {
                return response()->json([
                    'success' => false,
                    'message' => 'This lot is already assigned to another request.'
                ], 400);
            }
            
            // Get the lot from WIP to verify it exists and get details
            $wipLot = \App\Models\UpdateWip::where('lot_id', $request->lot_id)
                ->where('lot_code', $request->lot_code)
                ->whereIn('wip_status', ['Newlot Standby', 'Rework Lot Standby'])
                ->first();
                
            if (!$wipLot) {
                return response()->json([
                    'success' => false,
                    'message' => 'Lot not found or not available for assignment.'
                ], 404);
            }
            
            // Get equipment from lot request that matches the lot code
            $equipmentItem = $lotRequest->lotRequestItems
                ->where('equipment_code', $request->lot_code)
                ->first();
                
            if (!$equipmentItem) {
                return response()->json([
                    'success' => false,
                    'message' => 'No matching equipment found for this lot code.'
                ], 400);
            }
            
            // Create lot assignment
            \App\Models\LotAssignment::create([
                'lot_request_id' => $lotRequest->id,
                'lot_id' => $request->lot_id,
                'lot_code' => $request->lot_code,
                'equipment_number' => $equipmentItem->equipment_number,
                'equipment_code' => $equipmentItem->equipment_code,
                'lot_quantity' => $wipLot->lot_qty,
                'assigned_date' => now(),
                'assigned_by' => Auth::user()->id
            ]);
            
            // Automatically update lot request status to 'in_process' if it's pending
            if ($lotRequest->status === 'pending') {
                $lotRequest->update([
                    'status' => 'in_process',
                    'responded_at' => now()
                ]);
            }
            
            DB::commit();
            
            return response()->json([
                'success' => true,
                'message' => 'Lot assigned successfully!',
                'lot_request_status' => $lotRequest->fresh()->status
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign lot. Please try again.'
            ], 500);
        }
    }
}
