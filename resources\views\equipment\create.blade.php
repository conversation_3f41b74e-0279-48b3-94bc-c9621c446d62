<x-app-layout>
    <x-slot name="header">
        Add New Equipment
    </x-slot>

    <div class="row justify-content-center">
        <div class="col-12 col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-plus me-2"></i>Add New Equipment
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('equipment.store') }}">
                        @csrf
                        
                        <div class="row g-3">
                            <div class="col-md-6">
                                <label for="eqp_no" class="form-label">Equipment Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_no') is-invalid @enderror" 
                                       name="eqp_no" id="eqp_no" value="{{ old('eqp_no') }}" required>
                                @error('eqp_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="eqp_line" class="form-label">Equipment Line <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_line') is-invalid @enderror" 
                                       name="eqp_line" id="eqp_line" value="{{ old('eqp_line') }}" required>
                                @error('eqp_line')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="eqp_area" class="form-label">Equipment Area</label>
                                <input type="text" class="form-control @error('eqp_area') is-invalid @enderror" 
                                       name="eqp_area" id="eqp_area" value="{{ old('eqp_area') }}">
                                @error('eqp_area')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="eqp_type" class="form-label">Equipment Type <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_type') is-invalid @enderror" 
                                       name="eqp_type" id="eqp_type" value="{{ old('eqp_type') }}" required>
                                @error('eqp_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="eqp_class" class="form-label">Equipment Class</label>
                                <input type="text" class="form-control @error('eqp_class') is-invalid @enderror" 
                                       name="eqp_class" id="eqp_class" value="{{ old('eqp_class') }}">
                                @error('eqp_class')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="eqp_maker" class="form-label">Equipment Maker <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_maker') is-invalid @enderror" 
                                       name="eqp_maker" id="eqp_maker" value="{{ old('eqp_maker') }}" required>
                                @error('eqp_maker')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="feeder_type" class="form-label">Feeder Type</label>
                                <input type="text" class="form-control @error('feeder_type') is-invalid @enderror" 
                                       name="feeder_type" id="feeder_type" value="{{ old('feeder_type') }}">
                                @error('feeder_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="lot_size" class="form-label">Lot Size <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('lot_size') is-invalid @enderror" 
                                       name="lot_size" id="lot_size" value="{{ old('lot_size') }}" required>
                                @error('lot_size')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="work_type" class="form-label">Work Type <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('work_type') is-invalid @enderror" 
                                       name="work_type" id="work_type" value="{{ old('work_type') }}" required>
                                @error('work_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="lot_type" class="form-label">Lot Type <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('lot_type') is-invalid @enderror" 
                                       name="lot_type" id="lot_type" value="{{ old('lot_type') }}" required>
                                @error('lot_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="lotqty_alloc" class="form-label">Lot Quantity Allocation <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" class="form-control @error('lotqty_alloc') is-invalid @enderror" 
                                       name="lotqty_alloc" id="lotqty_alloc" value="{{ old('lotqty_alloc') }}" required>
                                @error('lotqty_alloc')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="eqp_oee" class="form-label">Equipment OEE <span class="text-danger">*</span></label>
                                <input type="number" step="0.0001" min="0" max="1" class="form-control @error('eqp_oee') is-invalid @enderror" 
                                       name="eqp_oee" id="eqp_oee" value="{{ old('eqp_oee') }}" required>
                                <div class="form-text">Value between 0 and 1 (e.g., 0.85 for 85%)</div>
                                @error('eqp_oee')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="eqp_speed" class="form-label">Equipment Speed <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control @error('eqp_speed') is-invalid @enderror" 
                                       name="eqp_speed" id="eqp_speed" value="{{ old('eqp_speed') }}" required>
                                @error('eqp_speed')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="operation_time" class="form-label">Operation Time <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control @error('operation_time') is-invalid @enderror" 
                                       name="operation_time" id="operation_time" value="{{ old('operation_time') }}" required>
                                @error('operation_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="eqp_code" class="form-label">Equipment Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_code') is-invalid @enderror" 
                                       name="eqp_code" id="eqp_code" value="{{ old('eqp_code') }}" required>
                                @error('eqp_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6">
                                <label for="ongoing_lot" class="form-label">Ongoing Lot</label>
                                <input type="text" class="form-control @error('ongoing_lot') is-invalid @enderror" 
                                       name="ongoing_lot" id="ongoing_lot" value="{{ old('ongoing_lot') }}">
                                @error('ongoing_lot')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-1"></i>Create Equipment
                                    </button>
                                    <a href="{{ route('equipment.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
