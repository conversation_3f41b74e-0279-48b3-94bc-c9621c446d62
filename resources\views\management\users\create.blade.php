<x-app-layout>
    <x-slot name="header">
        Add New User
    </x-slot>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Create User</h4>
                <a href="{{ route('management.users.index') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Users
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8 col-md-10 mx-auto">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">User Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.users.store') }}" method="POST">
                        @csrf
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="emp_name" class="form-label">Full Name <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('emp_name') is-invalid @enderror" 
                                       id="emp_name" 
                                       name="emp_name" 
                                       value="{{ old('emp_name') }}" 
                                       required>
                                @error('emp_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="emp_no" class="form-label">Employee Number <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('emp_no') is-invalid @enderror" 
                                       id="emp_no" 
                                       name="emp_no" 
                                       value="{{ old('emp_no') }}" 
                                       required>
                                @error('emp_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">Password <span class="text-danger">*</span></label>
                                <input type="password" 
                                       class="form-control @error('password') is-invalid @enderror" 
                                       id="password" 
                                       name="password" 
                                       required>
                                @error('password')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="password_confirmation" class="form-label">Confirm Password <span class="text-danger">*</span></label>
                                <input type="password" 
                                       class="form-control" 
                                       id="password_confirmation" 
                                       name="password_confirmation" 
                                       required>
                            </div>
                        </div>

                        <!-- Employee Information -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="position" class="form-label">Position</label>
                                <input type="text" 
                                       class="form-control @error('position') is-invalid @enderror" 
                                       id="position" 
                                       name="position" 
                                       value="{{ old('position') }}">
                                @error('position')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-6 mb-3">
                                <label for="title_class" class="form-label">Title Class</label>
                                <input type="text" 
                                       class="form-control @error('title_class') is-invalid @enderror" 
                                       id="title_class" 
                                       name="title_class" 
                                       value="{{ old('title_class') }}">
                                @error('title_class')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label for="rank" class="form-label">Rank</label>
                                <input type="text" 
                                       class="form-control @error('rank') is-invalid @enderror" 
                                       id="rank" 
                                       name="rank" 
                                       value="{{ old('rank') }}">
                                @error('rank')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="hr_job_name" class="form-label">HR Job Name</label>
                                <input type="text" 
                                       class="form-control @error('hr_job_name') is-invalid @enderror" 
                                       id="hr_job_name" 
                                       name="hr_job_name" 
                                       value="{{ old('hr_job_name') }}">
                                @error('hr_job_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4 mb-3">
                                <label for="job_assigned" class="form-label">Job Assigned</label>
                                <input type="text" 
                                       class="form-control @error('job_assigned') is-invalid @enderror" 
                                       id="job_assigned" 
                                       name="job_assigned" 
                                       value="{{ old('job_assigned') }}">
                                @error('job_assigned')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-4">
                            <label for="role" class="form-label">User Role <span class="text-danger">*</span></label>
                            <select class="form-select @error('role') is-invalid @enderror" 
                                    id="role" 
                                    name="role" 
                                    required>
                                <option value="">Select Role</option>
                                <option value="USER" {{ old('role') === 'USER' ? 'selected' : '' }}>Regular User</option>
                                <option value="MANAGER" {{ old('role') === 'MANAGER' ? 'selected' : '' }}>Manager</option>
                                <option value="ADMIN" {{ old('role') === 'ADMIN' ? 'selected' : '' }}>Administrator</option>
                            </select>
                            @error('role')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                <strong>Regular User:</strong> Can view products, place orders, and manage their own profile.<br>
                                <strong>Manager:</strong> Can view and update all orders, but limited access to other admin features.<br>
                                <strong>Administrator:</strong> Has full access to all system features and management functions.
                            </div>
                        </div>

                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('management.users.index') }}" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-user-plus me-2"></i>Create User
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>