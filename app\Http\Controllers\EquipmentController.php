<?php

namespace App\Http\Controllers;

use App\Models\Equipment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class EquipmentController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('order.manage')->except(['index', 'show']);
    }

    /**
     * Display a listing of the equipment.
     */
    public function index(Request $request)
    {
        // Get filter parameters with defaults (showing all data initially)
        $filters = [
            'eqp_line' => $request->get('eqp_line', 'all'),
            'lot_size' => $request->get('lot_size', 'all'),
            'eqp_maker' => $request->get('eqp_maker', 'all'),
            'eqp_type' => $request->get('eqp_type', 'all'),
            'work_type' => $request->get('work_type', 'all'),
            'wildcard_search' => $request->get('wildcard_search', ''),
        ];
        
        // Build Equipment summary query with filters
        $equipmentQuery = Equipment::select(
            'id',
            'eqp_no',
            'eqp_line',
            'eqp_code',
            'eqp_class',
            'lot_type',
            'lotqty_alloc',
            DB::raw('COUNT(*) as eqp_count'),
            DB::raw('SUM(CAST(eqp_oee AS DECIMAL(8,4)) * CAST(eqp_speed AS DECIMAL(8,4)) * CAST(operation_time AS DECIMAL(8,4))) as daily_capa'),
            DB::raw('AVG(CAST(eqp_oee AS DECIMAL(8,4)) * 100) as average_oee_percent')
        );
        
        // Apply filters
        if ($filters['eqp_line'] !== 'all') {
            $equipmentQuery->where('eqp_line', $filters['eqp_line']);
        }
        if ($filters['lot_size'] !== 'all') {
            $equipmentQuery->where('lot_size', $filters['lot_size']);
        }
        if ($filters['eqp_maker'] !== 'all') {
            $equipmentQuery->where('eqp_maker', $filters['eqp_maker']);
        }
        if ($filters['eqp_type'] !== 'all') {
            $equipmentQuery->where('eqp_type', $filters['eqp_type']);
        }
        if ($filters['work_type'] !== 'all') {
            $equipmentQuery->where('work_type', $filters['work_type']);
        }
        
        // Apply wildcard search if provided
        if (!empty($filters['wildcard_search'])) {
            $searchTerm = $filters['wildcard_search'];
            // Convert wildcards: * to % (multiple chars), ? to _ (single char)
            $likeTerm = str_replace(['*', '?'], ['%', '_'], $searchTerm);
            
            $equipmentQuery->where(function($query) use ($likeTerm) {
                $query->where('eqp_no', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_line', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_code', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_area', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_maker', 'LIKE', '%' . $likeTerm . '%');
            });
        }
        
        // Get all equipment for totals calculation (before pagination)
        $allEquipmentSummary = $equipmentQuery
            ->groupBy('id', 'eqp_no', 'eqp_line', 'eqp_code', 'eqp_class', 'lot_type', 'lotqty_alloc')
            ->orderBy('eqp_no')
            ->orderBy('eqp_line')
            ->orderBy('eqp_code')
            ->get();
        
        // Calculate totals for the header
        $totalEquipmentCount = $allEquipmentSummary->sum('eqp_count');
        $totalDailyCapa = $allEquipmentSummary->sum('daily_capa');
        
        // Apply pagination to the equipment summary
        $equipmentSummary = $equipmentQuery
            ->groupBy('id', 'eqp_no', 'eqp_line', 'eqp_code', 'eqp_class', 'lot_type', 'lotqty_alloc')
            ->orderBy('eqp_no')
            ->orderBy('eqp_line')
            ->orderBy('eqp_code')
            ->paginate(35)
            ->appends($filters);
        
        // Get filter options from database
        $filterOptions = [
            'eqp_lines' => Equipment::distinct()->pluck('eqp_line')->filter()->sort()->values(),
            'lot_sizes' => Equipment::distinct()->pluck('lot_size')->filter()->sort()->values(),
            'eqp_makers' => Equipment::distinct()->pluck('eqp_maker')->filter()->sort()->values(),
            'eqp_types' => Equipment::distinct()->pluck('eqp_type')->filter()->sort()->values(),
            'work_types' => Equipment::distinct()->pluck('work_type')->filter()->sort()->values(),
        ];
        
        return view('equipment.index', compact('equipmentSummary', 'filters', 'filterOptions', 'totalEquipmentCount', 'totalDailyCapa'));
    }

    /**
     * Get detailed equipment records for a specific group
     */
    public function getEquipmentGroupDetails(Request $request)
    {
        $request->validate([
            'eqp_line' => 'required|string',
            'eqp_code' => 'required|string',
            'lot_size' => 'nullable|string',
            'eqp_maker' => 'nullable|string',
            'eqp_type' => 'nullable|string',
            'work_type' => 'nullable|string',
            'lot_type' => 'nullable|string',
            'wildcard_search' => 'nullable|string',
        ]);

        $equipmentDetailsQuery = Equipment::select(
            'eqp_no',
            'eqp_line',
            'eqp_area',
            'eqp_type',
            'eqp_class',
            'eqp_maker',
            'feeder_type',
            'lot_size',
            'work_type',
            'lot_type',
            'lotqty_alloc',
            'eqp_oee',
            'eqp_speed',
            'operation_time',
            'eqp_code',
            'ongoing_lot'
        )
        ->where('eqp_line', $request->eqp_line)
        ->where('eqp_code', $request->eqp_code);
        
        // Apply additional filters if provided
        if ($request->has('lot_size') && $request->lot_size !== 'all') {
            $equipmentDetailsQuery->where('lot_size', $request->lot_size);
        }
        if ($request->has('eqp_maker') && $request->eqp_maker !== 'all') {
            $equipmentDetailsQuery->where('eqp_maker', $request->eqp_maker);
        }
        if ($request->has('eqp_type') && $request->eqp_type !== 'all') {
            $equipmentDetailsQuery->where('eqp_type', $request->eqp_type);
        }
        if ($request->has('work_type') && $request->work_type !== 'all') {
            $equipmentDetailsQuery->where('work_type', $request->work_type);
        }
        if ($request->has('lot_type') && $request->lot_type !== 'all') {
            $equipmentDetailsQuery->where('lot_type', $request->lot_type);
        }
        
        // Apply wildcard search if provided
        if ($request->has('wildcard_search') && !empty($request->wildcard_search)) {
            $searchTerm = $request->wildcard_search;
            // Convert wildcards: * to % (multiple chars), ? to _ (single char)
            $likeTerm = str_replace(['*', '?'], ['%', '_'], $searchTerm);
            
            $equipmentDetailsQuery->where(function($query) use ($likeTerm) {
                $query->where('eqp_no', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_line', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_code', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_area', 'LIKE', '%' . $likeTerm . '%')
                      ->orWhere('eqp_maker', 'LIKE', '%' . $likeTerm . '%');
            });
        }
        
        $equipmentDetails = $equipmentDetailsQuery->orderBy('eqp_no')->get();

        return response()->json([
            'success' => true,
            'data' => $equipmentDetails,
            'group_info' => [
                'eqp_line' => $request->eqp_line,
                'eqp_code' => $request->eqp_code,
                'total_equipment' => $equipmentDetails->count(),
                'total_alloc_qty' => $equipmentDetails->sum('lotqty_alloc'),
                'average_oee' => $equipmentDetails->avg(function($item) {
                    return floatval($item->eqp_oee) * floatval($item->eqp_speed) * floatval($item->operation_time);
                })
            ]
        ]);
    }

    /**
     * Show the form for creating a new equipment.
     */
    public function create()
    {
        return view('equipment.create');
    }

    /**
     * Store a newly created equipment in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'eqp_no' => 'required|string|max:255|unique:equipment',
            'eqp_line' => 'required|string|max:255',
            'eqp_area' => 'nullable|string|max:255',
            'eqp_type' => 'required|string|max:255',
            'eqp_class' => 'nullable|string|max:255',
            'eqp_maker' => 'required|string|max:255',
            'feeder_type' => 'nullable|string|max:255',
            'lot_size' => 'required|string|max:255',
            'work_type' => 'required|string|max:255',
            'lot_type' => 'required|string|max:255',
            'lotqty_alloc' => 'required|string|max:10',
            'eqp_oee' => 'required|numeric|min:0|max:1',
            'eqp_speed' => 'required|numeric|min:0',
            'operation_time' => 'required|numeric|min:0',
            'eqp_code' => 'required|string|max:255',
            'ongoing_lot' => 'nullable|string|max:255',
        ]);

        $data = $request->all();
        $data['modified_by'] = Auth::id();

        Equipment::create($data);

        return redirect()->route('equipment.index')
            ->with('success', 'Equipment created successfully.');
    }

    /**
     * Display the specified equipment.
     */
    public function show(Equipment $equipment)
    {
        return view('equipment.show', compact('equipment'));
    }

    /**
     * Show the form for editing the specified equipment.
     */
    public function edit(Request $request, Equipment $equipment)
    {
        // Capture current filter parameters to preserve them
        $currentFilters = $request->only(['eqp_line', 'lot_size', 'eqp_maker', 'eqp_type', 'work_type', 'wildcard_search']);
        $filterQuery = http_build_query(array_filter($currentFilters));
        
        return view('equipment.edit', compact('equipment', 'filterQuery'));
    }

    /**
     * Update the specified equipment in storage.
     */
    public function update(Request $request, Equipment $equipment)
    {
        $request->validate([
            'eqp_no' => 'required|string|max:255|unique:equipment,eqp_no,' . $equipment->id,
            'eqp_line' => 'required|string|max:255',
            'eqp_area' => 'nullable|string|max:255',
            'eqp_type' => 'required|string|max:255',
            'eqp_class' => 'nullable|string|max:255',
            'eqp_maker' => 'required|string|max:255',
            'feeder_type' => 'nullable|string|max:255',
            'lot_size' => 'required|string|max:255',
            'work_type' => 'required|string|max:255',
            'lot_type' => 'required|string|max:255',
            'lotqty_alloc' => 'required|string|max:10',
            'eqp_oee' => 'required|numeric|min:0|max:1',
            'eqp_speed' => 'required|numeric|min:0',
            'operation_time' => 'required|numeric|min:0',
            'eqp_code' => 'required|string|max:255',
            'ongoing_lot' => 'nullable|string|max:255',
        ]);

        $data = $request->all();
        $data['modified_by'] = Auth::id();

        $equipment->update($data);

        // Preserve filter parameters from the referring page
        $filterParams = [];
        if ($request->has('return_filters')) {
            parse_str($request->return_filters, $filterParams);
        }

        return redirect()->route('equipment.index', $filterParams)
            ->with('success', 'Equipment updated successfully.');
    }

    /**
     * Remove the specified equipment from storage.
     */
    public function destroy(Request $request, Equipment $equipment)
    {
        $equipment->delete();
        
        // Preserve filter parameters from the referring page
        $filterParams = [];
        if ($request->has('return_filters')) {
            parse_str($request->return_filters, $filterParams);
        }

        return redirect()->route('equipment.index', $filterParams)
            ->with('success', 'Equipment deleted successfully.');
    }
}
