<x-app-layout>
    <x-slot name="header">
        Edit Equipment
    </x-slot>

    <div class="row justify-content-center">
        <div class="col-12 col-xl-10">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Edit Equipment - {{ $equipment->eqp_no }}
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ route('equipment.update', $equipment) }}">
                        @csrf
                        @method('PUT')
                        
                        <!-- Hidden field to preserve filter parameters -->
                        @if(isset($filterQuery) && !empty($filterQuery))
                            <input type="hidden" name="return_filters" value="{{ $filterQuery }}">
                        @endif
                        
                        <!-- ROW 1: Equipment Number, Equipment Line, Equipment Area -->
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label for="eqp_no" class="form-label">Equipment Number <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_no') is-invalid @enderror" 
                                       name="eqp_no" id="eqp_no" value="{{ old('eqp_no', $equipment->eqp_no) }}" 
                                       placeholder="e.g., V1313" required>
                                @error('eqp_no')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="eqp_line" class="form-label">Equipment Line <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_line') is-invalid @enderror" 
                                       name="eqp_line" id="eqp_line" value="{{ old('eqp_line', $equipment->eqp_line) }}" 
                                       placeholder="e.g., F" required>
                                @error('eqp_line')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="eqp_area" class="form-label">Equipment Area</label>
                                <input type="text" class="form-control @error('eqp_area') is-invalid @enderror" 
                                       name="eqp_area" id="eqp_area" value="{{ old('eqp_area', $equipment->eqp_area) }}"
                                       placeholder="e.g., F1">
                                @error('eqp_area')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- ROW 2: Equipment Maker, Equipment Type, Equipment Class -->
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label for="eqp_maker" class="form-label">Equipment Maker <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_maker') is-invalid @enderror" 
                                       name="eqp_maker" id="eqp_maker" value="{{ old('eqp_maker', $equipment->eqp_maker) }}" 
                                       placeholder="e.g., TWA" required>
                                @error('eqp_maker')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="eqp_type" class="form-label">Equipment Type <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_type') is-invalid @enderror" 
                                       name="eqp_type" id="eqp_type" value="{{ old('eqp_type', $equipment->eqp_type) }}" 
                                       placeholder="e.g., COLOR" required onchange="generateEquipmentCode()">
                                @error('eqp_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="eqp_class" class="form-label">Equipment Class</label>
                                <input type="text" class="form-control @error('eqp_class') is-invalid @enderror" 
                                       name="eqp_class" id="eqp_class" value="{{ old('eqp_class', $equipment->eqp_class) }}"
                                       placeholder="e.g., 6S" onchange="generateEquipmentCode()">
                                @error('eqp_class')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- ROW 3: Lot Size, Work Type, Lot Type -->
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label for="lot_size" class="form-label">Lot Size <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('lot_size') is-invalid @enderror" 
                                       name="lot_size" id="lot_size" value="{{ old('lot_size', $equipment->lot_size) }}" 
                                       placeholder="e.g., 03" required onchange="generateEquipmentCode()">
                                @error('lot_size')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="work_type" class="form-label">Work Type <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('work_type') is-invalid @enderror" 
                                       name="work_type" id="work_type" value="{{ old('work_type', $equipment->work_type) }}" 
                                       placeholder="e.g., NOR" required onchange="generateEquipmentCode()">
                                @error('work_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="lot_type" class="form-label">Lot Type <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('lot_type') is-invalid @enderror" 
                                       name="lot_type" id="lot_type" value="{{ old('lot_type', $equipment->lot_type) }}" 
                                       placeholder="e.g., MAIN" required onchange="generateEquipmentCode()">
                                @error('lot_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- ROW 4: Equipment OEE, Equipment Speed, Operation Time -->
                        <div class="row g-3 mb-3">
                            <div class="col-md-4">
                                <label for="eqp_oee" class="form-label">Equipment OEE <span class="text-danger">*</span></label>
                                <input type="number" step="0.0001" min="0" max="1" class="form-control @error('eqp_oee') is-invalid @enderror" 
                                       name="eqp_oee" id="eqp_oee" value="{{ old('eqp_oee', $equipment->eqp_oee) }}" 
                                       placeholder="e.g., 0.85 (for 85%)" required>
                                <div class="form-text">Value between 0 and 1 (e.g., 0.85 for 85%)</div>
                                @error('eqp_oee')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="eqp_speed" class="form-label">Equipment Speed <span class="text-danger">*</span></label>
                                <input type="number" step="0.01" min="0" class="form-control @error('eqp_speed') is-invalid @enderror" 
                                       name="eqp_speed" id="eqp_speed" value="{{ old('eqp_speed', $equipment->eqp_speed) }}" 
                                       placeholder="e.g., 1.5" required>
                                @error('eqp_speed')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="operation_time" class="form-label">Operation Time <span class="text-danger">*</span></label>
                                <input type="number" step="1" min="0" class="form-control @error('operation_time') is-invalid @enderror" 
                                       name="operation_time" id="operation_time" value="{{ old('operation_time', $equipment->operation_time) }}" 
                                       placeholder="e.g., 1440 (minutes for 1 day)" required>
                                <div class="form-text">Value in minutes (1440 = 1 day, 720 = 12 hours)</div>
                                @error('operation_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- ROW 5: QTY Alloc Class, Feeder Type, Equipment Code -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-4">
                                <label for="lotqty_alloc" class="form-label">QTY Alloc Class <span class="text-danger">*</span></label>
                                <select class="form-select @error('lotqty_alloc') is-invalid @enderror" 
                                        name="lotqty_alloc" id="lotqty_alloc" required onchange="generateEquipmentCode()">
                                    <option value="">Select QTY Alloc Class</option>
                                    <option value="S" {{ old('lotqty_alloc', $equipment->lotqty_alloc) === 'S' ? 'selected' : '' }}>S - Small</option>
                                    <option value="L" {{ old('lotqty_alloc', $equipment->lotqty_alloc) === 'L' ? 'selected' : '' }}>L - Large</option>
                                </select>
                                @error('lotqty_alloc')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="feeder_type" class="form-label">Feeder Type</label>
                                <input type="text" class="form-control @error('feeder_type') is-invalid @enderror" 
                                       name="feeder_type" id="feeder_type" value="{{ old('feeder_type', $equipment->feeder_type) }}"
                                       placeholder="e.g., DAISHIN">
                                @error('feeder_type')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            
                            <div class="col-md-4">
                                <label for="eqp_code" class="form-label">Equipment Code <span class="text-danger">*</span></label>
                                <input type="text" class="form-control @error('eqp_code') is-invalid @enderror" 
                                       name="eqp_code" id="eqp_code" value="{{ old('eqp_code', $equipment->eqp_code) }}" 
                                       placeholder="Auto-generated: e.g., 03-COLOR-6S-NOR-MAIN-L" readonly required>
                                <div class="form-text">Auto-generated from: Lot Size + Equipment Type + Equipment Class + Work Type + Lot Type + QTY Alloc Class</div>
                                @error('eqp_code')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <!-- ROW 6: Ongoing Lot (full width) -->
                        <div class="row g-3 mb-4">
                            <div class="col-md-12">
                                <label for="ongoing_lot" class="form-label">Ongoing Lot</label>
                                <input type="text" class="form-control @error('ongoing_lot') is-invalid @enderror" 
                                       name="ongoing_lot" id="ongoing_lot" value="{{ old('ongoing_lot', $equipment->ongoing_lot) }}"
                                       placeholder="e.g., Current lot number or batch identifier">
                                @error('ongoing_lot')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-2">
                                    <button type="submit" class="btn btn-warning">
                                        <i class="fas fa-save me-1"></i>Update Equipment
                                    </button>
                                    <a href="{{ route('equipment.show', $equipment) }}" class="btn btn-outline-info">
                                        <i class="fas fa-eye me-1"></i>View Details
                                    </a>
                                    <a href="{{ route('equipment.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-times me-1"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function generateEquipmentCode() {
            const lotSize = document.getElementById('lot_size').value.trim();
            const eqpType = document.getElementById('eqp_type').value.trim();
            const eqpClass = document.getElementById('eqp_class').value.trim();
            const workType = document.getElementById('work_type').value.trim();
            const lotType = document.getElementById('lot_type').value.trim();
            const qtyAllocClass = document.getElementById('lotqty_alloc').value.trim();
            
            // Only generate if all required fields are filled
            if (lotSize && eqpType && workType && lotType && qtyAllocClass) {
                let code = lotSize + '-' + eqpType;
                
                // Add equipment class if provided
                if (eqpClass) {
                    code += '-' + eqpClass;
                }
                
                code += '-' + workType + '-' + lotType + '-' + qtyAllocClass;
                
                document.getElementById('eqp_code').value = code;
            }
        }
        
        // Generate code on page load if all fields are filled
        document.addEventListener('DOMContentLoaded', function() {
            generateEquipmentCode();
        });
    </script>
</x-app-layout>
