<x-app-layout>
    <x-slot name="header">
        {{ $title ?? 'Endline WIP Entry Form' }}
    </x-slot>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            Endline WIP Entry Form
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center py-5">
                            <i class="fas fa-clipboard-check fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">Multiple Entry Form</h4>
                            <p class="text-muted">
                                This form will allow for multiple endline WIP entries including:
                            </p>
                            <ul class="list-unstyled text-muted">
                                <li><i class="fas fa-check text-success me-2"></i>Batch data entry capability</li>
                                <li><i class="fas fa-check text-success me-2"></i>Real-time validation</li>
                                <li><i class="fas fa-check text-success me-2"></i>Progress tracking</li>
                                <li><i class="fas fa-check text-success me-2"></i>Data export options</li>
                            </ul>
                            <div class="mt-4">
                                <button class="btn btn-primary me-2">
                                    <i class="fas fa-plus me-1"></i>
                                    Add New Entry
                                </button>
                                <button class="btn btn-outline-secondary">
                                    <i class="fas fa-upload me-1"></i>
                                    Bulk Import
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
