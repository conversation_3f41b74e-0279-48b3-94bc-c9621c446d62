<x-app-layout>
    <x-slot name="header">
        Process WIP Management
    </x-slot>

    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Wip Breakdown</h4>
                @if(Auth::user()->isAdmin())
                    <a href="{{ route('updatewip.create') }}" class="btn btn-primary">
                        <i class="fas fa-sync-alt me-2"></i>Update WIP Data
                    </a>
                @endif
            </div>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <!-- WIP Filters Section -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header d-flex justify-content-between align-items-center" 
                     style="cursor: pointer; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none;" 
                     onclick="toggleFilters()">
                    <h6 class="mb-0 text-white">
                        <i class="fas fa-filter me-2" style="color: #fff; text-shadow: 0 1px 2px rgba(0,0,0,0.2);"></i>
                        <span style="font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.2);">WIP Filters</span>
                        <small class="ms-2" style="color: rgba(255,255,255,0.8);">(Click to collapse)</small>
                    </h6>
                    <i class="fas fa-chevron-up text-white" id="filter-toggle-icon" style="text-shadow: 0 1px 2px rgba(0,0,0,0.2);"></i>
                </div>
                <div class="card-body" id="filter-section" style="display: block;">
                    <form method="GET" action="{{ route('updatewip.index') }}" id="wip-filter-form">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <label for="hold" class="form-label">Hold</label>
                                <select class="form-select form-select-sm" name="hold" id="hold">
                                    <option value="all" {{ $filters['hold'] === 'all' ? 'selected' : '' }}>All</option>
                                    <option value="N" {{ $filters['hold'] === 'N' ? 'selected' : '' }}>N (Recommended)</option>
                                    <option value="Y" {{ $filters['hold'] === 'Y' ? 'selected' : '' }}>Y</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="lot_size" class="form-label">Lot Size</label>
                                <select class="form-select form-select-sm" name="lot_size" id="lot_size">
                                    <option value="all" {{ $filters['lot_size'] === 'all' ? 'selected' : '' }}>All Sizes</option>
                                    @foreach($filterOptions['lot_sizes'] as $size)
                                        <option value="{{ $size }}" {{ $filters['lot_size'] === $size ? 'selected' : '' }}>{{ $size }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="qty_class" class="form-label">Quantity Class</label>
                                <select class="form-select form-select-sm" name="qty_class" id="qty_class">
                                    <option value="all" {{ $filters['qty_class'] === 'all' ? 'selected' : '' }}>All Classes</option>
                                    @foreach($filterOptions['qty_classes'] as $class)
                                        <option value="{{ $class }}" {{ $filters['qty_class'] === $class ? 'selected' : '' }}>{{ $class }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="work_type" class="form-label">Work Type</label>
                                <select class="form-select form-select-sm" name="work_type" id="work_type">
                                    <option value="all" {{ $filters['work_type'] === 'all' ? 'selected' : '' }}>All Types</option>
                                    @foreach($filterOptions['work_types'] as $type)
                                        <option value="{{ $type }}" {{ $filters['work_type'] === $type ? 'selected' : '' }}>{{ $type }}{{ $type === 'NOR' ? ' (Default)' : '' }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="wip_status" class="form-label">WIP Status</label>
                                <select class="form-select form-select-sm" name="wip_status" id="wip_status">
                                    <option value="all" {{ $filters['wip_status'] === 'all' ? 'selected' : '' }}>All Status</option>
                                    @foreach($filterOptions['wip_statuses'] as $status)
                                        <option value="{{ $status }}" {{ $filters['wip_status'] === $status ? 'selected' : '' }}>{{ $status }}{{ $status === 'MAIN' ? ' (Default)' : '' }}</option>
                                    @endforeach
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label for="lipas_yn" class="form-label">Lipas</label>
                                <select class="form-select form-select-sm" name="lipas_yn" id="lipas_yn">
                                    <option value="all" {{ $filters['lipas_yn'] === 'all' ? 'selected' : '' }}>All</option>
                                    <option value="Y" {{ $filters['lipas_yn'] === 'Y' ? 'selected' : '' }}>Y</option>
                                    <option value="N" {{ $filters['lipas_yn'] === 'N' ? 'selected' : '' }}>N</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label for="auto_yn" class="form-label">Auto</label>
                                <select class="form-select form-select-sm" name="auto_yn" id="auto_yn">
                                    <option value="all" {{ $filters['auto_yn'] === 'all' ? 'selected' : '' }}>All</option>
                                    <option value="Y" {{ $filters['auto_yn'] === 'Y' ? 'selected' : '' }}>Y</option>
                                    <option value="N" {{ $filters['auto_yn'] === 'N' ? 'selected' : '' }}>N</option>
                                </select>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="d-flex gap-2 align-items-end">
                                    <div class="flex-shrink-0">
                                        <label for="wildcard_search" class="form-label mb-1"><small>Wildcard Search</small></label>
                                        <input type="text" 
                                               class="form-control form-control-sm" 
                                               name="wildcard_search" 
                                               id="wildcard_search" 
                                               value="{{ $filters['wildcard_search'] ?? '' }}"
                                               placeholder="Search lot ID, model, code..."
                                               style="width: 200px;"
                                               title="Search across Lot ID, Model, Lot Code, and Location using wildcards (* for multiple chars, ? for single char)">
                                    </div>
                                    <button type="submit" class="btn btn-primary btn-sm">
                                        <i class="fas fa-search me-1"></i>Apply Filters
                                    </button>
                                    <a href="{{ route('updatewip.index') }}" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-undo me-1"></i>Reset
                                    </a>
                                    <div class="ms-auto">
                                        <small class="text-muted">
                                            <i class="fas fa-info-circle me-1"></i>
                                            Defaults: Hold=N, Work Type=NOR, WIP Status=MAIN
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- WIP Summary Section -->
    @if(isset($wipSummary))
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 shadow-sm">
                    <div class="card-header border-bottom d-flex justify-content-between align-items-center" 
                         style="background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); border: none;">
                        <h6 class="mb-0 text-white">
                            <i class="fas fa-chart-bar me-2" style="color: #fff; text-shadow: 0 1px 2px rgba(0,0,0,0.3);"></i>
                            <span style="font-weight: 600; text-shadow: 0 1px 2px rgba(0,0,0,0.3);">WIP Summary</span>
                            <span class="badge ms-2" style="background: rgba(255,255,255,0.2); color: white; font-weight: 500; border: 1px solid rgba(255,255,255,0.3);">{{ $wipSummary->count() }} Groups</span>
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        @if($wipSummary->count() > 0)
                            <div class="table-responsive">
                                <table class="table table-hover mb-0" id="wipSummaryTable">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(0)">Lot Size <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(1)">Lot Code <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(2)">Inspection Type <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(3)">Machine Type <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(4)">Work Type <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(5)">Lot Count <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(6)">Total Quantity <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(7)">Average Quantity <i class="fas fa-sort text-muted"></i></th>
                                            <th style="cursor: pointer;" onclick="sortSummaryTable(8)">Average TAT <i class="fas fa-sort text-muted"></i></th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($wipSummary as $wip)
                                            <tr>
                                                <td data-sort="{{ $wip->lot_size }}">
                                                    <span class="badge bg-info">{{ $wip->lot_size }}</span>
                                                </td>
                                                <td data-sort="{{ $wip->lot_code }}">
                                                    <code class="text-primary">{{ $wip->lot_code }}</code>
                                                </td>
                                                <td data-sort="{{ $wip->eqp_class }}">
                                                    <span class="badge bg-secondary">{{ $wip->eqp_class }}</span>
                                                </td>
                                                <td data-sort="{{ $wip->eqp_type }}">
                                                    <span class="badge bg-warning text-dark">{{ $wip->eqp_type }}</span>
                                                </td>
                                                <td data-sort="{{ $wip->work_type }}">
                                                    <span class="badge bg-success">{{ $wip->work_type }}</span>
                                                </td>
                                                <td data-sort="{{ $wip->lot_count }}">
                                                    <strong>{{ number_format($wip->lot_count) }}</strong> lots
                                                </td>
                                                <td data-sort="{{ $wip->total_quantity }}">
                                                    <strong>{{ number_format($wip->total_quantity) }}</strong> pcs
                                                </td>
                                                <td data-sort="{{ number_format($wip->average_quantity, 2) }}">
                                                    <strong>{{ number_format($wip->average_quantity, 2) }}</strong> avg
                                                </td>
                                                <td data-sort="{{ number_format($wip->average_tat, 2) }}">
                                                    <span class="badge bg-danger">{{ number_format($wip->average_tat, 2) }}</span> days
                                                </td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button class="btn btn-sm btn-outline-info" 
                                                                title="View Details"
                                                                onclick="showWipDetails('{{ $wip->lot_size }}', '{{ $wip->lot_code }}', '{{ $wip->eqp_class }}', '{{ $wip->eqp_type }}', '{{ $wip->work_type }}', { hold: '{{ $filters['hold'] }}', qty_class: '{{ $filters['qty_class'] }}', wip_status: '{{ $filters['wip_status'] }}', lipas_yn: '{{ $filters['lipas_yn'] }}', auto_yn: '{{ $filters['auto_yn'] }}', wildcard_search: '{{ $filters['wildcard_search'] ?? '' }}' })">
                                                            <i class="fas fa-eye"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-5">
                                <i class="fas fa-filter fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No WIP Data Found</h5>
                                <p class="text-muted">No records match the current filter criteria.</p>
                                <div class="d-flex justify-content-center gap-2">
                                    <a href="{{ route('updatewip.index') }}" class="btn btn-outline-primary btn-sm">
                                        <i class="fas fa-undo me-1"></i>Reset Filters
                                    </a>
                                    @if(Auth::user()->isAdmin())
                                        <a href="{{ route('updatewip.create') }}" class="btn btn-primary btn-sm">
                                            <i class="fas fa-plus me-1"></i>Update WIP Data
                                        </a>
                                    @endif
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- WIP Details Modal -->
    <div class="modal fade" id="wipDetailsModal" tabindex="-1" aria-labelledby="wipDetailsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" style="max-width: 90%; width: 1200px;">
            <div class="modal-content" style="height: 80vh; display: flex; flex-direction: column;">
                <div class="modal-header">
                    <h5 class="modal-title" id="wipDetailsModalLabel">
                        <i class="fas fa-info-circle me-2"></i>WIP Details
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" style="flex: 1; overflow-y: auto; padding: 1.5rem;">
                    <div id="wipDetailsContent">
                        <!-- Details will be loaded here -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleFilters() {
            const filterSection = document.getElementById('filter-section');
            const toggleIcon = document.getElementById('filter-toggle-icon');
            
            if (filterSection.style.display === 'none') {
                filterSection.style.display = 'block';
                toggleIcon.className = 'fas fa-chevron-up';
            } else {
                filterSection.style.display = 'none';
                toggleIcon.className = 'fas fa-chevron-down';
            }
        }
        
        function showWipDetails(lotSize, lotCode, eqpClass, eqpType, workType, filters = {}) {
            const modal = new bootstrap.Modal(document.getElementById('wipDetailsModal'));
            const content = document.getElementById('wipDetailsContent');
            
            // Show loading state
            content.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading lot details...</p>
                </div>
            `;
            
            modal.show();
            
            // Build parameters including filters
            const params = {
                lot_size: lotSize,
                lot_code: lotCode,
                eqp_class: eqpClass,
                eqp_type: eqpType,
                work_type: workType,
                ...filters
            };
            
            // Fetch actual lot details via AJAX
            fetch('/updatewip/wip-group-details?' + new URLSearchParams(params))
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    content.innerHTML = generateLotDetailsHtml(data.data, data.group_info);
                } else {
                    content.innerHTML = `
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            Error loading lot details. Please try again.
                        </div>
                    `;
                }
            })
            .catch(error => {
                content.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Error: ${error.message}
                    </div>
                `;
            });
        }
        
        function generateLotDetailsHtml(lots, groupInfo) {
            let html = `
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center p-3 rounded" 
                             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <div>
                                <h6 class="mb-1 text-white">Group Summary</h6>
                                <div class="d-flex gap-3">
                                    <span class="badge bg-white text-primary">${groupInfo.total_lots} Lots</span>
                                    <span class="badge bg-white text-success">${parseInt(groupInfo.total_quantity).toLocaleString()} Units</span>
                                    <span class="badge bg-white text-danger">${parseFloat(groupInfo.average_tat).toFixed(2)} Avg TAT</span>
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="text-white small">
                                    <div><span class="badge bg-info">${groupInfo.lot_size}</span> <code style="background: rgba(255,255,255,0.2); color: white;">${groupInfo.lot_code}</code></div>
                                    <div class="mt-1">
                                        <span class="badge bg-secondary">${groupInfo.eqp_class}</span>
                                        <span class="badge bg-warning text-dark">${groupInfo.eqp_type}</span>
                                        <span class="badge bg-success">${groupInfo.work_type}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover table-sm" id="lotDetailsTable">
                        <thead class="table-dark">
                            <tr>
                                <th width="60"><i class="fas fa-list-ol me-1"></i>#</th>
                                <th style="cursor: pointer;" onclick="sortTable(1)"><i class="fas fa-hashtag me-1"></i>Lot No <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(2)"><i class="fas fa-microchip me-1"></i>Model <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(3)"><i class="fas fa-box me-1"></i>Quantity <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(4)"><i class="fas fa-clock me-1"></i>Stagnation <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(5)"><i class="fas fa-map-marker-alt me-1"></i>Location <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(6)"><i class="fas fa-barcode me-1"></i>Code <i class="fas fa-sort text-muted"></i></th>
                                <th width="80"><i class="fas fa-print me-1"></i>Print</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            lots.forEach((lot, index) => {
                const rowClass = index % 2 === 0 ? '' : 'table-light';
                const tatClass = parseFloat(lot.stagnant_tat) > 30 ? 'text-danger' : (parseFloat(lot.stagnant_tat) > 15 ? 'text-warning' : 'text-success');
                const rowNumber = index + 1;
                
                html += `
                    <tr class="${rowClass}">
                        <td class="text-center"><span class="badge bg-light text-dark">${rowNumber}</span></td>
                        <td data-sort="${lot.lot_id}"><strong>${lot.lot_id}</strong></td>
                        <td data-sort="${lot.model_15}"><code class="text-primary">${lot.model_15}</code></td>
                        <td data-sort="${parseInt(lot.lot_qty)}"><span class="badge bg-info">${parseInt(lot.lot_qty).toLocaleString()}</span></td>
                        <td data-sort="${parseFloat(lot.stagnant_tat)}"><span class="badge ${tatClass === 'text-danger' ? 'bg-danger' : (tatClass === 'text-warning' ? 'bg-warning text-dark' : 'bg-success')}">${parseFloat(lot.stagnant_tat).toFixed(2)} days</span></td>
                        <td data-sort="${lot.lot_location}"><span class="badge bg-secondary">${lot.lot_location}</span></td>
                        <td data-sort="${lot.code}"><code class="text-dark fw-bold" style="background-color: #f8f9fa; padding: 2px 6px; border-radius: 3px; border: 1px solid #dee2e6;">${lot.code}</code></td>
                        <td class="text-center">
                            <button class="btn btn-sm btn-outline-success" 
                                    title="Print Lot Details"
                                    onclick="printLotDetails('${lot.lot_id}', '${lot.model_15}', '${lot.lot_qty}', '${lot.stagnant_tat}', '${lot.lot_location}', '${lot.code}')">
                                <i class="fas fa-print"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            html += `
                        </tbody>
                    </table>
                </div>
                
                ${lots.length === 0 ? `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No lots found for this group.</p>
                    </div>
                ` : `
                    <div class="mt-3 p-3 bg-light rounded">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Showing ${lots.length} individual lot(s) for this group.
                        </small>
                    </div>
                `}
            `;
            
            return html;
        }
        
        function printLotDetails(lotId, model, quantity, stagnation, location, code) {
            // Create a new window for printing
            const printWindow = window.open('', '_blank', 'width=600,height=400');
            
            // Generate print content
            const printContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Lot Details - ${lotId}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
                        .details-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
                        .details-table th, .details-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
                        .details-table th { background-color: #f8f9fa; font-weight: bold; }
                        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                        @media print { 
                            body { margin: 0; } 
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>WIP Lot Details Report</h2>
                        <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                    </div>
                    
                    <table class="details-table">
                        <tr>
                            <th>Lot Number</th>
                            <td><strong>${lotId}</strong></td>
                        </tr>
                        <tr>
                            <th>Model</th>
                            <td>${model}</td>
                        </tr>
                        <tr>
                            <th>Quantity</th>
                            <td>${parseInt(quantity).toLocaleString()} units</td>
                        </tr>
                        <tr>
                            <th>Stagnation TAT</th>
                            <td>${parseFloat(stagnation).toFixed(2)} days</td>
                        </tr>
                        <tr>
                            <th>Location</th>
                            <td>${location}</td>
                        </tr>
                        <tr>
                            <th>Code</th>
                            <td>${code}</td>
                        </tr>
                    </table>
                    
                    <div class="footer">
                        <p>WIP Management Dashboard - Process Dashboard System</p>
                    </div>
                    
                    <div class="no-print" style="margin-top: 20px; text-align: center;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Print This Report</button>
                        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Close Window</button>
                    </div>
                </body>
                </html>
            `;
            
            printWindow.document.write(printContent);
            printWindow.document.close();
            
            // Auto-print after a short delay to ensure content is loaded
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
            }, 250);
        }
        
        // Summary table sorting variables
        let currentSummarySortColumn = -1;
        let summarySortDirection = 'asc';
        
        // Modal table sorting variables
        let currentSortColumn = -1;
        let sortDirection = 'asc';
        
        function sortSummaryTable(columnIndex) {
            const table = document.getElementById('wipSummaryTable');
            if (!table) return;
            
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = Array.from(tbody.getElementsByTagName('tr'));
            
            // Determine sort direction
            if (currentSummarySortColumn === columnIndex) {
                summarySortDirection = summarySortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                summarySortDirection = 'asc';
                currentSummarySortColumn = columnIndex;
            }
            
            // Update header icons
            const headers = table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                const sortIcon = header.querySelector('i.fa-sort, i.fa-sort-up, i.fa-sort-down');
                if (sortIcon && index !== 9) { // Skip Actions column
                    if (index === columnIndex) {
                        sortIcon.className = summarySortDirection === 'asc' ? 'fas fa-sort-up text-success' : 'fas fa-sort-down text-success';
                    } else {
                        sortIcon.className = 'fas fa-sort text-muted';
                    }
                }
            });
            
            // Sort rows
            rows.sort((a, b) => {
                const cellA = a.children[columnIndex];
                const cellB = b.children[columnIndex];
                
                let valueA, valueB;
                
                // Get sort value from data-sort attribute
                if (cellA.hasAttribute('data-sort')) {
                    valueA = cellA.getAttribute('data-sort');
                    valueB = cellB.getAttribute('data-sort');
                    
                    // Handle numeric values for specific columns
                    if (columnIndex >= 5 && columnIndex <= 8) { // Lot Count, Total Quantity, Average Quantity, Average TAT
                        valueA = parseFloat(valueA.replace(/,/g, '')) || 0;
                        valueB = parseFloat(valueB.replace(/,/g, '')) || 0;
                    }
                } else {
                    valueA = cellA.textContent.trim();
                    valueB = cellB.textContent.trim();
                }
                
                let comparison = 0;
                if (typeof valueA === 'number' && typeof valueB === 'number') {
                    comparison = valueA - valueB;
                } else {
                    comparison = valueA.toString().localeCompare(valueB.toString());
                }
                
                return summarySortDirection === 'asc' ? comparison : -comparison;
            });
            
            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
        }
        
        function sortTable(columnIndex) {
            const table = document.getElementById('lotDetailsTable');
            if (!table) return;
            
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = Array.from(tbody.getElementsByTagName('tr'));
            
            // Determine sort direction
            if (currentSortColumn === columnIndex) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortDirection = 'asc';
                currentSortColumn = columnIndex;
            }
            
            // Update header icons
            const headers = table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                const sortIcon = header.querySelector('i.fa-sort, i.fa-sort-up, i.fa-sort-down');
                if (sortIcon && index !== 0 && index !== 7) { // Skip # and Print columns
                    if (index === columnIndex) {
                        sortIcon.className = sortDirection === 'asc' ? 'fas fa-sort-up text-warning' : 'fas fa-sort-down text-warning';
                    } else {
                        sortIcon.className = 'fas fa-sort text-muted';
                    }
                }
            });
            
            // Sort rows
            rows.sort((a, b) => {
                const cellA = a.children[columnIndex];
                const cellB = b.children[columnIndex];
                
                let valueA, valueB;
                
                // Get sort value from data-sort attribute or text content
                if (cellA.hasAttribute('data-sort')) {
                    valueA = cellA.getAttribute('data-sort');
                    valueB = cellB.getAttribute('data-sort');
                    
                    // Handle numeric values
                    if (columnIndex === 3 || columnIndex === 4) { // Quantity or Stagnation columns
                        valueA = parseFloat(valueA) || 0;
                        valueB = parseFloat(valueB) || 0;
                    }
                } else {
                    valueA = cellA.textContent.trim();
                    valueB = cellB.textContent.trim();
                }
                
                let comparison = 0;
                if (typeof valueA === 'number' && typeof valueB === 'number') {
                    comparison = valueA - valueB;
                } else {
                    comparison = valueA.toString().localeCompare(valueB.toString());
                }
                
                return sortDirection === 'asc' ? comparison : -comparison;
            });
            
            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
            
            // Update row numbers
            rows.forEach((row, index) => {
                const numberCell = row.children[0];
                numberCell.innerHTML = `<span class="badge bg-light text-dark">${index + 1}</span>`;
                
                // Update alternating row classes
                row.className = index % 2 === 0 ? '' : 'table-light';
            });
        }
    </script>
</x-app-layout>
