<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class Endtime extends Model
{
    use HasFactory;
    
    protected $table = 'endtime';
    
    protected $fillable = [
        'lot_id',
        'model_15',
        'lot_size',
        'lot_qty',
        'work_type',
        'lot_type',
        'lipas_yn',
        'eqp_1',
        'eqp_2',
        'eqp_3',
        'eqp_4',
        'eqp_5',
        'eqp_6',
        'eqp_7',
        'eqp_8',
        'eqp_9',
        'eqp_10',
        'start_time_1',
        'start_time_2',
        'start_time_3',
        'start_time_4',
        'start_time_5',
        'start_time_6',
        'start_time_7',
        'start_time_8',
        'start_time_9',
        'start_time_10',
        'ng_percent_1',
        'ng_percent_2',
        'ng_percent_3',
        'ng_percent_4',
        'ng_percent_5',
        'ng_percent_6',
        'ng_percent_7',
        'ng_percent_8',
        'ng_percent_9',
        'ng_percent_10',
        'eqp_line',
        'eqp_area',
        'status',
        'est_endtime',
        'modified_by',
        'remarks',
        'submission_notes',
        'actual_submitted_at',
    ];
    
    protected $casts = [
        'est_endtime' => 'datetime',
        'actual_submitted_at' => 'datetime',
        'lot_qty' => 'integer',
        'ng_percent_1' => 'decimal:2',
        'ng_percent_2' => 'decimal:2',
        'ng_percent_3' => 'decimal:2',
        'ng_percent_4' => 'decimal:2',
        'ng_percent_5' => 'decimal:2',
        'ng_percent_6' => 'decimal:2',
        'ng_percent_7' => 'decimal:2',
        'ng_percent_8' => 'decimal:2',
        'ng_percent_9' => 'decimal:2',
        'ng_percent_10' => 'decimal:2',
    ];
    
    /**
     * Get lots that are estimated to finish today
     */
    public function scopeFinishingToday($query)
    {
        return $query->whereDate('est_endtime', today());
    }
    
    /**
     * Get lots by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }
    
    /**
     * Get lots by equipment line
     */
    public function scopeByEquipmentLine($query, $line)
    {
        return $query->where('eqp_line', $line);
    }
    
    /**
     * Get lots by equipment area
     */
    public function scopeByEquipmentArea($query, $area)
    {
        return $query->where('eqp_area', $area);
    }
    
    /**
     * Get overdue lots (estimated end time has passed)
     */
    public function scopeOverdue($query)
    {
        return $query->where('est_endtime', '<', now());
    }
}
