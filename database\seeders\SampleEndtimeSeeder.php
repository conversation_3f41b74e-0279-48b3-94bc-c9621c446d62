<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Endtime;

class SampleEndtimeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * 
     * Creates sample endtime data for development and testing.
     */
    public function run(): void
    {
        $this->command->info('Creating sample Endtime data...');
        
        // Create 30 ongoing lots
        Endtime::factory(30)->ongoing()->create();
        $this->command->info('Created 30 ongoing lots');
        
        // Create 10 early submitted lots
        Endtime::factory(10)->early()->create();
        $this->command->info('Created 10 early submitted lots');
        
        // Create 15 on-time submitted lots
        Endtime::factory(15)->onTime()->create();
        $this->command->info('Created 15 on-time submitted lots');
        
        // Create 8 delayed submitted lots
        Endtime::factory(8)->delayed()->create();
        $this->command->info('Created 8 delayed submitted lots');
        
        // Create some mixed random lots
        Endtime::factory(20)->create();
        $this->command->info('Created 20 mixed random lots');
        
        $this->command->info('Sample Endtime data creation completed!');
        $this->command->info('Total: ' . Endtime::count() . ' lots created');
        $this->command->info('- Ongoing: ' . Endtime::where('status', 'Ongoing')->count());
        $this->command->info('- Submitted: ' . Endtime::where('status', 'Submitted')->count());
        $this->command->info('  - Early: ' . Endtime::where('remarks', 'Early')->count());
        $this->command->info('  - OK: ' . Endtime::where('remarks', 'OK')->count());
        $this->command->info('  - Delayed: ' . Endtime::where('remarks', 'Delayed')->count());
    }
}
