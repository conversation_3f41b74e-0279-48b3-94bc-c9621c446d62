<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Order;
use App\Models\Product;
use App\Models\User;
use App\Models\OrderItem;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use Illuminate\Http\Response;

class ReportController extends Controller
{
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('role:ADMIN')->only(['users']);
    }

    /**
     * Display the reports dashboard
     */
    public function index()
    {
        return view('reports.index');
    }

    /**
     * Sales Report
     */
    public function sales(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());
        $period = $request->get('period', 'daily');

        $salesData = $this->getSalesReport($startDate, $endDate, $period);
        $topProducts = $this->getTopSellingProducts($startDate, $endDate);
        $salesSummary = $this->getSalesSummary($startDate, $endDate);

        return view('reports.sales', compact('salesData', 'topProducts', 'salesSummary', 'startDate', 'endDate', 'period'));
    }

    /**
     * Inventory Report
     */
    public function inventory(Request $request)
    {
        $category = $request->get('category');
        $status = $request->get('status');
        $lowStock = $request->get('low_stock', false);
        
        $query = Product::with('creator');
        
        if ($category) {
            $query->where('category', $category);
        }
        
        if ($status) {
            $query->where('status', $status);
        }
        
        if ($lowStock) {
            $query->where('stock', '<', 10);
        }
        
        $products = $query->orderBy('stock', 'asc')->paginate(20);
        
        $inventorySummary = $this->getInventorySummary();
        $categories = Product::distinct()->pluck('category')->filter();
        
        return view('reports.inventory', compact('products', 'inventorySummary', 'categories', 'category', 'status', 'lowStock'));
    }

    /**
     * User Analytics Report
     */
    public function users(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());
        
        $userStats = $this->getUserAnalytics($startDate, $endDate);
        $topCustomers = $this->getTopCustomers($startDate, $endDate);
        $registrationTrend = $this->getUserRegistrationTrend();
        
        return view('reports.users', compact('userStats', 'topCustomers', 'registrationTrend', 'startDate', 'endDate'));
    }

    /**
     * Export Sales Report to CSV
     */
    public function exportSales(Request $request)
    {
        $startDate = $request->get('start_date', Carbon::now()->startOfMonth());
        $endDate = $request->get('end_date', Carbon::now()->endOfMonth());
        
        $orders = Order::with(['user', 'orderItems.product'])
            ->whereBetween('created_at', [$startDate, $endDate])
            ->where('status', '!=', 'cancelled')
            ->get();
        
        $csvData = [];
        $csvData[] = ['Order Number', 'Customer', 'Date', 'Total Amount', 'Items Count', 'Status'];
        
        foreach ($orders as $order) {
            $csvData[] = [
                $order->order_number,
                $order->user->name,
                $order->created_at->format('Y-m-d H:i:s'),
                $order->total_amount,
                $order->total_items,
                $order->status
            ];
        }
        
        $filename = 'sales_report_' . Carbon::now()->format('Y_m_d_H_i_s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        
        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }

    /**
     * Export Inventory Report to CSV
     */
    public function exportInventory()
    {
        $products = Product::with('creator')->get();
        
        $csvData = [];
        $csvData[] = ['Name', 'Category', 'Price', 'Stock', 'Status', 'Created By', 'Created Date'];
        
        foreach ($products as $product) {
            $csvData[] = [
                $product->name,
                $product->category ?? 'N/A',
                $product->price,
                $product->stock,
                $product->status,
                $product->creator->name,
                $product->created_at->format('Y-m-d H:i:s')
            ];
        }
        
        $filename = 'inventory_report_' . Carbon::now()->format('Y_m_d_H_i_s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];
        
        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };
        
        return response()->stream($callback, 200, $headers);
    }

    // Private helper methods
    private function getSalesReport($startDate, $endDate, $period)
    {
        $query = Order::whereBetween('created_at', [$startDate, $endDate])
                      ->where('status', '!=', 'cancelled');
        
        switch ($period) {
            case 'hourly':
                return $query->selectRaw('DATE_FORMAT(created_at, "%Y-%m-%d %H:00:00") as period, SUM(total_amount) as revenue, COUNT(*) as orders')
                            ->groupBy('period')
                            ->orderBy('period')
                            ->get();
            case 'daily':
                return $query->selectRaw('DATE(created_at) as period, SUM(total_amount) as revenue, COUNT(*) as orders')
                            ->groupBy('period')
                            ->orderBy('period')
                            ->get();
            case 'weekly':
                return $query->selectRaw('YEARWEEK(created_at) as period, SUM(total_amount) as revenue, COUNT(*) as orders')
                            ->groupBy('period')
                            ->orderBy('period')
                            ->get();
            case 'monthly':
                return $query->selectRaw('DATE_FORMAT(created_at, "%Y-%m") as period, SUM(total_amount) as revenue, COUNT(*) as orders')
                            ->groupBy('period')
                            ->orderBy('period')
                            ->get();
            default:
                return $query->selectRaw('DATE(created_at) as period, SUM(total_amount) as revenue, COUNT(*) as orders')
                            ->groupBy('period')
                            ->orderBy('period')
                            ->get();
        }
    }

    private function getTopSellingProducts($startDate, $endDate)
    {
        return Product::select('products.*', DB::raw('SUM(order_items.quantity) as total_sold'), DB::raw('SUM(order_items.total) as total_revenue'))
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->where('orders.status', '!=', 'cancelled')
            ->groupBy('products.id')
            ->orderBy('total_sold', 'desc')
            ->take(10)
            ->get();
    }

    private function getSalesSummary($startDate, $endDate)
    {
        $orders = Order::whereBetween('created_at', [$startDate, $endDate]);
        
        return [
            'total_revenue' => $orders->where('status', '!=', 'cancelled')->sum('total_amount'),
            'total_orders' => $orders->count(),
            'completed_orders' => $orders->whereIn('status', ['paid', 'delivered'])->count(),
            'pending_orders' => $orders->where('status', 'pending')->count(),
            'cancelled_orders' => $orders->where('status', 'cancelled')->count(),
            'average_order_value' => $orders->where('status', '!=', 'cancelled')->avg('total_amount') ?? 0,
        ];
    }

    private function getInventorySummary()
    {
        return [
            'total_products' => Product::count(),
            'active_products' => Product::where('status', 'active')->count(),
            'inactive_products' => Product::where('status', 'inactive')->count(),
            'out_of_stock' => Product::where('stock', 0)->count(),
            'low_stock' => Product::where('stock', '>', 0)->where('stock', '<', 10)->count(),
            'total_inventory_value' => Product::selectRaw('SUM(price * stock)')->value('SUM(price * stock)') ?? 0,
            'categories_count' => Product::distinct('category')->count('category'),
        ];
    }

    private function getUserAnalytics($startDate, $endDate)
    {
        $totalUsers = User::count();
        $newUsers = User::whereBetween('created_at', [$startDate, $endDate])->count();
        $activeUsers = User::whereHas('orders', function($query) use ($startDate, $endDate) {
            $query->whereBetween('created_at', [$startDate, $endDate]);
        })->count();
        
        return [
            'total_users' => $totalUsers,
            'new_users' => $newUsers,
            'active_users' => $activeUsers,
            'admin_users' => User::where('role', 'admin')->count(),
            'regular_users' => User::where('role', 'user')->count(),
        ];
    }

    private function getTopCustomers($startDate, $endDate)
    {
        return User::select('users.*', DB::raw('COUNT(orders.id) as orders_count'), DB::raw('SUM(orders.total_amount) as total_spent'))
            ->join('orders', 'users.id', '=', 'orders.user_id')
            ->whereBetween('orders.created_at', [$startDate, $endDate])
            ->where('orders.status', '!=', 'cancelled')
            ->groupBy('users.id')
            ->orderBy('total_spent', 'desc')
            ->take(10)
            ->get();
    }

    private function getUserRegistrationTrend()
    {
        return User::selectRaw('DATE(created_at) as date, COUNT(*) as registrations')
            ->where('created_at', '>=', Carbon::now()->subDays(30))
            ->groupBy('date')
            ->orderBy('date')
            ->get();
    }
}
