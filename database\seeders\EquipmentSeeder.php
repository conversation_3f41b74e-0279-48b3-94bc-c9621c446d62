<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Equipment;

class EquipmentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $path = base_path('equipment.csv');
        if (!file_exists($path)) {
            $this->command->error("equipment.csv not found at {$path}");
            return;
        }

        // Read file content and normalize line endings
        $content = file_get_contents($path);
        if ($content === false) {
            $this->command->error('Unable to read equipment.csv');
            return;
        }

        // Replace various line endings with standard \n
        $content = str_replace(["\r\n", "\r"], "\n", $content);
        $lines = explode("\n", $content);
        $lines = array_filter($lines, function($line) {
            return trim($line) !== '';
        });

        if (empty($lines)) {
            $this->command->error('equipment.csv appears to be empty after processing.');
            return;
        }

        // Parse header from first line
        $headerLine = array_shift($lines);
        $header = str_getcsv($headerLine, ',');
        
        // Normalize header keys (trim BOM/spaces)
        $header = array_map(function ($h) {
            return trim(preg_replace('/^\xEF\xBB\xBF/', '', (string) $h));
        }, $header);

        $count = 0;
        foreach ($lines as $lineNum => $line) {
            $line = trim($line);
            if (empty($line)) {
                continue;
            }
            
            // Parse CSV data from line
            $row = str_getcsv($line, ',');
            
            // Map row to associative array by header
            $data = [];
            foreach ($header as $idx => $key) {
                $val = $row[$idx] ?? null;
                // Clean up values: trim spaces and surrounding quotes
                if (is_string($val)) {
                    $val = trim($val);
                    // Remove surrounding quotes if present
                    if (strlen($val) >= 2 && $val[0] === '"' && $val[-1] === '"') {
                        $val = substr($val, 1, -1);
                    }
                }
                $data[$key] = $val === '' ? null : $val;
            }

            // Helper function to clean numeric values that might still have commas
            $cleanNumeric = function($value) {
                if ($value === null || $value === '') {
                    return null;
                }
                // Remove commas and quotes, then trim
                $cleaned = str_replace([',', '"', "'"], '', trim((string)$value));
                return $cleaned === '' ? null : $cleaned;
            };

            // Build payload matching our columns
            $payload = [
                'eqp_no'         => $data['eqp_no'] ?? null,
                'eqp_line'       => $data['eqp_line'] ?? null,
                'eqp_area'       => $data['eqp_area'] ?? null,
                'eqp_type'       => $data['eqp_type'] ?? null,
                'eqp_class'      => $data['eqp_class'] ?? null,
                'eqp_maker'      => $data['eqp_maker'] ?? null,
                'feeder_type'    => $data['feeder_type'] ?? null,
                'lot_size'       => isset($data['lot_size']) ? trim((string)$data['lot_size']) : null,
                'work_type'      => $data['work_type'] ?? null,
                'lot_type'       => $data['lot_type'] ?? null,
                'lotqty_alloc'   => $data['lotqty_alloc'] ?? null,
                'eqp_oee'        => $data['eqp_oee'] ?? null,
                'eqp_speed'      => $data['eqp_speed'] ?? null,
                'operation_time' => $cleanNumeric($data['operation_time'] ?? null),
                'eqp_code'       => isset($data['eqp_code']) ? trim((string)$data['eqp_code']) : null,
                'modified_by'    => $data['modified_by'] ?? null,
                'ongoing_lot'    => $data['ongoing_lot'] ?? null,
            ];

            // Skip if no eqp_no
            if (empty($payload['eqp_no'])) {
                continue;
            }

            Equipment::updateOrCreate(
                ['eqp_no' => $payload['eqp_no']],
                $payload
            );
            $count++;
        }

        $this->command->info("Successfully seeded {$count} equipment rows from equipment.csv");
    }
}
