<x-app-layout>
    <x-slot name="header">
        Update Lot Request
    </x-slot>

    <style>
        /* Clean page background */
        .edit-lot-request {
            background: #f8f9ff;
            min-height: 100vh;
        }
        
        /* Enhanced form card */
        .form-card {
            background: white;
            border: 1px solid #e1e8ff;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            transition: all 0.2s ease;
            overflow: hidden;
        }
        
        .form-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
        }
        
        /* Card header */
        .form-card .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2) !important;
            color: white !important;
            border: none !important;
            padding: 1rem 1.5rem;
        }
        
        .form-card .card-header h6 {
            margin: 0;
            font-weight: 600;
        }
        
        /* Form styling */
        .form-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .form-control, .form-select {
            border: 1px solid #e1e8ff;
            border-radius: 8px;
            padding: 0.75rem;
            transition: all 0.2s ease;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        
        /* Equipment items inline display */
        .equipment-display {
            background: #f8f9ff;
            border: 1px solid #e1e8ff;
            border-radius: 8px;
            padding: 1rem;
        }
        
        .equipment-inline {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            align-items: center;
        }
        
        .equipment-tag {
            background: #667eea;
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            display: inline-flex;
            align-items: center;
            gap: 0.3rem;
        }
        
        .equipment-tag .quantity {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.2rem 0.5rem;
            border-radius: 10px;
            font-size: 0.75rem;
        }
        
        /* Progress Status with Radio Buttons */
        .progress-status-container {
            background: white;
            border: 1px solid #e1e8ff;
            border-radius: 12px;
            padding: 1.5rem;
            position: relative;
        }
        
        .progress-bar-container {
            height: 10px;
            background-color: #e9ecef;
            border-radius: 5px;
            margin: 1.5rem 0;
            position: relative;
        }
        
        .progress-bar {
            height: 100%;
            background: linear-gradient(to right, #ffc107, #17a2b8, #28a745);
            border-radius: 5px;
            transition: width 0.4s ease;
        }
        
        .status-steps {
            display: flex;
            justify-content: space-between;
            margin-bottom: 0.5rem;
            position: relative;
        }
        
        .status-step {
            position: relative;
            flex: 1;
            text-align: center;
        }
        
        .status-marker {
            width: 25px;
            height: 25px;
            border-radius: 50%;
            position: absolute;
            top: -32px; /* Position above the progress bar */
            left: 50%;
            transform: translateX(-50%);
            background-color: #e9ecef;
            border: 2px solid white;
            z-index: 2;
            box-shadow: 0 0 0 2px #e9ecef;
        }
        
        .status-marker.active {
            transform: translateX(-50%) scale(1.2);
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.5);
        }
        
        /* Status marker colors */
        .status-marker-pending {
            background-color: #ffc107;
        }
        
        .status-marker-in_process {
            background-color: #17a2b8;
        }
        
        .status-marker-completed {
            background-color: #28a745;
        }
        
        .status-marker-cancelled {
            background-color: #dc3545;
            top: -32px;
            right: 0;
            left: auto;
            transform: none;
        }
        
        .status-label {
            margin-top: 20px;
            font-size: 0.875rem;
            font-weight: 500;
            color: #495057;
        }
        
        .status-label.active {
            font-weight: 700;
            color: #000;
        }
        
        .status-radio-container {
            display: flex;
            flex-wrap: wrap;
            gap: 0.5rem;
            margin-top: 2rem;
        }
        
        .status-radio-option {
            flex: 1;
            min-width: calc(25% - 0.5rem);
        }
        
        .status-radio {
            display: none;
        }
        
        .status-radio-label {
            display: flex;
            align-items: center;
            padding: 0.75rem 1rem;
            border: 1px solid #e1e8ff;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #f8f9ff;
        }
        
        .status-radio-label:hover {
            border-color: #667eea;
            background: #fff;
        }
        
        .status-radio:checked + .status-radio-label {
            background: #667eea;
            color: white;
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }
        
        .status-radio:disabled + .status-radio-label {
            opacity: 0.6;
            cursor: not-allowed;
            background: #f5f5f5;
            border-color: #ced4da;
        }
        
        .status-icon {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 0.75rem;
            color: white;
            font-weight: bold;
            font-size: 0.75rem;
        }
        
        .status-icon-pending { background-color: #ffc107; }
        .status-icon-in_process { background-color: #17a2b8; }
        .status-icon-completed { background-color: #28a745; }
        .status-icon-cancelled { background-color: #dc3545; }
        
        /* Enhanced WIP Section Styling */
        .wip-section {
            background: white;
            border: 1px solid #e1e8ff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
        }
        
        .wip-section-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem 1.5rem;
            border-bottom: none;
        }
        
        .wip-section-title {
            font-size: 1.1rem;
            font-weight: 600;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .wip-section-subtitle {
            font-size: 0.875rem;
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
            display: flex;
            align-items: center;
            gap: 0.75rem;
            flex-wrap: wrap;
        }
        
        .filter-badge {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 500;
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        
        .wip-table-container {
            background: white;
            padding: 0;
        }
        
        /* Enhanced WIP Table Styling */
        .wip-table {
            margin: 0;
            border: none;
        }
        
        .wip-table thead th {
            background: #f8f9fa;
            color: #495057;
            border: none;
            padding: 1rem 0.75rem;
            font-weight: 600;
            font-size: 0.875rem;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            border-bottom: 2px solid #e9ecef;
        }
        
        .wip-table tbody td {
            padding: 1rem 0.75rem;
            border-top: 1px solid #f1f3f5;
            vertical-align: middle;
            font-size: 0.9rem;
        }
        
        .wip-table tbody tr {
            transition: all 0.2s ease;
        }
        
        .wip-table tbody tr:hover {
            background: linear-gradient(135deg, #f8f9ff 0%, #fff 100%);
            transform: translateX(2px);
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
        }
        
        /* Enhanced lot code styling */
        .lot-code-display {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-weight: 600;
            font-size: 0.85rem;
            letter-spacing: 0.5px;
            border: none;
        }
        
        /* Enhanced badges */
        .count-badge {
            background: #667eea;
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .quantity-badge {
            background: #28a745;
            color: white;
            padding: 0.4rem 0.8rem;
            border-radius: 8px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        /* Enhanced View Details button */
        .view-details-btn {
            background: linear-gradient(135deg, #17a2b8, #20c997);
            border: none;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
            font-size: 0.875rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(23, 162, 184, 0.2);
        }
        
        .view-details-btn:hover {
            background: linear-gradient(135deg, #138496, #1abc9c);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(23, 162, 184, 0.3);
            color: white;
        }
        
        .btn-info {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: white;
        }
        
        .btn-info:hover {
            background-color: #138496;
            border-color: #117a8b;
        }
        
        /* WIP Details Modal */
        .modal-header {
            background: #495057;
            color: white;
            border-bottom: none;
        }
        
        .modal-content {
            border: none;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .modal-xl {
            max-width: 1400px;
            width: 95vw;
        }
        
        /* Fixed modal positioning */
        .modal.show .modal-dialog {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) !important;
            margin: 0 !important;
            height: 90vh;
            max-height: 90vh;
        }
        
        .modal-content {
            height: 100%;
            display: flex;
            flex-direction: column;
            max-height: 90vh;
            overflow: hidden;
        }
        
        .modal-header {
            flex-shrink: 0;
            position: sticky;
            top: 0;
            z-index: 1020;
            background: #495057;
            border-bottom: none;
        }
        
        .modal-footer {
            flex-shrink: 0;
            position: sticky;
            bottom: 0;
            z-index: 1020;
            background: white;
            border-top: 1px solid #dee2e6;
        }
        
        .modal-body {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            padding: 1rem;
            min-height: 0;
        }
        
        /* Group Summary Header */
        .group-summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 1rem;
            border-radius: 8px;
            margin-bottom: 1rem;
        }
        
        .group-summary h6 {
            margin: 0 0 0.5rem 0;
            font-weight: 600;
        }
        
        .summary-stats {
            display: flex;
            gap: 1rem;
            flex-wrap: wrap;
        }
        
        .summary-stat {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        
        .summary-badges {
            display: flex;
            gap: 0.5rem;
            margin-top: 0.5rem;
            flex-wrap: wrap;
        }
        
        .summary-badge {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
            padding: 0.25rem 0.75rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        /* Modal Table Styling */
        .modal-wip-table {
            margin: 0;
        }
        
        .modal-wip-table thead th {
            background: #f8f9fa;
            color: #495057;
            font-weight: 600;
            padding: 0.75rem 0.5rem;
            border: none;
            font-size: 0.875rem;
            text-align: center;
        }
        
        .modal-wip-table tbody td {
            padding: 0.75rem 0.5rem;
            border-top: 1px solid #dee2e6;
            vertical-align: middle;
            font-size: 0.875rem;
            text-align: center;
        }
        
        .modal-wip-table tbody tr:hover {
            background: #f8f9ff;
        }
        
        /* Auto/Lipas badges */
        .auto-badge {
            background: #28a745;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        .lipas-badge {
            background: #fd7e14;
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-size: 0.75rem;
            font-weight: 600;
        }
        
        /* Print button styling */
        .print-btn {
            background: white;
            color: #28a745;
            border: 1px solid #28a745;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
        }
        
        .print-btn:hover {
            background: #28a745;
            color: white;
        }
        
        /* Enhanced buttons */
        .btn-primary {
            background: #667eea;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        
        .btn-primary:hover {
            background: #5a67d8;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }
        
        .btn-outline-secondary {
            border-color: #6c757d;
            color: #6c757d;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        
        .btn-outline-secondary:hover {
            background: #6c757d;
            border-color: #6c757d;
            transform: translateY(-1px);
        }
        
        /* Badge styling */
        .badge {
            font-weight: 500;
            padding: 0.4rem 0.8rem;
            border-radius: 6px;
        }
        
        /* Fade-in animation */
        .fade-in {
            animation: fadeIn 0.4s ease-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responsive design */
        @media (max-width: 768px) {
            .form-card {
                margin: 0 15px;
            }
            
            .d-flex.gap-2 {
                flex-direction: column;
            }
            
            .btn {
                width: 100%;
            }
            
            .status-progress {
                flex-direction: column;
                gap: 1rem;
            }
            
            .status-item {
                width: 100%;
            }
            
            .status-line {
                display: none;
            }
        }
    </style>

    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h4 class="mb-1">Update Lot Request #{{ $lotRequest->request_number }}</h4>
                    <p class="text-muted mb-0">Update lot request information</p>
                </div>
                <a href="{{ route('lot-requests.index', $lotRequest) }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Lot Request
                </a>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-10 mx-auto">
            <div class="form-card fade-in">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-edit me-2"></i>Lot Request Information</h6>
                </div>
                <div class="card-body">
                    <form action="{{ route('lot-requests.update', $lotRequest) }}" method="POST">
                        @csrf
                        @method('PUT')

                        <!-- 2-Column Layout: Request Number and Requestor -->
                        <div class="row mb-4">
                            <div class="col-md-6">
                                <label class="form-label">Request Number</label>
                                <input type="text" class="form-control" value="{{ $lotRequest->request_number }}" readonly>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Requestor</label>
                                <input type="text" class="form-control" 
                                       value="{{ $lotRequest->user->emp_name }} ({{ $lotRequest->user->emp_no }})" readonly>
                            </div>
                        </div>

                        <!-- Equipment Items (Single Line Display) -->
                        <div class="mb-4">
                            <label class="form-label">Equipment Items</label>
                            <div class="equipment-display">
                                <div class="equipment-inline">
                                    @foreach($lotRequest->lotRequestItems as $item)
                                        <div class="equipment-tag">
                                            {{ $item->equipment_number }}
                                            <span class="badge bg-light text-dark ms-1">{{ $item->equipment_code }}</span>
                                            @php
                                                $quantityDisplay = $lotRequest->getQuantityDisplayForItem($item);
                                            @endphp
                                            <span class="quantity">
                                                @if($quantityDisplay === 'shared')
                                                    shared ({{ $item->quantity }})
                                                @else
                                                    {{ $item->quantity }} lots
                                                @endif
                                            </span>
                                        </div>
                                    @endforeach
                                </div>
                                <div class="d-flex justify-content-between align-items-center mt-3 pt-3 border-top">
                                    <span class="fw-bold">Sub-total:</span>
                                    <span class="fw-bold text-primary">{{ $lotRequest->total_quantity }} lots</span>
                                </div>
                            </div>
                        </div>

                        <!-- Progress Status with Radio Buttons and Progress Bar -->
                        @if(Auth::user()->canManageOrders())
                            <div class="mb-4">
                                <label class="form-label">Status</label>
                                <div class="progress-status-container">
                                    <!-- Status Progress Bar -->
                                    <div class="status-steps">
                                        <div class="status-step" style="flex: 3">
                                            <div class="status-marker status-marker-pending {{ $lotRequest->status === 'pending' ? 'active' : '' }}"></div>
                                            <div class="status-label {{ $lotRequest->status === 'pending' ? 'active' : '' }}">Pending</div>
                                        </div>
                                        <div class="status-step" style="flex: 3">
                                            <div class="status-marker status-marker-in_process {{ $lotRequest->status === 'in_process' ? 'active' : '' }}"></div>
                                            <div class="status-label {{ $lotRequest->status === 'in_process' ? 'active' : '' }}">Getting Lot</div>
                                        </div>
                                        <div class="status-step" style="flex: 3">
                                            <div class="status-marker status-marker-completed {{ $lotRequest->status === 'completed' ? 'active' : '' }}"></div>
                                            <div class="status-label {{ $lotRequest->status === 'completed' ? 'active' : '' }}">Completed</div>
                                        </div>
                                        <div class="status-step" style="flex: 1">
                                            <div class="status-marker status-marker-cancelled {{ $lotRequest->status === 'cancelled' ? 'active' : '' }}"></div>
                                            <div class="status-label {{ $lotRequest->status === 'cancelled' ? 'active' : '' }}">Cancelled</div>
                                        </div>
                                    </div>
                                    
                                    <div class="progress-bar-container">
                                        <div class="progress-bar" id="statusProgressBar" style="width: {{ $lotRequest->status === 'pending' ? '0%' : ($lotRequest->status === 'in_process' ? '50%' : ($lotRequest->status === 'completed' ? '100%' : '0%')) }}"></div>
                                    </div>
                                    
                                    <!-- Status Radio Buttons -->
                                    <div class="status-radio-container">
                                        <!-- Pending Option -->
                                        <div class="status-radio-option">
                                            <input type="radio" class="status-radio" name="status_radio" id="status_pending" value="pending" 
                                                {{ $lotRequest->status === 'pending' ? 'checked' : '' }}>
                                            <label for="status_pending" class="status-radio-label">
                                                <div class="status-icon status-icon-pending">1</div>
                                                <span>Pending</span>
                                            </label>
                                        </div>
                                        
                                        <!-- Getting Lot Option -->
                                        <div class="status-radio-option">
                                            <input type="radio" class="status-radio" name="status_radio" id="status_in_process" value="in_process" 
                                                {{ $lotRequest->status === 'in_process' ? 'checked' : '' }}
                                                {{ $lotRequest->status === 'completed' ? 'disabled' : '' }}>
                                            <label for="status_in_process" class="status-radio-label">
                                                <div class="status-icon status-icon-in_process">2</div>
                                                <span>Getting Lot</span>
                                            </label>
                                        </div>
                                        
                                        <!-- Completed Option -->
                                        <div class="status-radio-option">
                                            <input type="radio" class="status-radio" name="status_radio" id="status_completed" value="completed" 
                                                {{ $lotRequest->status === 'completed' ? 'checked' : '' }}
                                                {{ $lotRequest->status === 'pending' ? 'disabled' : '' }}>
                                            <label for="status_completed" class="status-radio-label">
                                                <div class="status-icon status-icon-completed">3</div>
                                                <span>Completed</span>
                                            </label>
                                        </div>
                                        
                                        <!-- Cancelled Option (always available) -->
                                        <div class="status-radio-option">
                                            <input type="radio" class="status-radio" name="status_radio" id="status_cancelled" value="cancelled" 
                                                {{ $lotRequest->status === 'cancelled' ? 'checked' : '' }}>
                                            <label for="status_cancelled" class="status-radio-label">
                                                <div class="status-icon status-icon-cancelled">X</div>
                                                <span>Cancelled</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Hidden input for form submission -->
                                <input type="hidden" id="status" name="status" value="{{ $lotRequest->status }}">
                                @error('status')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                            </div>
                        @else
                            <div class="mb-4">
                                <label class="form-label">Status</label>
                                <div class="progress-status-container">
                                    <!-- Status Progress Bar (simplified for regular users) -->
                                    <div class="status-steps">
                                        <div class="status-step" style="flex: 1">
                                            <div class="status-marker status-marker-pending {{ $lotRequest->status === 'pending' ? 'active' : '' }}"></div>
                                            <div class="status-label {{ $lotRequest->status === 'pending' ? 'active' : '' }}">Pending</div>
                                        </div>
                                        <div class="status-step" style="flex: 1">
                                            <div class="status-marker status-marker-cancelled {{ $lotRequest->status === 'cancelled' ? 'active' : '' }}"></div>
                                            <div class="status-label {{ $lotRequest->status === 'cancelled' ? 'active' : '' }}">Cancelled</div>
                                        </div>
                                    </div>
                                    
                                    <div class="progress-bar-container">
                                        <!-- No progress bar for regular users -->
                                    </div>
                                    
                                    <!-- Status Radio Buttons (limited options) -->
                                    <div class="status-radio-container">
                                        <!-- Pending Option -->
                                        <div class="status-radio-option">
                                            <input type="radio" class="status-radio" name="status_radio" id="status_pending" value="pending" 
                                                {{ $lotRequest->status === 'pending' ? 'checked' : '' }}>
                                            <label for="status_pending" class="status-radio-label">
                                                <div class="status-icon status-icon-pending">1</div>
                                                <span>Pending</span>
                                            </label>
                                        </div>
                                        
                                        <!-- Cancelled Option -->
                                        <div class="status-radio-option">
                                            <input type="radio" class="status-radio" name="status_radio" id="status_cancelled" value="cancelled" 
                                                {{ $lotRequest->status === 'cancelled' ? 'checked' : '' }}>
                                            <label for="status_cancelled" class="status-radio-label">
                                                <div class="status-icon status-icon-cancelled">X</div>
                                                <span>Cancelled</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- Hidden input for form submission -->
                                <input type="hidden" id="status" name="status" value="{{ $lotRequest->status }}">
                                @error('status')
                                    <div class="text-danger mt-2">{{ $message }}</div>
                                @enderror
                                <div class="form-text mt-2">As a regular user, you can only set status to pending or cancelled</div>
                            </div>
                        @endif

                        <!-- Enhanced Available WIP Section -->
                        <div class="mb-4">
                            @if($availableWip->count() > 0)
                                @php
                                    $wipSummary = $availableWip->groupBy(function($item) {
                                        return $item->lot_code;
                                    })->map(function($group, $key) {
                                        return (object) [
                                            'lot_code' => $key,
                                            'lot_count' => $group->count(),
                                            'total_quantity' => $group->sum('lot_qty'),
                                            'average_tat' => $group->avg('stagnant_tat'),
                                            'lots' => $group
                                        ];
                                    });
                                @endphp
                                
                                <div class="wip-section">
                                    <div class="wip-section-header">
                                        <h6 class="wip-section-title">
                                            <i class="fas fa-microchip"></i>
                                            Available WIP (Matching Equipment Codes)
                                        </h6>
                                        <div class="wip-section-subtitle">
                                            <span><i class="fas fa-filter"></i> Filtered by WIP Status:</span>
                                            <span class="filter-badge">Newlot Standby</span>
                                            <span class="filter-badge">Rework Lot Standby</span>
                                        </div>
                                    </div>
                                    
                                    <div class="wip-table-container">
                                        <div class="table-responsive">
                                            <table class="table wip-table">
                                                <thead>
                                                    <tr>
                                                        <th><i class="fas fa-code me-2"></i>Lot Code</th>
                                                        <th><i class="fas fa-layer-group me-2"></i>Lot Count</th>
                                                        <th><i class="fas fa-boxes me-2"></i>Total Quantity</th>
                                                        <th><i class="fas fa-stopwatch me-2"></i>Average TAT</th>
                                                        <th><i class="fas fa-eye me-2"></i>Actions</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    @foreach($wipSummary as $index => $summary)
                                                        <tr>
                                                            <td>
                                                                <span class="lot-code-display">{{ $summary->lot_code }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="count-badge">{{ number_format($summary->lot_count) }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="quantity-badge">{{ number_format($summary->total_quantity) }}</span>
                                                            </td>
                                                            <td>
                                                                <span class="badge {{ $summary->average_tat > 7 ? 'bg-danger' : ($summary->average_tat > 3 ? 'bg-warning text-dark' : 'bg-success') }}">
                                                                    <i class="fas fa-clock me-1"></i>{{ number_format($summary->average_tat, 1) }}d
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <button type="button" 
                                                                        class="view-details-btn" 
                                                                        onclick="showWipDetails('{{ $summary->lot_code }}')">
                                                                    <i class="fas fa-search-plus me-1"></i>View Lot List
                                                                </button>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            @else
                                <div class="wip-section">
                                    <div class="wip-section-header">
                                        <h6 class="wip-section-title">
                                            <i class="fas fa-microchip"></i>
                                            Available WIP (Matching Equipment Codes)
                                        </h6>
                                        <div class="wip-section-subtitle">
                                            <span><i class="fas fa-filter"></i> Filtered by WIP Status:</span>
                                            <span class="filter-badge">Newlot Standby</span>
                                            <span class="filter-badge">Rework Lot Standby</span>
                                        </div>
                                    </div>
                                    
                                    <div class="text-center py-5" style="background: white; padding: 3rem 1.5rem;">
                                        <div class="mb-3">
                                            <i class="fas fa-search-minus" style="font-size: 3rem; color: #dee2e6;"></i>
                                        </div>
                                        <h6 class="mb-2 text-muted">No Available WIP Found</h6>
                                        <p class="text-muted mb-3">No WIP data with the specified status matches the equipment codes in this lot request.</p>
                                        <div class="d-flex justify-content-center gap-2 flex-wrap">
                                            <small class="text-muted">Equipment codes:</small>
                                            @foreach($lotRequest->lotRequestItems->pluck('equipment_code')->unique()->filter() as $code)
                                                <span class="badge bg-light text-dark">{{ $code }}</span>
                                            @endforeach
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Submit Buttons -->
                        <div class="d-flex justify-content-end gap-2">
                            <a href="{{ route('lot-requests.show', $lotRequest) }}" class="btn btn-outline-secondary">Cancel</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Lot Request
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- WIP Details Modal -->
    <div class="modal fade" id="wipDetailsModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>WIP Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="wipModalBody">
                    <!-- Content populated by JavaScript -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Store WIP data for modal
        const wipData = @json($availableWip->groupBy('lot_code'));
        
        function showWipDetails(lotCode) {
            console.log('Showing details for lot code:', lotCode);
            
            const lots = wipData[lotCode] || [];
            if (lots.length === 0) {
                alert('No details available for this lot code.');
                return;
            }
            
            // Calculate summary statistics
            const totalLots = lots.length;
            const totalUnits = lots.reduce((sum, lot) => sum + parseInt(lot.lot_qty || 0), 0);
            const avgTat = lots.reduce((sum, lot) => sum + parseFloat(lot.stagnant_tat || 0), 0) / lots.length;
            
            // Get unique values for badges
            const firstLot = lots[0];
            const eqpClass = firstLot.eqp_class || '';
            const eqpType = firstLot.eqp_type || '';
            const workType = firstLot.work_type || '';
            const lotSize = firstLot.lot_size || '';
            
            // Build modal content exactly like products index
            let modalContent = `
                <div class="row mb-3">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center p-3 rounded" 
                             style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                            <div>
                                <h6 class="mb-1 text-white">Group Summary</h6>
                                <div class="d-flex gap-3">
                                    <span class="badge bg-white text-primary">${totalLots} Lots</span>
                                    <span class="badge bg-white text-success">${totalUnits.toLocaleString()} Units</span>
                                    <span class="badge bg-white text-danger">${avgTat.toFixed(2)} Avg TAT</span>
                                </div>
                            </div>
                            <div class="text-end">
                                <div class="text-white small">
                                    <div><span class="badge bg-info">${lotSize}</span> <code style="background: rgba(255,255,255,0.2); color: white;">${lotCode}</code></div>
                                    <div class="mt-1">
                                        <span class="badge bg-secondary">${eqpClass}</span>
                                        <span class="badge bg-warning text-dark">${eqpType}</span>
                                        <span class="badge bg-success">${workType}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="table-responsive">
                    <table class="table table-hover table-sm" id="lotDetailsTable">
                        <thead class="table-dark">
                            <tr>
                                <th width="60"></i>No.</th>
                                <th style="cursor: pointer;" onclick="sortTable(1)"></i>Lot No <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(2)"></i>Model <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(3)"></i>Quantity <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(4)"></i>Stagnation <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(5)"></i>Location <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(6)"></i>Automotive <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(7)"></i>Lipas <i class="fas fa-sort text-muted"></i></th>
                                <th style="cursor: pointer;" onclick="sortTable(8)"></i>WIP Status <i class="fas fa-sort text-muted"></i></th>
                                <th width="80"></i>Assign</th>
                                <th width="80"><i class="fas fa-print me-1"></i>Print</th>
                            </tr>
                        </thead>
                        <tbody>
            `;
            
            lots.forEach((lot, index) => {
                const rowClass = index % 2 === 0 ? '' : 'table-light';
                const tatClass = parseFloat(lot.stagnant_tat) > 30 ? 'text-danger' : (parseFloat(lot.stagnant_tat) > 15 ? 'text-warning' : 'text-success');
                const rowNumber = index + 1;
                
                modalContent += `
                    <tr class="${rowClass}">
                        <td class="text-center"><span class="badge bg-light text-dark">${rowNumber}</span></td>
                        <td data-sort="${lot.lot_id}"><strong>${lot.lot_id}</strong></td>
                        <td data-sort="${lot.model_15}"><code class="text-primary">${lot.model_15 || 'N/A'}</code></td>
                        <td data-sort="${parseInt(lot.lot_qty)}"><span class="badge bg-info">${parseInt(lot.lot_qty || 0).toLocaleString()}</span></td>
                        <td data-sort="${parseFloat(lot.stagnant_tat)}"><span class="badge ${tatClass === 'text-danger' ? 'bg-danger' : (tatClass === 'text-warning' ? 'bg-warning text-dark' : 'bg-success')}">${parseFloat(lot.stagnant_tat || 0).toFixed(2)} days</span></td>
                        <td data-sort="${lot.lot_location}"><span class="badge bg-secondary">${lot.lot_location || 'N/A'}</span></td>
                        <td data-sort="${lot.auto_yn}" class="text-center">
                            ${lot.auto_yn === 'Y' ? '<span class="badge bg-success">Yes</span>' : (lot.auto_yn === 'N' ? '<span class="badge bg-danger">No</span>' : '<span class="badge bg-light text-muted">-</span>')}
                        </td>
                        <td data-sort="${lot.lipas_yn}" class="text-center">
                            ${lot.lipas_yn === 'Y' ? '<span class="badge bg-warning text-dark">Yes</span>' : (lot.lipas_yn === 'N' ? '<span class="badge bg-danger">No</span>' : '<span class="badge bg-light text-muted">-</span>')}
                        </td>
                        <td data-sort="${lot.wip_status}" class="text-center">
                            <span class="badge ${lot.wip_status === 'Newlot Standby' ? 'bg-primary' : 'bg-info'}">${lot.wip_status || 'N/A'}</span>
                        </td>
                        <td class="text-center">
                            <button class="btn btn-sm btn-primary" 
                                    title="Assign this lot to the requesting equipment"
                                    onclick="assignLot('${lot.lot_id}', '${lotCode}')">
                                <i class="fas fa-check"></i>
                            </button>
                        </td>
                        <td class="text-center">
                            <button class="btn btn-sm btn-outline-success" 
                                    title="Print Lot Details"
                                    onclick="printLotDetails('${lot.lot_id}', '${lot.model_15}', '${lot.lot_qty}', '${lot.stagnant_tat}', '${lot.lot_location}', '${lot.auto_yn}', '${lot.lipas_yn}')">
                                <i class="fas fa-print"></i>
                            </button>
                        </td>
                    </tr>
                `;
            });
            
            modalContent += `
                        </tbody>
                    </table>
                </div>
                
                ${lots.length === 0 ? `
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-2x text-muted mb-2"></i>
                        <p class="text-muted">No lots found for this group.</p>
                    </div>
                ` : `
                    <div class="mt-3 p-3 bg-light rounded">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            Showing ${lots.length} individual lot(s) for this group.
                        </small>
                    </div>
                `}
            `;
            
            // Update modal body
            document.getElementById('wipModalBody').innerHTML = modalContent;
            
            // Show modal
            const modal = new bootstrap.Modal(document.getElementById('wipDetailsModal'));
            modal.show();
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            let currentStatus = '{{ $lotRequest->status }}';
            const statusRadios = document.querySelectorAll('.status-radio');
            const hiddenStatusInput = document.getElementById('status');
            const progressBar = document.getElementById('statusProgressBar');
            
            // Set up radio button change event listeners
            statusRadios.forEach(radio => {
                radio.addEventListener('change', function() {
                    // Update hidden input for form submission
                    hiddenStatusInput.value = this.value;
                    currentStatus = this.value;
                    
                    // Update visual elements
                    updateProgressDisplay();
                    
                    // Disable previous steps based on current selection
                    updateRadioAvailability();
                });
            });
            
            function updateProgressDisplay() {
                // Reset all markers and labels
                document.querySelectorAll('.status-marker').forEach(marker => {
                    marker.classList.remove('active');
                });
                
                document.querySelectorAll('.status-label').forEach(label => {
                    label.classList.remove('active');
                });
                
                // Activate current status marker and label
                const activeMarker = document.querySelector(`.status-marker-${currentStatus}`);
                const activeLabel = activeMarker.nextElementSibling;
                
                if (activeMarker) {
                    activeMarker.classList.add('active');
                    activeLabel.classList.add('active');
                    
                    // Update progress bar width based on status
                    if (progressBar) {
                        if (currentStatus === 'pending') {
                            progressBar.style.width = '0%';
                        } else if (currentStatus === 'in_process') {
                            progressBar.style.width = '50%';
                        } else if (currentStatus === 'completed') {
                            progressBar.style.width = '100%';
                        } else if (currentStatus === 'cancelled') {
                            progressBar.style.width = '0%';
                        }
                    }
                }
            }
            
            function updateRadioAvailability() {
                // Get radio elements
                const pendingRadio = document.getElementById('status_pending');
                const inProcessRadio = document.getElementById('status_in_process');
                const completedRadio = document.getElementById('status_completed');
                
                if (!pendingRadio || !inProcessRadio || !completedRadio) return;
                
                // Enforce forward-only progression
                if (currentStatus === 'in_process') {
                    // Can't go back to pending
                    pendingRadio.disabled = true;
                    completedRadio.disabled = false;
                } else if (currentStatus === 'completed') {
                    // Can't go back to pending or in_process
                    pendingRadio.disabled = true;
                    inProcessRadio.disabled = true;
                } else if (currentStatus === 'pending') {
                    // Can't jump to completed
                    pendingRadio.disabled = false;
                    inProcessRadio.disabled = false;
                    completedRadio.disabled = true;
                } else if (currentStatus === 'cancelled') {
                    // All options available when cancelled
                    pendingRadio.disabled = false;
                    inProcessRadio.disabled = false;
                    completedRadio.disabled = false;
                }
            }
            
            // Initial setup
            updateProgressDisplay();
            updateRadioAvailability();
        });
        
        // Modal table sorting variables
        let currentSortColumn = -1;
        let sortDirection = 'asc';
        
        function sortTable(columnIndex) {
            const table = document.getElementById('lotDetailsTable');
            if (!table) return;
            
            const tbody = table.getElementsByTagName('tbody')[0];
            const rows = Array.from(tbody.getElementsByTagName('tr'));
            
            // Determine sort direction
            if (currentSortColumn === columnIndex) {
                sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                sortDirection = 'asc';
                currentSortColumn = columnIndex;
            }
            
            // Update header icons
            const headers = table.querySelectorAll('thead th');
            headers.forEach((header, index) => {
                const sortIcon = header.querySelector('i.fa-sort, i.fa-sort-up, i.fa-sort-down');
                if (sortIcon && index !== 0 && index !== 9 && index !== 10) { // Skip #, Assign, and Print columns
                    if (index === columnIndex) {
                        sortIcon.className = sortDirection === 'asc' ? 'fas fa-sort-up text-warning' : 'fas fa-sort-down text-warning';
                    } else {
                        sortIcon.className = 'fas fa-sort text-muted';
                    }
                }
            });
            
            // Sort rows
            rows.sort((a, b) => {
                const cellA = a.children[columnIndex];
                const cellB = b.children[columnIndex];
                
                let valueA, valueB;
                
                // Get sort value from data-sort attribute or text content
                if (cellA.hasAttribute('data-sort')) {
                    valueA = cellA.getAttribute('data-sort');
                    valueB = cellB.getAttribute('data-sort');
                    
                    // Handle numeric values
                    if (columnIndex === 3 || columnIndex === 4) { // Quantity or Stagnation columns
                        valueA = parseFloat(valueA) || 0;
                        valueB = parseFloat(valueB) || 0;
                    }
                } else {
                    valueA = cellA.textContent.trim();
                    valueB = cellB.textContent.trim();
                }
                
                let comparison = 0;
                if (typeof valueA === 'number' && typeof valueB === 'number') {
                    comparison = valueA - valueB;
                } else {
                    comparison = valueA.toString().localeCompare(valueB.toString());
                }
                
                return sortDirection === 'asc' ? comparison : -comparison;
            });
            
            // Re-append sorted rows
            rows.forEach(row => tbody.appendChild(row));
            
            // Update row numbers
            rows.forEach((row, index) => {
                const numberCell = row.children[0];
                numberCell.innerHTML = `<span class="badge bg-light text-dark">${index + 1}</span>`;
                
                // Update alternating row classes
                row.className = index % 2 === 0 ? '' : 'table-light';
            });
        }
        
        function assignLot(lotId, lotCode) {
            // Get the current lot request ID
            const lotRequestId = {{ $lotRequest->id }};
            
            // Get the equipment items from the lot request for confirmation
            const equipmentItems = @json($lotRequest->lotRequestItems->map(function($item) {
                return $item->equipment_number . ' (' . $item->equipment_code . ') - ' . $item->quantity . ' lots';
            }));
            
            // Show confirmation dialog
            const confirmMessage = `Are you sure you want to assign lot "${lotId}" to this lot request?\n\nThis will assign the lot to the following equipment:\n${equipmentItems.join('\n')}`;
            
            if (!confirm(confirmMessage)) {
                return;
            }
            
            // Disable the assign button to prevent double-clicking
            const assignButton = event.target.closest('button');
            const originalText = assignButton.innerHTML;
            assignButton.disabled = true;
            assignButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Assigning...';
            
            // Create CSRF token for the request
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                             document.querySelector('input[name="_token"]')?.value;
            
            // Send assignment request to server
            fetch(`/lot-requests/${lotRequestId}/assign-lot`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    lot_id: lotId,
                    lot_code: lotCode
                })
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    // Update button to show assigned state immediately
                    assignButton.innerHTML = '<i class="fas fa-check"></i> Assigned';
                    assignButton.classList.remove('btn-primary');
                    assignButton.classList.add('btn-success');
                    assignButton.disabled = true;
                    
                    // Show success message with status update info
                    let message = `Success! Lot "${lotId}" has been assigned to this lot request.`;
                    if (data.lot_request_status === 'in_process') {
                        message += '\n\nThe request status has been automatically updated to "Getting Lot".';
                    }
                    alert(message);
                    
                    // Close the modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('wipDetailsModal'));
                    if (modal) {
                        modal.hide();
                    }
                    
                    // Refresh the page to show updated data and status
                    setTimeout(() => {
                        window.location.reload();
                    }, 1000);
                } else {
                    throw new Error(data.message || 'Assignment failed');
                }
            })
            .catch(error => {
                console.error('Assignment error:', error);
                alert(`Error assigning lot: ${error.message}`);
                
                // Re-enable button on error
                assignButton.disabled = false;
                assignButton.innerHTML = originalText;
            });
        }
        
        function printLotDetails(lotId, model, quantity, stagnation, location, autoYn, lipasYn) {
            // Create a new window for printing
            const printWindow = window.open('', '_blank', 'width=600,height=400');
            
            // Generate print content
            const printContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Lot Details - ${lotId}</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; }
                        .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; margin-bottom: 20px; }
                        .details-table { width: 100%; border-collapse: collapse; margin-top: 10px; }
                        .details-table th, .details-table td { padding: 8px; border: 1px solid #ddd; text-align: left; }
                        .details-table th { background-color: #f8f9fa; font-weight: bold; }
                        .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
                        @media print { 
                            body { margin: 0; } 
                            .no-print { display: none; }
                        }
                    </style>
                </head>
                <body>
                    <div class="header">
                        <h2>WIP Lot Details Report</h2>
                        <p>Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}</p>
                    </div>
                    
                    <table class="details-table">
                        <tr>
                            <th>Lot Number</th>
                            <td><strong>${lotId}</strong></td>
                        </tr>
                        <tr>
                            <th>Model</th>
                            <td>${model || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Quantity</th>
                            <td>${parseInt(quantity || 0).toLocaleString()} units</td>
                        </tr>
                        <tr>
                            <th>Stagnation TAT</th>
                            <td>${parseFloat(stagnation || 0).toFixed(2)} days</td>
                        </tr>
                        <tr>
                            <th>Location</th>
                            <td>${location || 'N/A'}</td>
                        </tr>
                        <tr>
                            <th>Automotive</th>
                            <td>${autoYn === 'Y' ? 'Yes' : 'No'}</td>
                        </tr>
                        <tr>
                            <th>Lipas</th>
                            <td>${lipasYn === 'Y' ? 'Yes' : 'No'}</td>
                        </tr>
                    </table>
                    
                    <div class="footer">
                        <p>WIP Management Dashboard - Process Dashboard System</p>
                    </div>
                    
                    <div class="no-print" style="margin-top: 20px; text-align: center;">
                        <button onclick="window.print()" style="padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">Print This Report</button>
                        <button onclick="window.close()" style="padding: 10px 20px; background: #6c757d; color: white; border: none; border-radius: 4px; cursor: pointer; margin-left: 10px;">Close Window</button>
                    </div>
                </body>
                </html>
            `;
            
            printWindow.document.write(printContent);
            printWindow.document.close();
            
            // Auto-print after a short delay to ensure content is loaded
            setTimeout(() => {
                printWindow.focus();
                printWindow.print();
            }, 250);
        }
    </script>
</x-app-layout>
