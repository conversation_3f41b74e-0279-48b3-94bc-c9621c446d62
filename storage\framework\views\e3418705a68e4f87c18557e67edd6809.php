<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        ENDTIME | SUBMITTED
     <?php $__env->endSlot(); ?>



    <!-- Success/Error Messages -->
    <?php if(session('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i><?php echo e(session('success')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i><?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i><strong>Validation Errors:</strong>
            <ul class="mb-0 mt-2">
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Merged Filter & Action Controls -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="merged-controls-wrapper">
                <!-- Unified Filter Card -->
                <div class="card border-0 shadow-sm unified-filter-card">
                    <div class="card-header bg-gradient-primary text-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <h6 class="mb-0 me-3">
                                    <i class="fas fa-filter me-2"></i>
                                    <span id="filter-toggle" onclick="toggleFilters()" style="cursor: pointer;">Filters & Controls</span>
                                </h6>
                                <!-- Quick Time Range Display -->
                                <div class="time-range-quick-display">
                                    <span class="badge bg-light text-primary px-3 py-2" id="cutoff_range_badge">Loading...</span>
                                </div>
                            </div>
                            <div class="d-flex align-items-center gap-3">
                                <!-- Summary Stats -->
                                <div class="filter-stats d-flex gap-2">
                                    <div class="stat-badge">
                                        <span class="badge bg-light text-primary px-2 py-1">
                                            <i class="fas fa-list-ol me-1"></i>
                                            <strong id="total-lots-count"><?php echo e($stats['total_filtered_lots'] ?? 0); ?></strong>
                                            <small>LOTS</small>
                                        </span>
                                    </div>
                                    <div class="stat-badge">
                                        <span class="badge bg-light text-success px-2 py-1">
                                            <i class="fas fa-cogs me-1"></i>
                                            <strong id="ideal-equipment-count"><?php echo e($stats['ideal_equipment_count'] ?? 0); ?></strong>
                                            <small>EQP</small>
                                        </span>
                                    </div>
                                </div>

                                <!-- Header Search -->
                                <div class="header-search-wrapper">
                                    <div class="search-input-wrapper">
                                        <i class="fas fa-search search-icon"></i>
                                        <input type="text" id="header_search" class="form-control form-control-sm header-search"
                                               placeholder="Search lots..."
                                               value="<?php echo e($filters['search'] ?? ''); ?>"
                                               oninput="document.getElementById('hidden_search').value = this.value;"
                                               onchange="document.getElementById('hidden_search').value = this.value;">
                                    </div>
                                </div>


                                <!-- Action Buttons -->
                                <div class="action-buttons-compact d-flex gap-2">
                                    <a href="<?php echo e(route('dashboard')); ?>" class="btn btn-warning btn-sm nav-button action-btn-labeled" title="Dashboard">
                                        <i class="fas fa-home me-1"></i>
                                        <span>Dashboard</span>
                                    </a>
                                    <a href="<?php echo e(route('endtime.create')); ?>" class="btn btn-primary btn-sm action-btn-labeled" title="Create New Lot">
                                        <i class="fas fa-plus-circle me-1"></i>
                                        <span>ADD ENDTIME Lot</span>
                                    </a>
                                    <a href="<?php echo e(route('endtime.submit.show')); ?>" class="btn btn-success btn-sm action-btn-labeled" title="Submit Ongoing Lot">
                                        <i class="fas fa-check-circle me-1"></i>
                                        <span>SUBMIT Lot</span>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Collapsible Filter Controls -->
                    <div id="filter-controls" class="collapse show">
                        <div class="card-body bg-light py-3">
                            <form method="GET" action="<?php echo e(route('endtime.index')); ?>" id="endtime-filters-form">
                                <!-- Ultra Compact Single Row Filter -->
                                <div class="single-row-filter">
                                    <!-- Production Schedule Group -->
                                    <div class="filter-group schedule-group">
                                        <div class="group-badge schedule-badge">
                                            <i class="fas fa-clock me-2"></i>
                                            <span>Schedule</span>
                                        </div>
                                        <div class="group-controls">
                                            <div class="mini-group">
                                                <label>Date</label>
                                                <input type="date" id="cutoff_date_picker" class="form-control form-control-sm mini-input"
                                                       value="<?php echo e(request('cutoff_date', date('Y-m-d'))); ?>"
                                                       onchange="updateCutoffFilter()">
                                            </div>
                                            <div class="mini-group">
                                                <label>Shift</label>
                                                <select id="shift_selector" class="form-select form-select-sm mini-input"
                                                        onchange="updateCutoffFilter()">
                                                    <option value="all" <?php echo e(request('cutoff_shift', 'day') === 'all' ? 'selected' : ''); ?>>All</option>
                                                    <option value="day" <?php echo e(request('cutoff_shift', 'day') === 'day' ? 'selected' : ''); ?>>Day</option>
                                                    <option value="night" <?php echo e(request('cutoff_shift', 'day') === 'night' ? 'selected' : ''); ?>>Night</option>
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Period</label>
                                                <select id="cutoff_period_selector" class="form-select form-select-sm mini-input"
                                                        onchange="updateCutoffFilter()">
                                                    <option value="1" <?php echo e(request('cutoff_period', '1') === '1' ? 'selected' : ''); ?>>1st</option>
                                                    <option value="2" <?php echo e(request('cutoff_period', '1') === '2' ? 'selected' : ''); ?>>2nd</option>
                                                    <option value="3" <?php echo e(request('cutoff_period', '1') === '3' ? 'selected' : ''); ?>>3rd</option>
                                                    <option value="all" <?php echo e(request('cutoff_period', '1') === 'all' ? 'selected' : ''); ?>>All</option>
                                                </select>
                                            </div>
                                            <div class="mini-group range-display">
                                                <label>Range</label>
                                                <div class="mini-range" id="shift_description">Day - 1st</div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Divider -->
                                    <div class="filter-divider"></div>

                                    <!-- Equipment & Status Group -->
                                    <div class="filter-group equipment-group">
                                        <div class="group-badge equipment-badge">
                                            <i class="fas fa-cogs me-2"></i>
                                            <span>Equipment</span>
                                        </div>
                                        <div class="group-controls">
                                            <div class="mini-group">
                                                <label>Line</label>
                                                <select name="eqp_line" id="eqp_line" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    <?php if(isset($filterOptions['equipment_lines'])): ?>
                                                        <?php $__currentLoopData = $filterOptions['equipment_lines']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $line): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($line); ?>" <?php echo e((isset($filters['eqp_line']) && $filters['eqp_line'] === $line) ? 'selected' : ''); ?>><?php echo e($line); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Area</label>
                                                <select name="eqp_area" id="eqp_area" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    <?php if(isset($filterOptions['equipment_areas'])): ?>
                                                        <?php $__currentLoopData = $filterOptions['equipment_areas']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $area): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($area); ?>" <?php echo e((isset($filters['eqp_area']) && $filters['eqp_area'] === $area) ? 'selected' : ''); ?>><?php echo e($area); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Work</label>
                                                <select name="work_type" id="work_type" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    <?php if(isset($filterOptions['work_types'])): ?>
                                                        <?php $__currentLoopData = $filterOptions['work_types']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($type); ?>" <?php echo e((isset($filters['work_type']) && $filters['work_type'] === $type) ? 'selected' : ''); ?>><?php echo e($type); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Lot</label>
                                                <select name="lot_type" id="lot_type" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    <?php if(isset($filterOptions['lot_types'])): ?>
                                                        <?php $__currentLoopData = $filterOptions['lot_types']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($type); ?>" <?php echo e((isset($filters['lot_type']) && $filters['lot_type'] === $type) ? 'selected' : ''); ?>><?php echo e($type); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                            <div class="mini-group">
                                                <label>Status</label>
                                                <select name="status" id="status" class="form-select form-select-sm mini-input">
                                                    <option value="all">All</option>
                                                    <?php if(isset($filterOptions['statuses'])): ?>
                                                        <?php $__currentLoopData = $filterOptions['statuses']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $status): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($status); ?>" <?php echo e((isset($filters['status']) && $filters['status'] === $status) ? 'selected' : ''); ?>><?php echo e($status); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    <?php endif; ?>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Filter Action Buttons -->
                                    <div class="filter-actions-inline">
                                        <button type="button" class="btn btn-primary btn-sm filter-btn" onclick="applyCutoffFilter()" title="Apply Filters">
                                            <i class="fas fa-search me-1"></i>Apply
                                        </button>
                                        <button type="button" class="btn btn-outline-warning btn-sm filter-btn" onclick="resetAllFilters()" title="Reset All">
                                            <i class="fas fa-undo me-1"></i>Reset
                                        </button>
                                    </div>
                                </div>

                                <!-- Hidden fields for cutoff parameters -->
                                <input type="hidden" name="cutoff_date" id="cutoff_date" value="<?php echo e(request('cutoff_date', date('Y-m-d'))); ?>">
                                <input type="hidden" name="cutoff_shift" id="cutoff_shift" value="<?php echo e(request('cutoff_shift', 'day')); ?>">
                                <input type="hidden" name="cutoff_period" id="cutoff_period" value="<?php echo e(request('cutoff_period', '1')); ?>">
                                <input type="hidden" name="search" id="hidden_search" value="<?php echo e($filters['search'] ?? ''); ?>">
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>




    <!-- Lots Table -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Forecasted End Times</h6>
        </div>
        <div class="card-body p-0">
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Lot ID</th>
                            <th>Model</th>
                            <th>Size/Qty</th>
                            <th>Work Type</th>
                            <th>Lot Type</th>
                            <th>Equipment</th>
                            <th>Line/Area</th>
                            <th>Est. End Time</th>
                            <th>Status</th>
                            <th>Result</th>
                            <th>LIPAS</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__empty_1 = true; $__currentLoopData = $endtimeLots; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $lot): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr class="<?php echo e($lot->est_endtime < now() ? 'table-danger' : ($lot->est_endtime < now()->addHours(2) ? 'table-warning' : '')); ?>">
                            <td>
                                <strong><?php echo e($lot->lot_id); ?></strong>
                            </td>
                            <td><?php echo e($lot->model_15); ?></td>
                            <td>
                                <span class="badge bg-secondary"><?php echo e($lot->lot_size); ?></span>
                                <br><small class="text-muted">Qty: <?php echo e(number_format($lot->lot_qty)); ?></small>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($lot->work_type === 'PROD' ? 'primary' : 'secondary'); ?>">
                                    <?php echo e($lot->work_type); ?>

                                </span>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($lot->lot_type === 'HOT' ? 'danger' : ($lot->lot_type === 'SUPER_HOT' ? 'warning' : 'info')); ?>">
                                    <?php echo e($lot->lot_type); ?>

                                </span>
                            </td>
                            <td>
                                <small class="text-muted">
                                    <?php echo e($lot->eqp_1); ?>

                                    <?php if($lot->eqp_2): ?>, <?php echo e($lot->eqp_2); ?><?php endif; ?>
                                    <?php if($lot->eqp_3): ?>, <?php echo e($lot->eqp_3); ?><?php endif; ?>
                                    <?php if($lot->eqp_4): ?>, <?php echo e($lot->eqp_4); ?><?php endif; ?>
                                    <?php if($lot->eqp_5): ?>, <?php echo e($lot->eqp_5); ?><?php endif; ?>
                                    <?php if($lot->eqp_6): ?>, <?php echo e($lot->eqp_6); ?><?php endif; ?>
                                    <?php if($lot->eqp_7): ?>, <?php echo e($lot->eqp_7); ?><?php endif; ?>
                                    <?php if($lot->eqp_8): ?>, <?php echo e($lot->eqp_8); ?><?php endif; ?>
                                    <?php if($lot->eqp_9): ?>, <?php echo e($lot->eqp_9); ?><?php endif; ?>
                                    <?php if($lot->eqp_10): ?>, <?php echo e($lot->eqp_10); ?><?php endif; ?>
                                </small>
                            </td>
                            <td>
                                <strong><?php echo e($lot->eqp_line); ?></strong>
                                <br><small class="text-muted"><?php echo e($lot->eqp_area); ?></small>
                            </td>
                            <td>
                                <div class="text-<?php echo e($lot->est_endtime < now() ? 'danger' : ($lot->est_endtime < now()->addHours(2) ? 'warning' : 'success')); ?>">
                                    <strong><?php echo e($lot->est_endtime->format('M d, H:i')); ?></strong>
                                    <br>
                                    <small><?php echo e($lot->est_endtime->diffForHumans()); ?></small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($lot->status === 'Submitted' ? 'success' :
                                    ($lot->status === 'Ongoing' ? 'primary' :
                                    ($lot->status === 'COMPLETED' ? 'info' :
                                    ($lot->status === 'MAINTENANCE' ? 'danger' : 'secondary')))); ?>">
                                    <?php echo e($lot->status); ?>

                                </span>
                                <?php if($lot->status === 'Submitted' && $lot->actual_submitted_at): ?>
                                    <br><small class="text-muted">Submitted: <?php echo e($lot->actual_submitted_at->format('M d, H:i')); ?></small>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if($lot->remarks): ?>
                                    <span class="badge bg-<?php echo e($lot->remarks === 'Early' ? 'success' :
                                        ($lot->remarks === 'Delayed' ? 'danger' : 'primary')); ?>">
                                        <?php echo e($lot->remarks); ?>

                                    </span>
                                    <?php if($lot->actual_submitted_at): ?>
                                        <br><small class="text-muted">Actual: <?php echo e($lot->actual_submitted_at->format('M d, H:i')); ?></small>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="text-muted">-</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <span class="badge bg-<?php echo e($lot->lipas_yn === 'Y' ? 'success' : 'secondary'); ?>">
                                    <?php echo e($lot->lipas_yn === 'Y' ? 'Yes' : 'No'); ?>

                                </span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <!-- View Details - Available to all authenticated users -->
                                    <button type="button" class="btn btn-sm btn-outline-info"
                                            onclick="viewLot(<?php echo e($lot->id); ?>)"
                                            title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </button>

                                    <!-- Edit/Modify - Available to all authenticated users (USER, MANAGER, ADMIN) -->
                                    <?php if(Auth::user()->isUser() || Auth::user()->isManager() || Auth::user()->isAdmin()): ?>
                                    <button type="button" class="btn btn-sm btn-outline-warning"
                                            onclick="editLot(<?php echo e($lot->id); ?>)"
                                            title="Edit/Modify">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <?php endif; ?>

                                    <!-- Delete - Available to Managers and Admins only -->
                                    <?php if(Auth::user()->isManager() || Auth::user()->isAdmin()): ?>
                                    <button type="button" class="btn btn-sm btn-outline-danger"
                                            onclick="deleteLot(<?php echo e($lot->id); ?>, '<?php echo e($lot->lot_id); ?>')"
                                            title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    <?php endif; ?>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="12" class="text-center py-4">
                                <i class="fas fa-clock fa-3x text-muted mb-3"></i>
                                <h6 class="text-muted">No forecasted end times found for the selected criteria</h6>
                            </td>
                        </tr>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
        </div>
        <?php if($endtimeLots->hasPages()): ?>
        <div class="card-footer bg-light">
            <?php echo e($endtimeLots->links()); ?>

        </div>
        <?php endif; ?>
    </div>


    <!-- JavaScript Integration -->
    <script>
        // Inject available equipment data for JavaScript consumption
        window.availableEquipment = <?php echo json_encode($availableEquipment ?? [], 15, 512) ?>;

        // Debug equipment data
        console.log('Equipment data injected:', {
            count: window.availableEquipment.length,
            sample: window.availableEquipment.slice(0, 2)
        });

        // Add global calculation function for manual testing
        window.triggerCalculation = function() {
            console.log('=== Manual Calculation Trigger ===');
            if (typeof calculateEndtime === 'function') {
                calculateEndtime();
            } else {
                console.error('calculateEndtime function not available');
            }
        };
    </script>

    <!-- Include Endtime JavaScript -->
    <script src="<?php echo e(asset('js/endtime.js')); ?>"></script>

    <!-- Additional JavaScript for specific functions not in main file -->
    <script>
        // Functions that need to be global for inline handlers

        /**
         * View lot details
         */
        function viewLot(lotId) {
            // Implementation for viewing lot details
            console.log('View lot:', lotId);
            // This could redirect to a detail page
        }

        /**
         * Edit lot
         */
        function editLot(lotId) {
            // Implementation for editing lot
            console.log('Edit lot:', lotId);
            // This could redirect to edit page
        }

        /**
         * Delete lot
         */
        function deleteLot(lotId, lotIdString) {
            if (confirm(`Are you sure you want to delete lot ${lotIdString}?`)) {
                // Implementation for deleting lot
                console.log('Delete lot:', lotId);
                // Make AJAX call to delete endpoint
                fetch(`/endtime/${lotId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        setTimeout(() => window.location.reload(), 1000);
                    } else {
                        showToast(data.message || 'Failed to delete lot', 'error');
                    }
                })
                .catch(error => {
                    console.error('Delete error:', error);
                    showToast('Failed to delete lot', 'error');
                });
            }
        }

        /**
         * Filter and utility functions from existing inline scripts
         */

        // Initialize existing filter functions if they don't exist
        if (typeof toggleFilters === 'undefined') {
            function toggleFilters() {
                const filterControls = document.getElementById('filter-controls');
                if (filterControls) {
                    if (filterControls.classList.contains('show')) {
                        filterControls.classList.remove('show');
                    } else {
                        filterControls.classList.add('show');
                    }
                }
            }
        }

        if (typeof updateCutoffFilter === 'undefined') {
            function updateCutoffFilter() {
                // Update cutoff filter display
                const date = document.getElementById('cutoff_date_picker')?.value;
                const shift = document.getElementById('shift_selector')?.value;
                const period = document.getElementById('cutoff_period_selector')?.value;

                // Update hidden fields
                if (document.getElementById('cutoff_date')) document.getElementById('cutoff_date').value = date;
                if (document.getElementById('cutoff_shift')) document.getElementById('cutoff_shift').value = shift;
                if (document.getElementById('cutoff_period')) document.getElementById('cutoff_period').value = period;

                // Update range display
                updateShiftDescription();
            }
        }

        if (typeof updateShiftDescription === 'undefined') {
            function updateShiftDescription() {
                const shift = document.getElementById('shift_selector')?.value || 'day';
                const period = document.getElementById('cutoff_period_selector')?.value || '1';
                const shiftDesc = document.getElementById('shift_description');

                if (shiftDesc) {
                    let description = '';
                    if (shift === 'all') {
                        description = 'All Shifts';
                    } else if (shift === 'day') {
                        switch(period) {
                            case '1': description = 'Day - 1st (7AM-12PM)'; break;
                            case '2': description = 'Day - 2nd (12PM-4PM)'; break;
                            case '3': description = 'Day - 3rd (4PM-7PM)'; break;
                            case 'all': description = 'Day - All (7AM-7PM)'; break;
                        }
                    } else if (shift === 'night') {
                        switch(period) {
                            case '1': description = 'Night - 1st (7PM-12AM)'; break;
                            case '2': description = 'Night - 2nd (12AM-4AM)'; break;
                            case '3': description = 'Night - 3rd (4AM-7AM)'; break;
                            case 'all': description = 'Night - All (7PM-7AM)'; break;
                        }
                    }
                    shiftDesc.textContent = description;
                }
            }
        }

        if (typeof applyCutoffFilter === 'undefined') {
            function applyCutoffFilter() {
                updateCutoffFilter();
                document.getElementById('endtime-filters-form')?.submit();
            }
        }

        if (typeof resetAllFilters === 'undefined') {
            function resetAllFilters() {
                // Reset all filter inputs to defaults
                const form = document.getElementById('endtime-filters-form');
                if (form) {
                    form.reset();
                    // Set default values
                    const today = new Date().toISOString().split('T')[0];
                    if (document.getElementById('cutoff_date_picker')) document.getElementById('cutoff_date_picker').value = today;
                    if (document.getElementById('shift_selector')) document.getElementById('shift_selector').value = 'day';
                    if (document.getElementById('cutoff_period_selector')) document.getElementById('cutoff_period_selector').value = '1';

                    updateCutoffFilter();
                    form.submit();
                }
            }
        }

        // Initialize page when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize shift description
            updateShiftDescription();

            // Update cutoff range badge
            updateCutoffRangeBadge();
        });

        function updateCutoffRangeBadge() {
            const badge = document.getElementById('cutoff_range_badge');
            if (badge) {
                const date = document.getElementById('cutoff_date_picker')?.value || new Date().toISOString().split('T')[0];
                const shift = document.getElementById('shift_selector')?.value || 'day';
                const period = document.getElementById('cutoff_period_selector')?.value || '1';

                let badgeText = '';
                const dateObj = new Date(date);
                const formattedDate = dateObj.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });

                if (shift === 'all') {
                    badgeText = `${formattedDate} - All Shifts`;
                } else {
                    const shiftText = shift.charAt(0).toUpperCase() + shift.slice(1);
                    const periodText = period === 'all' ? 'All' : `${period}${period === '1' ? 'st' : period === '2' ? 'nd' : 'rd'}`;
                    badgeText = `${formattedDate} - ${shiftText} ${periodText}`;
                }

                badge.textContent = badgeText;
            }
        }
    </script>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\Project\process-dashboard\resources\views/endtime/index.blade.php ENDPATH**/ ?>