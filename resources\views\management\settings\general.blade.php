<x-app-layout>
    <x-slot name="header">
        General Settings
    </x-slot>

    <!-- Breadcrumb -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('management.settings.index') }}">Settings</a></li>
                    <li class="breadcrumb-item active" aria-current="page">General</li>
                </ol>
            </nav>
        </div>
    </div>

    @if(session('success'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('success') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    @if($errors->any())
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <ul class="mb-0">
                @foreach($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h5 class="mb-0">
                        <i class="fas fa-cog me-2"></i>General Application Settings
                    </h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('management.settings.update.general') }}" method="POST">
                        @csrf
                        @method('PUT')

                        <div class="row g-4">
                            <!-- Application Information -->
                            <div class="col-12">
                                <h6 class="text-muted border-bottom pb-2">Application Information</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="app_name" class="form-label">Application Name <span class="text-danger">*</span></label>
                                <input type="text" 
                                       class="form-control @error('app_name') is-invalid @enderror" 
                                       id="app_name" 
                                       name="app_name" 
                                       value="{{ old('app_name', $settings['app_name']) }}" 
                                       required>
                                @error('app_name')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="contact_email" class="form-label">Contact Email <span class="text-danger">*</span></label>
                                <input type="email" 
                                       class="form-control @error('contact_email') is-invalid @enderror" 
                                       id="contact_email" 
                                       name="contact_email" 
                                       value="{{ old('contact_email', $settings['contact_email']) }}" 
                                       required>
                                @error('contact_email')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-12">
                                <label for="app_description" class="form-label">Application Description</label>
                                <textarea class="form-control @error('app_description') is-invalid @enderror" 
                                          id="app_description" 
                                          name="app_description" 
                                          rows="3" 
                                          placeholder="Brief description of your application">{{ old('app_description', $settings['app_description']) }}</textarea>
                                @error('app_description')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Localization Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Localization Settings</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="timezone" class="form-label">Timezone <span class="text-danger">*</span></label>
                                <select class="form-select @error('timezone') is-invalid @enderror" 
                                        id="timezone" 
                                        name="timezone" 
                                        required>
                                    <option value="">Select Timezone</option>
                                    @php
                                        $timezones = [
                                            'UTC' => 'UTC',
                                            'America/New_York' => 'Eastern Time (ET)',
                                            'America/Chicago' => 'Central Time (CT)',
                                            'America/Denver' => 'Mountain Time (MT)',
                                            'America/Los_Angeles' => 'Pacific Time (PT)',
                                            'Europe/London' => 'London (GMT)',
                                            'Europe/Paris' => 'Paris (CET)',
                                            'Europe/Berlin' => 'Berlin (CET)',
                                            'Asia/Tokyo' => 'Tokyo (JST)',
                                            'Asia/Shanghai' => 'Shanghai (CST)',
                                            'Asia/Kolkata' => 'India (IST)',
                                            'Australia/Sydney' => 'Sydney (AEST)',
                                        ];
                                    @endphp
                                    @foreach($timezones as $value => $label)
                                        <option value="{{ $value }}" {{ old('timezone', $settings['timezone']) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('timezone')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="date_format" class="form-label">Date Format <span class="text-danger">*</span></label>
                                <select class="form-select @error('date_format') is-invalid @enderror" 
                                        id="date_format" 
                                        name="date_format" 
                                        required>
                                    <option value="">Select Date Format</option>
                                    @php
                                        $dateFormats = [
                                            'Y-m-d' => date('Y-m-d') . ' (YYYY-MM-DD)',
                                            'm/d/Y' => date('m/d/Y') . ' (MM/DD/YYYY)',
                                            'd/m/Y' => date('d/m/Y') . ' (DD/MM/YYYY)',
                                            'M j, Y' => date('M j, Y') . ' (Mon DD, YYYY)',
                                            'F j, Y' => date('F j, Y') . ' (Month DD, YYYY)',
                                        ];
                                    @endphp
                                    @foreach($dateFormats as $value => $label)
                                        <option value="{{ $value }}" {{ old('date_format', $settings['date_format']) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('date_format')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Display Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="text-muted border-bottom pb-2">Display Settings</h6>
                            </div>

                            <div class="col-md-6">
                                <label for="currency" class="form-label">Currency <span class="text-danger">*</span></label>
                                <select class="form-select @error('currency') is-invalid @enderror" 
                                        id="currency" 
                                        name="currency" 
                                        required>
                                    <option value="">Select Currency</option>
                                    @php
                                        $currencies = [
                                            'USD' => 'USD - US Dollar',
                                            'EUR' => 'EUR - Euro',
                                            'GBP' => 'GBP - British Pound',
                                            'JPY' => 'JPY - Japanese Yen',
                                            'CAD' => 'CAD - Canadian Dollar',
                                            'AUD' => 'AUD - Australian Dollar',
                                            'CHF' => 'CHF - Swiss Franc',
                                            'CNY' => 'CNY - Chinese Yuan',
                                            'INR' => 'INR - Indian Rupee',
                                        ];
                                    @endphp
                                    @foreach($currencies as $value => $label)
                                        <option value="{{ $value }}" {{ old('currency', $settings['currency']) == $value ? 'selected' : '' }}>
                                            {{ $label }}
                                        </option>
                                    @endforeach
                                </select>
                                @error('currency')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <div class="col-md-6">
                                <label for="items_per_page" class="form-label">Items Per Page <span class="text-danger">*</span></label>
                                <select class="form-select @error('items_per_page') is-invalid @enderror" 
                                        id="items_per_page" 
                                        name="items_per_page" 
                                        required>
                                    <option value="">Select Items Per Page</option>
                                    @foreach([10, 15, 20, 25, 50, 100] as $value)
                                        <option value="{{ $value }}" {{ old('items_per_page', $settings['items_per_page']) == $value ? 'selected' : '' }}>
                                            {{ $value }} items
                                        </option>
                                    @endforeach
                                </select>
                                @error('items_per_page')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>

                            <!-- Submit Button -->
                            <div class="col-12 mt-4">
                                <hr>
                                <div class="d-flex justify-content-between">
                                    <a href="{{ route('management.settings.index') }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Back to Settings
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Save Changes
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Current Settings</h6>
                </div>
                <div class="card-body">
                    <div class="setting-preview">
                        <div class="mb-3">
                            <label class="form-label text-muted small">Application Name</label>
                            <p class="mb-0 fw-semibold">{{ $settings['app_name'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Contact Email</label>
                            <p class="mb-0">{{ $settings['contact_email'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Timezone</label>
                            <p class="mb-0">{{ $settings['timezone'] }}</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Date Format</label>
                            <p class="mb-0">{{ $settings['date_format'] }} 
                                <small class="text-muted">({{ date($settings['date_format']) }})</small>
                            </p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label text-muted small">Currency</label>
                            <p class="mb-0">{{ $settings['currency'] }}</p>
                        </div>
                        <div class="mb-0">
                            <label class="form-label text-muted small">Items Per Page</label>
                            <p class="mb-0">{{ $settings['items_per_page'] }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="card border-0 shadow-sm mt-4">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Help & Tips</h6>
                </div>
                <div class="card-body">
                    <div class="help-tips">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Tip:</strong> Changes to general settings will take effect immediately for all users.
                        </div>
                        <ul class="list-unstyled small">
                            <li><i class="fas fa-check text-success me-2"></i>Application name appears in the browser title</li>
                            <li><i class="fas fa-check text-success me-2"></i>Contact email is used for system notifications</li>
                            <li><i class="fas fa-check text-success me-2"></i>Timezone affects all date/time displays</li>
                            <li><i class="fas fa-check text-success me-2"></i>Currency format is used throughout the application</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>