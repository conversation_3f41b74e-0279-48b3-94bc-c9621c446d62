<x-app-layout>
    <x-slot name="header">
        Reports Dashboard
    </x-slot>

    <!-- <PERSON> Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h4 class="mb-0">Business Reports & Analytics</h4>
                <div class="text-muted">
                    <i class="fas fa-chart-line me-2"></i>
                    Generate comprehensive business insights
                </div>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="row g-4">
        <!-- Sales Reports -->
        <div class="col-xl-4 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="bg-primary bg-opacity-10 text-primary rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-chart-line fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-2">Sales Reports</h5>
                    <p class="text-muted mb-4">Analyze sales performance, revenue trends, and top-selling products over time.</p>
                    
                    <div class="mb-3">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="text-primary fw-bold">{{ \App\Models\Order::where('status', '!=', 'cancelled')->count() }}</div>
                                <small class="text-muted">Total Orders</small>
                            </div>
                            <div class="col-4">
                                <div class="text-success fw-bold">${{ number_format(\App\Models\Order::where('status', '!=', 'cancelled')->sum('total_amount'), 0) }}</div>
                                <small class="text-muted">Revenue</small>
                            </div>
                            <div class="col-4">
                                <div class="text-info fw-bold">{{ \App\Models\Order::whereDate('created_at', today())->count() }}</div>
                                <small class="text-muted">Today</small>
                            </div>
                        </div>
                    </div>
                    
                    <a href="{{ route('reports.sales') }}" class="btn btn-primary">
                        <i class="fas fa-chart-bar me-2"></i>View Sales Reports
                    </a>
                </div>
            </div>
        </div>

        <!-- Inventory Reports -->
        <div class="col-xl-4 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="mb-3">
                        <div class="bg-warning bg-opacity-10 text-warning rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                            <i class="fas fa-boxes fa-2x"></i>
                        </div>
                    </div>
                    <h5 class="fw-bold mb-2">Inventory Reports</h5>
                    <p class="text-muted mb-4">Track stock levels, identify low inventory, and manage product categories.</p>
                    
                    <div class="mb-3">
                        <div class="row text-center">
                            <div class="col-4">
                                <div class="text-primary fw-bold">{{ \App\Models\Product::where('status', 'active')->count() }}</div>
                                <small class="text-muted">Products</small>
                            </div>
                            <div class="col-4">
                                <div class="text-danger fw-bold">{{ \App\Models\Product::where('stock', '<', 10)->count() }}</div>
                                <small class="text-muted">Low Stock</small>
                            </div>
                            <div class="col-4">
                                <div class="text-warning fw-bold">{{ \App\Models\Product::where('stock', 0)->count() }}</div>
                                <small class="text-muted">Out of Stock</small>
                            </div>
                        </div>
                    </div>
                    
                    <a href="{{ route('reports.inventory') }}" class="btn btn-warning">
                        <i class="fas fa-warehouse me-2"></i>View Inventory Reports
                    </a>
                </div>
            </div>
        </div>

        <!-- User Analytics -->
        @if(Auth::user()->isAdmin())
            <div class="col-xl-4 col-md-6">
                <div class="card border-0 shadow-sm h-100">
                    <div class="card-body text-center p-4">
                        <div class="mb-3">
                            <div class="bg-success bg-opacity-10 text-success rounded-circle d-inline-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                                <i class="fas fa-users fa-2x"></i>
                            </div>
                        </div>
                        <h5 class="fw-bold mb-2">User Analytics</h5>
                        <p class="text-muted mb-4">Monitor user activity, registration trends, and customer behavior patterns.</p>
                        
                        <div class="mb-3">
                            <div class="row text-center">
                                <div class="col-4">
                                    <div class="text-primary fw-bold">{{ \App\Models\User::count() }}</div>
                                    <small class="text-muted">Total Users</small>
                                </div>
                                <div class="col-4">
                                    <div class="text-success fw-bold">{{ \App\Models\User::whereDate('created_at', today())->count() }}</div>
                                    <small class="text-muted">New Today</small>
                                </div>
                                <div class="col-4">
                                    <div class="text-info fw-bold">{{ \App\Models\User::where('role', 'admin')->count() }}</div>
                                    <small class="text-muted">Admins</small>
                                </div>
                            </div>
                        </div>
                        
                        <a href="{{ route('reports.users') }}" class="btn btn-success">
                            <i class="fas fa-chart-pie me-2"></i>View User Analytics
                        </a>
                    </div>
                </div>
            </div>
        @endif
    </div>

    <!-- Quick Actions -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('reports.export.sales') }}?start_date={{ now()->startOfMonth()->format('Y-m-d') }}&end_date={{ now()->endOfMonth()->format('Y-m-d') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-download me-2"></i>Export Monthly Sales
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('reports.export.inventory') }}" class="btn btn-outline-warning w-100">
                                <i class="fas fa-download me-2"></i>Export Inventory
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('reports.inventory') }}?low_stock=1" class="btn btn-outline-danger w-100">
                                <i class="fas fa-exclamation-triangle me-2"></i>Low Stock Alert
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('reports.sales') }}?period=daily&start_date={{ now()->startOfWeek()->format('Y-m-d') }}&end_date={{ now()->endOfWeek()->format('Y-m-d') }}" class="btn btn-outline-info w-100">
                                <i class="fas fa-calendar-week me-2"></i>This Week's Sales
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Summary -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-bottom">
                    <h6 class="mb-0">System Overview</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="badge bg-primary fs-6">{{ \App\Models\Order::whereDate('created_at', today())->count() }}</span>
                            </div>
                            <small class="text-muted">Orders Today</small>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="badge bg-success fs-6">${{ number_format(\App\Models\Order::whereDate('created_at', today())->where('status', '!=', 'cancelled')->sum('total_amount'), 0) }}</span>
                            </div>
                            <small class="text-muted">Revenue Today</small>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="badge bg-info fs-6">{{ \App\Models\Product::where('status', 'active')->count() }}</span>
                            </div>
                            <small class="text-muted">Active Products</small>
                        </div>
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="badge bg-warning fs-6">{{ \App\Models\Product::where('stock', '<', 10)->where('stock', '>', 0)->count() }}</span>
                            </div>
                            <small class="text-muted">Low Stock Items</small>
                        </div>
                        @if(Auth::user()->isAdmin())
                            <div class="col-md-2">
                                <div class="mb-2">
                                    <span class="badge bg-secondary fs-6">{{ \App\Models\User::whereDate('created_at', today())->count() }}</span>
                                </div>
                                <small class="text-muted">New Users</small>
                            </div>
                        @endif
                        <div class="col-md-2">
                            <div class="mb-2">
                                <span class="badge bg-dark fs-6">{{ \App\Models\Order::where('status', 'pending')->count() }}</span>
                            </div>
                            <small class="text-muted">Pending Orders</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>